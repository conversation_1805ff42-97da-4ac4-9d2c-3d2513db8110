apiVersion: apps/v1
kind: Deployment
metadata:
  name: fwcc
spec:
  template:
    spec:
      containers:
      - name: fwcc
        env:
        - name: APP_PROPERTIES
          value: config-prod.yaml
        volumeMounts:
          - mountPath: /usr/src/app/config-prod.yaml
            name: fwcc-config
            subPath: config-prod.yaml
          - mountPath: /etc/cron.d/fwcc-cron
            name: fwcc-cron
            subPath: fwcc-cron
      volumes:
      - name: fwcc-config
        secret:
          secretName: fwcc-config
      - name: fwcc-cron
        secret:
          secretName: fwcc-cron