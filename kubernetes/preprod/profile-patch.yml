apiVersion: apps/v1
kind: Deployment
metadata:
  name: fwcc
spec:
  template:
    spec:
      containers:
      - name: fwcc
        env:
        - name: APP_PROPERTIES
          value: config-preprod.yaml
        volumeMounts:
          - mountPath: /usr/src/app/config-preprod.yaml
            name: fwcc-config
            subPath: config-preprod.yaml
          - mountPath: /etc/cron.d/fwcc-cron
            name: fwcc-cron
            subPath: fwcc-cron
      volumes:
      - name: fwcc-config
        secret:
          secretName: fwcc-config
      - name: fwcc-cron
        secret:
          secretName: fwcc-cron
