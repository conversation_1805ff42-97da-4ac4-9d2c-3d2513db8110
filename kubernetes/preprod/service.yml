apiVersion: apps/v1
kind: Deployment
metadata:
  name: iad-product
  namespace: yolo-service
spec:
  selector:
    matchLabels:
      app: iad-product
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      name: iad-product
      labels:
        app: iad-product
    spec:
      automountServiceAccountToken: false
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - iad-product
                topologyKey: kubernetes.io/hostname
      containers:
      - name: iad-product
        image: KUBERNETES_IMAGE:IMAGE_TAG
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /q/health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /q/health/ready
            port: http
          initialDelaySeconds: 25
          periodSeconds: 20
          timeoutSeconds: 15
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 50m
            memory: 50Mi
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9000
          name: grpc
        volumeMounts:
          - name: iad-product-config
            mountPath: /deployments/app/application.properties
            subPath: application.properties
          - name: private-key-encrypt
            mountPath: /deployments/app/privateKeyEncrypt.pem
            subPath: privateKeyEncrypt.pem
          - name: public-key
            mountPath: /deployments/app/publicKey.pem
            subPath: publicKey.pem
      volumes:
        - name: iad-product-config
          secret:
            secretName: iad-product-config
        - name: private-key-encrypt
          secret:
            secretName: private-key-encrypt
        - name: public-key
          secret:
            secretName: public-key
---
apiVersion: v1
kind: Service
metadata:
  name: iad-product
  namespace: yolo-service
  labels:
    app: iad-product
spec:
  ports:
  - port: 8080
    targetPort: http
    name: http
  - port: 9000
    targetPort: 9000
    name: grpc
  selector:
    app: iad-product
