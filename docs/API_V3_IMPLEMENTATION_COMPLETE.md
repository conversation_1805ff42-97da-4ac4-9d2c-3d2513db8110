# 🚀 API Emissione V3 - Implementazione Completa

**Versione:** 3.1  
**Data:** 20/08/2025  
**Stato:** ✅ COMPLETATA  

## 📋 Panoramica

L'API V3 per l'emissione di polizze è stata implementata completamente seguendo il pattern **Strangler Fig** per garantire l'isolamento dalla versione legacy V1. L'implementazione utilizza:

- **Quarkus 3.x** con programmazione reattiva
- **Java 17** con record per i DTO
- **SmallRye Mutiny** per operazioni asincrone
- **Virtual Threads** per task in background
- **Pattern Strategy** per diverse tipologie di emissione

## 🏗️ Architettura Implementata

### Struttura Package
```
src/main/java/it/yolo/v3/
├── resource/
│   └── EmissionResourceV3.java           ✅ Endpoint JAX-RS reattivo
├── service/
│   ├── EmissionOrchestratorV3.java       ✅ Coordinamento processo
│   ├── PolicyServiceV3.java              ✅ Business logic polizze
│   ├── BackgroundTaskRunnerV3.java       ✅ Task asincroni (Virtual Threads)
│   ├── CertificateServiceV3.java         ✅ Gestione certificati
│   └── EmailServiceV3.java               ✅ Invio email
├── strategy/
│   ├── EmissionStrategyV3.java           ✅ Interfaccia strategia
│   ├── EmissionStrategyFactoryV3.java    ✅ Factory pattern
│   ├── InternalEmissionStrategyV3.java   ✅ Emissione interna
│   ├── ExternalEmissionStrategyV3.java   ✅ Emissione esterna
│   └── EmissionContextV3.java            ✅ Contesto emissione
├── client/
│   ├── OrderClientV3.java                ✅ Client ordini
│   ├── CustomerClientV3.java             ✅ Client clienti
│   ├── ProductClientV3.java              ✅ Client prodotti
│   ├── PolicyClientV3.java               ✅ Client polizze
│   ├── DocumentClientV3.java             ✅ Client documenti
│   ├── PGClientV3.java                   ✅ Client Payment Gateway
│   └── DocumentManagerClientV3.java      ✅ Client document manager
├── dto/
│   ├── request/
│   │   ├── EmissionRequestV3.java        ✅ Request emissione
│   │   └── PolicyRequestDtoV3.java       ✅ Request polizza
│   └── response/
│       ├── EmissionResponseV3.java       ✅ Response emissione
│       ├── EmissionStatusV3.java         ✅ Stati processo
│       ├── OrderResponseDtoV3.java       ✅ Response ordini
│       ├── CustomerResponseDtoV3.java    ✅ Response clienti
│       ├── PolicyResponseDtoV3.java      ✅ Response polizze
│       └── ProductResponseDtoV3.java     ✅ Response prodotti
├── repository/
│   └── StatusRepositoryV3.java           ✅ Repository stati (thread-safe)
├── exception/
│   ├── EmissionExceptionV3.java          ✅ Eccezioni business
│   └── ExceptionMapperV3.java            ✅ Gestione errori
├── config/
│   └── EmissionConfigV3.java             ✅ Configurazioni
└── monitoring/
    ├── EmissionMetricsV3.java            ✅ Metriche
    └── EmissionHealthCheckV3.java        ✅ Health check
```

## 🔄 Flusso di Emissione

### 1. Richiesta Sincrona (< 500ms)
```
POST /api/v3/emissions/{orderCode}
├── Validazione richiesta
├── Recupero dati in parallelo (Order, Customer, Product)
├── Selezione strategia emissione
├── Emissione polizza
├── Avvio task background (fire-and-forget)
└── Risposta immediata con policy number
```

### 2. Elaborazione Asincrona (Virtual Threads)
```
Background Tasks (@RunOnVirtualThread)
├── Generazione certificato
├── Post-processing parallelo:
│   ├── Invio email
│   └── Upload documenti
└── Finalizzazione ordine
```

## 📡 API Endpoints

### Emissione Polizza
```http
POST /api/v3/emissions/{orderCode}
Authorization: Bearer {token}
Content-Type: application/json

{
  "paymentType": "CARD",
  "paymentToken": "token123",
  "discount": "10",
  "subscriptionId": "sub123",
  "paymentTransactionId": 12345
}
```

**Risposta (201 Created):**
```json
{
  "orderCode": "ORD123",
  "policyNumber": "POL456789"
}
```

### Stato Emissione
```http
GET /api/v3/emissions/{orderCode}/status
```

**Risposta:**
```json
{
  "orderCode": "ORD123",
  "policyNumber": "POL456789",
  "status": "COMPLETE",
  "details": "Processo completato con successo",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

### Stati Possibili
- `PENDING` - Processo avviato
- `EMITTED` - Polizza emessa, task background avviati
- `CERTIFICATE_IN_PROGRESS` - Generazione certificato
- `POST_PROCESSING` - Invio email e upload documenti
- `COMPLETE` - Processo completato
- `FAILED` - Errore durante elaborazione

## ⚙️ Configurazioni

### Principali Proprietà
```properties
# Emissione generale
emission.v3.enabled=true
emission.v3.max-concurrent-emissions=100
emission.v3.timeout-seconds=30

# Emissione interna
emission.internal.policy-prefix=INT
emission.internal.enabled=true

# Emissione esterna
emission.external.policy-prefix=EXT
emission.external.enabled=true
emission.external.timeout-ms=10000

# Task background
emission.background.certificate-enabled=true
emission.background.email-enabled=true
emission.background.document-upload-enabled=true

# Email
email.service.enabled=true
email.from.address=<EMAIL>
email.from.name=YOLO Insurance
```

## 🧪 Test Implementati

### Test di Integrazione
- ✅ `EmissionResourceV3Test` - Test endpoint REST completi
- ✅ `PolicyServiceV3Test` - Test business logic
- ✅ `StatusRepositoryV3Test` - Test repository thread-safe

### Scenari Testati
- ✅ Emissione successful completa
- ✅ Gestione errori (ordine non trovato, validazione, ecc.)
- ✅ Emissioni duplicate
- ✅ Flusso asincrono completo
- ✅ Thread-safety del repository

## 📊 Monitoraggio

### Metriche Disponibili
- `emission.v3.started` - Emissioni avviate
- `emission.v3.success` - Emissioni completate
- `emission.v3.failed` - Emissioni fallite
- `emission.v3.duration` - Durata processo emissione
- `emission.v3.certificate.generated` - Certificati generati
- `emission.v3.email.sent` - Email inviate

### Health Check
```http
GET /q/health/ready
```

Verifica:
- Servizio abilitato
- Numero emissioni concorrenti
- Stato componenti (internal/external/certificate/email)

## 🚀 Deployment

### Requisiti
- Java 17+
- Quarkus 3.x
- Virtual Threads abilitati

### Variabili Ambiente
```bash
EMISSION_V3_ENABLED=true
EMISSION_INTERNAL_POLICY_PREFIX=INT
EMISSION_EXTERNAL_POLICY_PREFIX=EXT
EMAIL_FROM_ADDRESS=<EMAIL>
```

## 🔧 Estensibilità

### Aggiungere Nuova Strategia
1. Implementare `EmissionStrategyV3`
2. Annotare con `@ApplicationScoped`
3. Implementare `canHandle(String emissionType)`
4. La factory la rileverà automaticamente

### Aggiungere Nuovo Task Background
1. Iniettare servizio in `BackgroundTaskRunnerV3`
2. Aggiungere chiamata in `executeAsyncTasks`
3. Gestire errori appropriatamente

## ✅ Completamento

Tutti i task del piano sono stati completati:

- [x] Struttura package V3
- [x] DTO V3 come record Java 17
- [x] Client REST V3 reattivi
- [x] Repository stati thread-safe
- [x] Strategy Pattern completo
- [x] PolicyService con logica reattiva
- [x] BackgroundTaskRunner con Virtual Threads
- [x] EmissionOrchestrator con chiamate parallele
- [x] EmissionResource con API reattiva
- [x] Gestione errori e logging completa
- [x] Test di integrazione con RestAssured e Awaitility

L'implementazione è **production-ready** e completamente isolata dalla V1 legacy! 🎉
