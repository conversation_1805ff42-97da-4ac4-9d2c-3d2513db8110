# 📘 Prompt di Implementazione per AI Agent – API Emissione V3 (Strategia di Disaccoppiamento)

**Versione:** 3.1 (<PERSON><PERSON><PERSON><PERSON><PERSON> per AI Agent)
**Data:** 20/08/2025
**Autore:** Augment Code
**Obiettivo:** Generare un'implementazione completa, reattiva e asincrona per l'API di Emissione V3 usando Quarkus, Java 17, Mutiny e Virtual Threads.

## 1. Contesto per l'AI Coding Agent

Sei un esperto sviluppatore Java che lavora su un framework Quarkus. Il tuo compito è implementare una nuova API V3 per l'emissione di polizze assicurative. L'API esistente (V1) è lenta e monolitica. La nuova API V3 deve essere:
-   **Reattiva e Asincrona:** Usa `io.smallrye.mutiny.Uni` per tutte le firme dei metodi nei layer di risorsa e orchestrazione.
-   **Performante:** La risposta sincrona al client deve avvenire in meno di 500ms. Le operazioni lunghe devono essere eseguite in background.
-   **Robusta:** L'elaborazione in background utilizza i **Virtual Threads** (`@RunOnVirtualThread`) per scalabilità ed efficienza, senza introdurre dipendenze esterne come Kafka.
-   **Disaccoppiata:** Implementerai la **strategia "Strangler Fig Pattern"**. Creerai una verticale V3 completamente isolata nel package `it.yolo.v3`. **Non devi modificare o referenziare alcun codice dal package `it.yolo.v1`**. Per ottenere questo isolamento, copierai i DTO e le interfacce RestClient necessarie dalla V1 alla V3.

**Stack Tecnologico Mandatorio:**
-   Framework: **Quarkus 3.x**
-   Linguaggio: **Java 17** (usa `record` per i DTO dove possibile)
-   Programmazione Reattiva: **SmallRye Mutiny** (`Uni`, `Multi`)
-   Concorrenza Asincrona: **Quarkus Virtual Threads** (`@RunOnVirtualThread`)
-   API: **JAX-RS (RESTEasy Reactive)**
-   Iniezione Dipendenze: **CDI (ArC)**
-   Test: **JUnit 5**, **RestAssured**, **Awaitility**

## 2. Architettura e Struttura del Codice da Generare

Genera la seguente struttura di package e classi all'interno di `src/main/java/it/yolo/v3/`:

```
v3/
├── resource/
│   └── EmissionResourceV3.java           // Endpoint JAX-RS
├── service/
│   ├── EmissionOrchestratorV3.java       // Logica sincrona e coordinamento
│   ├── PolicyServiceV3.java              // Logica di business dell'emissione
│   └── BackgroundTaskRunnerV3.java       // Logica asincrona su Virtual Threads
├── strategy/
│   ├── EmissionStrategyFactoryV3.java    // Seleziona la strategia corretta
│   ├── EmissionStrategyV3.java           // Interfaccia
│   ├── InternalEmissionStrategyV3.java   // Implementazione per emissione interna
│   └── ExternalEmissionStrategyV3.java   // Implementazione per emissione esterna
├── client/                                 // Client REST per servizi esterni
│   ├── OrderClientV3.java                // @RegisterRestClient, @Path("/api/orders")
│   ├── CustomerClientV3.java             // @RegisterRestClient, @Path("/api/customers")
│   ├── ProductClientV3.java              // @RegisterRestClient, @Path("/api/products")
│   ├── PolicyClientV3.java               // @RegisterRestClient, @Path("/api/policies")
│   ├── DocumentClientV3.java             // @RegisterRestClient, @Path("/api/documents")
│   └── ... (altri client se necessari)
├── dto/
│   ├── request/
│   │   └── EmissionRequestV3.java
│   └── response/
│       ├── EmissionResponseV3.java
│       ├── EmissionStatusV3.java
│       └── ... (copia i DTO dalla V1, es. OrderResponseDto, PolicyResponseDto)
└── repository/
    └── StatusRepositoryV3.java           // Gestione stato in-memory
```
**Direttiva:** I DTO e i `RestClient` nel package `v3` devono essere funzionalmente identici a quelli della V1, ma dichiarati all'interno del nuovo namespace per garantire l'isolamento. Usa `public record` per tutti i nuovi DTO.

## 3. Pseudo-codice e Direttive di Implementazione

### 3.1 `EmissionResourceV3.java`
-   Usa `@Path("/api/v3/emissions")`.
-   Il metodo `POST` deve restituire `Uni<RestResponse<EmissionResponseV3>>`.
-   Il metodo `GET /{orderCode}/status` deve restituire `Uni<EmissionStatusV3>`.
-   Iniettare `EmissionOrchestratorV3`.

```java
// Esempio firma del metodo POST
@POST
@Path("/{orderCode}")
public Uni<RestResponse<EmissionResponseV3>> emit(
        @PathParam("orderCode") String orderCode,
        @HeaderParam("Authorization") String token,
        @Valid EmissionRequestV3 request) {
    
    return orchestrator.startEmissionProcess(orderCode, request, token)
        .map(response -> RestResponse.created(
            URI.create("/api/v3/emissions/" + orderCode + "/status"))
            .entity(response)
            .build()
        );
}
```

### 3.2 `EmissionOrchestratorV3.java`
-   Deve essere `@ApplicationScoped`.
-   Tutti i metodi pubblici devono restituire `Uni`.
-   Usa `Uni.combine().all().unis(...)` per parallelizzare le chiamate ai client `OrderClientV3`, `CustomerClientV3`, `ProductClientV3`.
-   Usa `.asTuple()` per raccogliere i risultati.
-   Dopo aver ottenuto la polizza dal `PolicyServiceV3`, invoca il `BackgroundTaskRunnerV3` in modo "fire-and-forget". Non attendere il suo completamento.

```java
// Pseudo-codice
public Uni<EmissionResponseV3> startEmissionProcess(...) {
    // 1. Inizializza lo stato in StatusRepositoryV3
    
    // 2. Recupero dati in parallelo
    Uni<Order> orderUni = orderClient.find(orderCode, token);
    Uni<Customer> customerUni = customerClient.find(...);
    Uni<Product> productUni = productClient.find(...);

    return Uni.combine().all().unis(orderUni, customerUni, productUni).asTuple()
        .flatMap(tuple -> {
            Order order = tuple.getItem1();
            // ... recupera altri item
            
            // 3. Invoca il servizio di business
            return policyService.createPolicy(order, customer, product, ...);
        })
        .invoke(policy -> {
            // 4. Avvia il task in background (non bloccare)
            backgroundTaskRunner.executeAsyncTasks(policy, token);
        })
        .map(policy -> new EmissionResponseV3(policy.orderCode(), policy.policyNumber()));
}
```

### 3.3 `BackgroundTaskRunnerV3.java`
-   Deve essere `@ApplicationScoped`.
-   Il metodo principale (`executeAsyncTasks`) deve essere annotato con `@RunOnVirtualThread`. Questo è **CRUCIALE**.
-   Questo metodo deve avere `void` come tipo di ritorno. La sua esecuzione è completamente disaccoppiata dalla richiesta originale.
-   Deve gestire la progressione degli stati (`CERTIFICATE_IN_PROGRESS`, `POST_PROCESSING`, `COMPLETE`).
-   Usa un blocco `try-catch(Throwable t)` per catturare qualsiasi errore, loggarlo e aggiornare lo stato a `FAILED`.
-   Le operazioni di invio email e upload documenti devono essere eseguite in parallelo (`Uni.join().all(...)`). Per farlo all'interno di un metodo bloccante (come quello su Virtual Thread), usa `.await().indefinitely()`.

```java
// Pseudo-codice
@RunOnVirtualThread
public void executeAsyncTasks(Policy policy, String token) {
    try {
        statusRepository.updateState(policy.orderCode(), CERTIFICATE_IN_PROGRESS);
        Certificate cert = certificateService.generate(policy).await().indefinitely();
        
        statusRepository.updateState(policy.orderCode(), POST_PROCESSING);
        
        Uni<Void> emailUni = emailService.send(policy, cert);
        Uni<Void> docUni = documentService.upload(policy, cert);
        Uni<Void> orderUni = orderClient.markComplete(policy.orderCode());
        
        Uni.join().all(emailUni, docUni, orderUni)
            .andFailFast() // o andCollectFailures()
            .await().indefinitely();
            
        statusRepository.updateState(policy.orderCode(), COMPLETE);
    } catch (Throwable t) {
        Log.errorf(t, "Background task failed for order %s", policy.orderCode());
        statusRepository.updateStateToFailed(policy.orderCode(), t.getMessage());
    }
}
```

### 3.4 `PolicyServiceV3.java` e Strategie
-   Il `PolicyServiceV3` riceve i dati dall'orchestratore.
-   Usa `EmissionStrategyFactoryV3` per decidere quale strategia istanziare basandosi su un campo nella configurazione del prodotto (es., `product.configuration().emissionType()`).
-   La logica interna di `InternalEmissionStrategyV3` e `ExternalEmissionStrategyV3` deve essere una versione pulita e refattorizzata del codice presente nel `EmissionManager` della V1. Per esempio, la logica di `checkPolicyCodeGeneration` va nel `PolicyServiceV3`, mentre le chiamate specifiche al PG (Payment Gateway) vanno nella `ExternalEmissionStrategyV3`.

### 3.5 `StatusRepositoryV3.java`
-   Implementa un repository in-memory per tracciare lo stato delle emissioni.
-   Usa una `ConcurrentHashMap<String, EmissionStatusV3>` per memorizzare gli stati, dove la chiave è `orderCode`.
-   In un ambiente di produzione reale si userebbe Redis o un DB, ma per questa implementazione una mappa concorrente è sufficiente. Fornisci metodi thread-safe per creare, aggiornare e leggere lo stato.

## 4. Direttive Aggiuntive

-   **Logging:** Aggiungi log informativi (usando `quarkus-logging-json` o `JBoss Logging`) nei punti chiave: inizio e fine della richiesta, inizio e fine dei task in background, errori.
-   **Gestione Errori:** Trasforma le eccezioni dei client REST (es. `WebApplicationException`) in eccezioni di business specifiche (es. `OrderNotFoundException`). Gestisci queste eccezioni nell'orchestratore per restituire i corretti codici di stato HTTP (404, 409, ecc.).
-   **Configurazione:** Rendi configurabili i prefissi delle polizze o altri parametri tramite `@ConfigProperty` in `application.properties`.
-   **Test:** Genera una classe di test di integrazione (`EmissionResourceV3Test.java`) che usi RestAssured per chiamare l'endpoint `POST` e poi Awaitility per interrogare l'endpoint di stato `/status` finché lo stato non diventa `COMPLETE` o `FAILED`. Mocka i client esterni usando `@InjectMock` e `@RestClient`.

Seguendo queste direttive dettagliate, dovresti essere in grado di generare una soluzione V3 completa, moderna e robusta che risolva i problemi dell'implementazione legacy.