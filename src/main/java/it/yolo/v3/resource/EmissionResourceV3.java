package it.yolo.v3.resource;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.EmissionResponseV3;
import it.yolo.v3.dto.response.EmissionStatusV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import it.yolo.v3.service.EmissionOrchestratorV3;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.net.URI;

@Path("/api/v3/emissions")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class EmissionResourceV3 {
    private static final Logger log = Logger.getLogger(EmissionResourceV3.class);

    @Inject
    EmissionOrchestratorV3 orchestrator;

    @Inject
    StatusRepositoryV3 statusRepository;

    @POST
    @Path("/{orderCode}")
    @Operation(summary = "Avvia processo di emissione asincrono V3")
    @APIResponse(responseCode = "201", description = "Processo di emissione avviato",
            content = @Content(schema = @Schema(implementation = EmissionResponseV3.class)))
    public Uni<RestResponse<EmissionResponseV3>> emit(
            @PathParam("orderCode") String orderCode,
            @HeaderParam("Authorization") String token,
            @Valid EmissionRequestV3 request) {

        return orchestrator.startEmissionProcess(orderCode, request, token)
                .map(resp -> RestResponse.ResponseBuilder
                        .created(URI.create("/api/v3/emissions/" + orderCode + "/status"))
                        .entity(resp)
                        .build());
    }

    @GET
    @Path("/{orderCode}/status")
    @Operation(summary = "Stato del processo di emissione V3")
    @APIResponse(responseCode = "200", description = "Stato corrente",
            content = @Content(schema = @Schema(implementation = EmissionStatusV3.class)))
    public Uni<RestResponse<EmissionStatusV3>> status(@PathParam("orderCode") String orderCode) {
        return Uni.createFrom().item(() -> statusRepository.get(orderCode).orElse(EmissionStatusV3.PENDING))
                .map(RestResponse::ok);
    }
}

