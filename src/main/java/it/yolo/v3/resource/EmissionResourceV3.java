package it.yolo.v3.resource;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.EmissionResponseV3;
import it.yolo.v3.dto.response.EmissionStatusDetailV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import it.yolo.v3.service.EmissionOrchestratorV3;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.net.URI;

/**
 * Endpoint REST V3 per l'emissione di polizze.
 * Implementa un'API reattiva e asincrona per l'emissione di polizze assicurative.
 */
@Path("/api/v3/emissions")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequestScoped
@Tag(name = "Emission V3", description = "API V3 per l'emissione di polizze assicurative")
public class EmissionResourceV3 {
    
    private static final Logger LOG = Logger.getLogger(EmissionResourceV3.class);
    
    @Inject
    EmissionOrchestratorV3 orchestrator;
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    /**
     * Avvia il processo di emissione per un ordine.
     * Restituisce una risposta immediata mentre i task in background continuano l'elaborazione.
     */
    @POST
    @Path("/{orderCode}")
    @Operation(
        summary = "Avvia emissione polizza",
        description = "Avvia il processo di emissione per l'ordine specificato. " +
                     "La risposta è immediata (< 500ms) mentre i task in background continuano l'elaborazione."
    )
    @APIResponses({
        @APIResponse(
            responseCode = "201",
            description = "Emissione avviata con successo",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = @Schema(implementation = EmissionResponseV3.class)
            )
        ),
        @APIResponse(
            responseCode = "400",
            description = "Richiesta non valida"
        ),
        @APIResponse(
            responseCode = "404",
            description = "Ordine non trovato"
        ),
        @APIResponse(
            responseCode = "409",
            description = "Ordine già in elaborazione"
        ),
        @APIResponse(
            responseCode = "500",
            description = "Errore interno del server"
        )
    })
    public Uni<RestResponse<EmissionResponseV3>> emit(
            @Parameter(description = "Codice dell'ordine da emettere", required = true)
            @PathParam("orderCode") @NotBlank String orderCode,
            
            @Parameter(description = "Token di autorizzazione", required = true)
            @HeaderParam("Authorization") @NotBlank String token,
            
            @Parameter(description = "Dati della richiesta di emissione")
            @Valid EmissionRequestV3 request) {
        
        LOG.infof("V3 - Received emission request for order %s", orderCode);
        
        // Verifica se l'ordine è già in elaborazione
        if (statusRepository.exists(orderCode)) {
            LOG.warnf("V3 - Order %s is already being processed", orderCode);
            return Uni.createFrom().item(
                RestResponse.status(RestResponse.Status.CONFLICT)
                    .entity(new EmissionResponseV3(orderCode, null))
                    .build()
            );
        }
        
        return orchestrator.startEmissionProcess(orderCode, request, token)
            .map(response -> {
                LOG.infof("V3 - Emission started successfully for order %s -> policy %s", 
                         orderCode, response.policyNumber());
                
                return RestResponse.created(
                    URI.create("/api/v3/emissions/" + orderCode + "/status")
                ).entity(response).build();
            })
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "V3 - Emission failed for order %s", orderCode);
                
                if (throwable.getMessage().contains("not found")) {
                    return RestResponse.status(RestResponse.Status.NOT_FOUND)
                        .entity(new EmissionResponseV3(orderCode, null))
                        .build();
                } else if (throwable.getMessage().contains("validation")) {
                    return RestResponse.status(RestResponse.Status.BAD_REQUEST)
                        .entity(new EmissionResponseV3(orderCode, null))
                        .build();
                } else {
                    return RestResponse.status(RestResponse.Status.INTERNAL_SERVER_ERROR)
                        .entity(new EmissionResponseV3(orderCode, null))
                        .build();
                }
            });
    }
    
    /**
     * Ottiene lo stato attuale del processo di emissione per un ordine.
     */
    @GET
    @Path("/{orderCode}/status")
    @Operation(
        summary = "Ottieni stato emissione",
        description = "Restituisce lo stato attuale del processo di emissione per l'ordine specificato."
    )
    @APIResponses({
        @APIResponse(
            responseCode = "200",
            description = "Stato recuperato con successo",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = @Schema(implementation = EmissionStatusDetailV3.class)
            )
        ),
        @APIResponse(
            responseCode = "404",
            description = "Ordine non trovato"
        )
    })
    public Uni<RestResponse<EmissionStatusDetailV3>> getStatus(
            @Parameter(description = "Codice dell'ordine", required = true)
            @PathParam("orderCode") @NotBlank String orderCode) {
        
        LOG.debugf("V3 - Getting status for order %s", orderCode);
        
        return Uni.createFrom().item(() -> {
            EmissionStatusDetailV3 status = statusRepository.getStatus(orderCode);
            
            if (status.status().name().equals("NOT_FOUND")) {
                return RestResponse.status(RestResponse.Status.NOT_FOUND)
                    .entity(status)
                    .build();
            } else {
                return RestResponse.ok(status).build();
            }
        });
    }
    
    /**
     * Verifica se un ordine può essere emesso.
     */
    @GET
    @Path("/{orderCode}/can-emit")
    @Operation(
        summary = "Verifica possibilità emissione",
        description = "Verifica se l'ordine specificato può essere emesso."
    )
    @APIResponses({
        @APIResponse(
            responseCode = "200",
            description = "Verifica completata",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = @Schema(implementation = CanEmitResponseV3.class)
            )
        ),
        @APIResponse(
            responseCode = "404",
            description = "Ordine non trovato"
        )
    })
    public Uni<RestResponse<CanEmitResponseV3>> canEmit(
            @Parameter(description = "Codice dell'ordine", required = true)
            @PathParam("orderCode") @NotBlank String orderCode,
            
            @Parameter(description = "Token di autorizzazione", required = true)
            @HeaderParam("Authorization") @NotBlank String token) {
        
        LOG.debugf("V3 - Checking if order %s can be emitted", orderCode);
        
        return orchestrator.canEmitOrder(orderCode, token)
            .map(canEmit -> {
                CanEmitResponseV3 response = new CanEmitResponseV3(orderCode, canEmit);
                return RestResponse.ok(response).build();
            })
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "V3 - Failed to check if order %s can be emitted", orderCode);
                
                if (throwable.getMessage().contains("not found")) {
                    return RestResponse.status(RestResponse.Status.NOT_FOUND)
                        .entity(new CanEmitResponseV3(orderCode, false))
                        .build();
                } else {
                    return RestResponse.ok(new CanEmitResponseV3(orderCode, false)).build();
                }
            });
    }
    
    /**
     * Ottiene informazioni dettagliate sull'ordine.
     */
    @GET
    @Path("/{orderCode}/info")
    @Operation(
        summary = "Ottieni informazioni ordine",
        description = "Restituisce informazioni dettagliate sull'ordine senza avviare l'emissione."
    )
    public Uni<RestResponse<EmissionOrchestratorV3.OrderInfoV3>> getOrderInfo(
            @Parameter(description = "Codice dell'ordine", required = true)
            @PathParam("orderCode") @NotBlank String orderCode,
            
            @Parameter(description = "Token di autorizzazione", required = true)
            @HeaderParam("Authorization") @NotBlank String token) {
        
        LOG.debugf("V3 - Getting order info for %s", orderCode);
        
        return orchestrator.getOrderInfo(orderCode, token)
            .map(orderInfo -> RestResponse.ok(orderInfo).build())
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "V3 - Failed to get order info for %s", orderCode);
                
                if (throwable.getMessage().contains("not found")) {
                    return RestResponse.status(RestResponse.Status.NOT_FOUND).build();
                } else {
                    return RestResponse.status(RestResponse.Status.INTERNAL_SERVER_ERROR).build();
                }
            });
    }
    
    /**
     * Record per la risposta di verifica emissione
     */
    public record CanEmitResponseV3(
        String orderCode,
        boolean canEmit
    ) {}
}
