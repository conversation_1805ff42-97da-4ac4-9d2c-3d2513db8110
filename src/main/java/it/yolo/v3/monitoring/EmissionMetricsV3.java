package it.yolo.v3.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Metriche per il monitoraggio dell'emissione V3
 */
@ApplicationScoped
public class EmissionMetricsV3 {
    
    private static final Logger LOG = Logger.getLogger(EmissionMetricsV3.class);
    
    @Inject
    MeterRegistry meterRegistry;
    
    // Contatori
    private Counter emissionStartedCounter;
    private Counter emissionSuccessCounter;
    private Counter emissionFailedCounter;
    private Counter certificateGeneratedCounter;
    private Counter emailSentCounter;
    private Counter backgroundTaskFailedCounter;
    
    // Timer
    private Timer emissionTimer;
    private Timer certificateTimer;
    private Timer emailTimer;
    
    public void init() {
        // Inizializza i contatori
        emissionStartedCounter = Counter.builder("emission.v3.started")
            .description("Number of emissions started")
            .register(meterRegistry);
            
        emissionSuccessCounter = Counter.builder("emission.v3.success")
            .description("Number of successful emissions")
            .register(meterRegistry);
            
        emissionFailedCounter = Counter.builder("emission.v3.failed")
            .description("Number of failed emissions")
            .register(meterRegistry);
            
        certificateGeneratedCounter = Counter.builder("emission.v3.certificate.generated")
            .description("Number of certificates generated")
            .register(meterRegistry);
            
        emailSentCounter = Counter.builder("emission.v3.email.sent")
            .description("Number of emails sent")
            .register(meterRegistry);
            
        backgroundTaskFailedCounter = Counter.builder("emission.v3.background.failed")
            .description("Number of failed background tasks")
            .register(meterRegistry);
        
        // Inizializza i timer
        emissionTimer = Timer.builder("emission.v3.duration")
            .description("Emission process duration")
            .register(meterRegistry);
            
        certificateTimer = Timer.builder("emission.v3.certificate.duration")
            .description("Certificate generation duration")
            .register(meterRegistry);
            
        emailTimer = Timer.builder("emission.v3.email.duration")
            .description("Email sending duration")
            .register(meterRegistry);
    }
    
    public void recordEmissionStarted() {
        emissionStartedCounter.increment();
        LOG.debugf("V3 - Emission started metric recorded");
    }
    
    public void recordEmissionSuccess() {
        emissionSuccessCounter.increment();
        LOG.debugf("V3 - Emission success metric recorded");
    }
    
    public void recordEmissionFailed() {
        emissionFailedCounter.increment();
        LOG.debugf("V3 - Emission failed metric recorded");
    }
    
    public void recordCertificateGenerated() {
        certificateGeneratedCounter.increment();
        LOG.debugf("V3 - Certificate generated metric recorded");
    }
    
    public void recordEmailSent() {
        emailSentCounter.increment();
        LOG.debugf("V3 - Email sent metric recorded");
    }
    
    public void recordBackgroundTaskFailed() {
        backgroundTaskFailedCounter.increment();
        LOG.debugf("V3 - Background task failed metric recorded");
    }
    
    public Timer.Sample startEmissionTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void stopEmissionTimer(Timer.Sample sample) {
        sample.stop(emissionTimer);
    }
    
    public Timer.Sample startCertificateTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void stopCertificateTimer(Timer.Sample sample) {
        sample.stop(certificateTimer);
    }
    
    public Timer.Sample startEmailTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void stopEmailTimer(Timer.Sample sample) {
        sample.stop(emailTimer);
    }
}
