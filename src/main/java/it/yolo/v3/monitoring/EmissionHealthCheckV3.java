package it.yolo.v3.monitoring;

import it.yolo.v3.config.EmissionConfigV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Readiness;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Health check per l'emissione V3
 */
@Readiness
@ApplicationScoped
public class EmissionHealthCheckV3 implements HealthCheck {
    
    @Inject
    EmissionConfigV3 config;
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    @Override
    public HealthCheckResponse call() {
        if (!config.isHealthCheckEnabled()) {
            return HealthCheckResponse.up("emission-v3-disabled");
        }
        
        try {
            // Verifica che il servizio sia abilitato
            if (!config.isEmissionEnabled()) {
                return HealthCheckResponse.down("emission-v3")
                    .withData("reason", "Emission service is disabled")
                    .build();
            }
            
            // Verifica che il repository di stato sia funzionante
            int activeEmissions = statusRepository.size();
            if (activeEmissions > config.getMaxConcurrentEmissions()) {
                return HealthCheckResponse.down("emission-v3")
                    .withData("reason", "Too many concurrent emissions")
                    .withData("active", activeEmissions)
                    .withData("max", config.getMaxConcurrentEmissions())
                    .build();
            }
            
            // Tutto OK
            return HealthCheckResponse.up("emission-v3")
                .withData("active_emissions", activeEmissions)
                .withData("max_concurrent", config.getMaxConcurrentEmissions())
                .withData("internal_enabled", config.isInternalEmissionEnabled())
                .withData("external_enabled", config.isExternalEmissionEnabled())
                .withData("certificate_enabled", config.isCertificateEnabled())
                .withData("email_enabled", config.isEmailEnabled())
                .build();
                
        } catch (Exception e) {
            return HealthCheckResponse.down("emission-v3")
                .withData("reason", "Health check failed: " + e.getMessage())
                .build();
        }
    }
}
