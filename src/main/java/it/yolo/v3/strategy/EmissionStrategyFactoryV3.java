package it.yolo.v3.strategy;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.util.Optional;

/**
 * Factory per la selezione delle strategie di emissione V3.
 * Utilizza CDI per iniettare tutte le strategie disponibili e seleziona quella appropriata.
 */
@ApplicationScoped
public class EmissionStrategyFactoryV3 {
    
    private static final Logger LOG = Logger.getLogger(EmissionStrategyFactoryV3.class);
    
    @Inject
    Instance<EmissionStrategyV3> strategies;
    
    /**
     * Seleziona la strategia di emissione appropriata basandosi sul tipo di emissione.
     * 
     * @param emissionType Tipo di emissione (internal, external, email)
     * @return Strategia di emissione appropriata
     * @throws IllegalArgumentException se nessuna strategia è disponibile per il tipo specificato
     */
    public EmissionStrategyV3 getStrategy(String emissionType) {
        if (emissionType == null || emissionType.isBlank()) {
            LOG.warn("Emission type is null or blank, defaulting to internal");
            emissionType = "internal";
        }
        
        LOG.debugf("V3 - Selecting emission strategy for type: %s", emissionType);
        
        // Cerca la strategia che può gestire questo tipo di emissione
        Optional<EmissionStrategyV3> strategy = strategies.stream()
            .filter(s -> s.canHandle(emissionType))
            .findFirst();
        
        if (strategy.isEmpty()) {
            // Fallback: se non trova la strategia specifica, usa quella interna
            LOG.warnf("V3 - No specific strategy found for type '%s', falling back to internal", emissionType);
            strategy = strategies.stream()
                .filter(s -> s.canHandle("internal"))
                .findFirst();
        }
        
        if (strategy.isEmpty()) {
            String availableStrategies = strategies.stream()
                .map(EmissionStrategyV3::getStrategyName)
                .reduce((a, b) -> a + ", " + b)
                .orElse("none");
                
            throw new IllegalArgumentException(
                String.format("V3 - No emission strategy found for type '%s'. Available strategies: %s", 
                             emissionType, availableStrategies));
        }
        
        EmissionStrategyV3 selectedStrategy = strategy.get();
        LOG.infof("V3 - Selected emission strategy: %s for type %s", 
                 selectedStrategy.getStrategyName(), emissionType);
        
        return selectedStrategy;
    }
    
    /**
     * Seleziona la strategia basandosi sul contesto di emissione.
     * 
     * @param context Contesto di emissione
     * @return Strategia di emissione appropriata
     */
    public EmissionStrategyV3 getStrategy(EmissionContextV3 context) {
        if (context == null) {
            throw new IllegalArgumentException("Emission context cannot be null");
        }
        
        return getStrategy(context.getEmissionType());
    }
    
    /**
     * Verifica se una strategia è disponibile per il tipo specificato.
     * 
     * @param emissionType Tipo di emissione da verificare
     * @return true se una strategia è disponibile, false altrimenti
     */
    public boolean isStrategyAvailable(String emissionType) {
        if (emissionType == null || emissionType.isBlank()) {
            return false;
        }
        
        return strategies.stream()
            .anyMatch(s -> s.canHandle(emissionType));
    }
}
