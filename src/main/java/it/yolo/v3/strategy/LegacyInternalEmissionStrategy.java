package it.yolo.v3.strategy;

import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.v3.domain.Policy;
import it.yolo.emission.Internal;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.time.LocalDateTime;

/**
 * Strategia per l'emissione interna che utilizza il codice legacy esistente.
 * Integra l'implementazione esistente InternalEmissionImpl.
 */
@ApplicationScoped
@Internal
public class LegacyInternalEmissionStrategy implements EmissionStrategy {
    
    private static final Logger LOG = Logger.getLogger(LegacyInternalEmissionStrategy.class);
    
    @Inject
    @RestClient
    PolicyClient policyClient;
    
    @Inject
    JsonWebToken jsonWebToken;
    
    @ConfigProperty(name = "emission.internal.policy-prefix", defaultValue = "INT")
    String policyPrefix;
    
    @Override
    public Uni<Policy> emit(EmissionContext context) {
        LOG.infof("Starting legacy internal emission for order %s", context.order().orderCode());
        
        return Uni.createFrom().item(() -> {
            validateInternalEmission(context);
            
            // Usa il codice legacy esistente per l'emissione interna
            String token = "Bearer " + jsonWebToken.getRawToken();
            
            // Crea la richiesta per il PolicyClient usando la struttura legacy
            PolicyRequestDto policyRequest = createPolicyRequest(context);
            
            try {
                Response response = policyClient.create(token, policyRequest);
                String emissionResult = response.readEntity(String.class);
                
                // Converte la risposta legacy nel nuovo modello Policy V3
                Policy policy = convertLegacyToV3Policy(emissionResult, context);
                
                LOG.infof("Legacy internal emission completed: %s", policy.data().policyCode());
                return policy;
                
            } catch (Exception e) {
                LOG.errorf(e, "Legacy internal emission failed for order %s", context.order().orderCode());
                throw new RuntimeException("Internal emission failed", e);
            }
        });
    }
    
    @Override
    public String getStrategyName() {
        return "INTERNAL";
    }
    
    private void validateInternalEmission(EmissionContext context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for emission");
        }
        
        if (!context.product().isInternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for internal emission");
        }
    }
    
    /**
     * Crea la richiesta per il PolicyClient usando la struttura legacy
     */
    private PolicyRequestDto createPolicyRequest(EmissionContext context) {
        PolicyRequestDto request = new PolicyRequestDto();
        DataPolicyRequestDto data = new DataPolicyRequestDto();
        
        // Mappa i dati del contesto V3 verso la struttura legacy
        // Basandosi sulla logica esistente in InternalEmissionImpl
        
        data.setStartDate(context.order().createdAt().toString());
        // Altri mapping necessari...
        
        request.setDto(data);
        return request;
    }
    
    /**
     * Converte la risposta legacy del PolicyClient nel nuovo modello Policy V3
     */
    private Policy convertLegacyToV3Policy(String legacyResponse, EmissionContext context) {
        // Parsing della risposta legacy e conversione nel modello V3
        // Implementazione semplificata - in produzione dovrebbe essere completa
        
        // Per ora, placeholder che dovrebbe essere implementato completamente
        throw new UnsupportedOperationException("Legacy to V3 conversion not yet implemented");
    }
}
