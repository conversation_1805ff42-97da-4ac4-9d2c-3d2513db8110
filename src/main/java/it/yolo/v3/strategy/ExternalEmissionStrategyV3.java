package it.yolo.v3.strategy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.PGClientV3;
import it.yolo.v3.client.PolicyClientV3;
import it.yolo.v3.dto.request.PolicyRequestDtoV3;
import it.yolo.v3.dto.response.PolicyResponseDtoV3;
import it.yolo.v3.dto.response.DataPolicyResponseV3;
import it.yolo.v3.dto.response.CustomerResponseV3;
import it.yolo.v3.dto.response.ProductResponseV3;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Strategia per l'emissione esterna delle polizze V3.
 * Gestisce l'emissione di polizze che vengono delegate a sistemi PG (Payment Gateway) esterni.
 */
@ApplicationScoped
public class ExternalEmissionStrategyV3 implements EmissionStrategyV3 {
    
    private static final Logger LOG = Logger.getLogger(ExternalEmissionStrategyV3.class);
    
    @Inject
    @RestClient
    PGClientV3 pgClient;
    
    @Inject
    @RestClient
    PolicyClientV3 policyClient;
    
    @Inject
    ObjectMapper objectMapper;
    
    @ConfigProperty(name = "emission.external.policy-prefix", defaultValue = "EXT")
    String policyPrefix;
    
    @ConfigProperty(name = "emission.external.timeout-ms", defaultValue = "10000")
    int timeoutMs;
    
    @ConfigProperty(name = "emission.username")
    String emissionUsername;
    
    @ConfigProperty(name = "emission.password")
    String emissionPassword;
    
    @Override
    public Uni<PolicyResponseDtoV3> emit(EmissionContextV3 context) {
        LOG.infof("V3 - Starting external emission for order %s", context.orderCode());
        
        // Validazioni specifiche per emissione esterna
        validateExternalEmission(context);
        
        // Prima crea la polizza nel sistema interno
        return createInternalPolicy(context)
            .flatMap(policy -> {
                // Poi invia la richiesta al sistema esterno (PG)
                return callExternalPG(context, policy)
                    .map(pgResponse -> {
                        LOG.infof("V3 - External PG call completed for order %s", context.orderCode());
                        return policy; // Ritorna la polizza creata internamente
                    });
            })
            .onItem().invoke(policy -> 
                LOG.infof("V3 - External emission completed for order %s -> policy %s", 
                         context.orderCode(), policy.data().policyCode()))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - External emission failed for order %s", context.orderCode()));
    }
    
    @Override
    public String getStrategyName() {
        return "EXTERNAL";
    }
    
    @Override
    public boolean canHandle(String emissionType) {
        return "external".equalsIgnoreCase(emissionType);
    }
    
    private void validateExternalEmission(EmissionContextV3 context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for external emission");
        }
        
        if (!context.isExternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for external emission");
        }
        
        // Validazioni specifiche per emissione esterna
        if (context.product().data().price() == null || 
            context.product().data().price() <= 0) {
            throw new IllegalArgumentException("Product price must be greater than zero for external emission");
        }
    }
    
    private Uni<PolicyResponseDtoV3> createInternalPolicy(EmissionContextV3 context) {
        LOG.debugf("V3 - Creating internal policy for external emission, order %s", context.orderCode());
        
        // Utilizza la strategia interna per creare la polizza
        InternalEmissionStrategyV3 internalStrategy = new InternalEmissionStrategyV3();
        // Nota: In un'implementazione reale, si dovrebbe iniettare la strategia interna
        // o utilizzare il factory per ottenerla
        
        // Per ora, creiamo direttamente la richiesta di polizza
        PolicyRequestDtoV3 policyRequest = buildPolicyRequestForExternal(context);
        
        return policyClient.create(context.token(), policyRequest);
    }
    
    private Uni<JsonNode> callExternalPG(EmissionContextV3 context, PolicyResponseDtoV3 policy) {
        LOG.debugf("V3 - Calling external PG for order %s, policy %s", 
                  context.orderCode(), policy.data().policyCode());
        
        try {
            // Prepara la richiesta per il PG esterno
            JsonNode pgRequest = buildPGRequest(context, policy);
            
            // Chiama il PG esterno
            return pgClient.emission(pgRequest)
                .onItem().transform(response -> {
                    if (response.getStatus() >= 200 && response.getStatus() < 300) {
                        return response.readEntity(JsonNode.class);
                    } else {
                        throw new RuntimeException("PG call failed with status: " + response.getStatus());
                    }
                });
                
        } catch (Exception e) {
            LOG.errorf(e, "V3 - Failed to prepare PG request for order %s", context.orderCode());
            return Uni.createFrom().failure(new RuntimeException("Failed to call external PG", e));
        }
    }
    
    private JsonNode buildPGRequest(EmissionContextV3 context, PolicyResponseDtoV3 policy) {
        try {
            ObjectNode request = objectMapper.createObjectNode();
            ObjectNode data = objectMapper.createObjectNode();
            
            // Aggiunge i dati dell'ordine
            data.set("order", objectMapper.valueToTree(context.order()));
            
            // Aggiunge i dati del cliente
            data.set("customer", objectMapper.valueToTree(context.customer()));
            
            // Aggiunge i dati della polizza
            data.set("policy", objectMapper.valueToTree(policy));
            
            // Aggiunge credenziali di emissione
            data.put("emissionUsername", emissionUsername);
            data.put("emissionPassword", emissionPassword);
            
            // Aggiunge dati di pagamento
            if (context.request().paymentType() != null) {
                data.put("paymentType", context.request().paymentType());
            }
            if (context.request().paymentToken() != null) {
                data.put("paymentToken", context.request().paymentToken());
            }
            if (context.request().subscriptionId() != null) {
                data.put("subscriptionId", context.request().subscriptionId());
            }
            
            request.set("data", data);
            
            return request;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build PG request", e);
        }
    }
    
    private PolicyRequestDtoV3 buildPolicyRequestForExternal(EmissionContextV3 context) {
        // Simile alla strategia interna ma con alcune differenze per l'emissione esterna
        // Per ora, riutilizziamo la logica della strategia interna
        // In un'implementazione reale, ci potrebbero essere differenze specifiche
        
        var orderData = context.order().data();
        var customerData = context.customer().data();
        var productData = context.product().data();
        
        // Costruisce la richiesta di polizza per emissione esterna
        // (implementazione semplificata)
        return new PolicyRequestDtoV3(null); // Implementazione da completare
    }
}
