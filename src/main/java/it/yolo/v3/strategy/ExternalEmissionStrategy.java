package it.yolo.v3.strategy;

import it.yolo.client.pg.PGClient;
import it.yolo.v3.domain.Policy;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Strategia per l'emissione esterna delle polizze.
 * 
 * Gestisce l'emissione di polizze che vengono delegate
 * a sistemi PG (Produttori Generali) esterni.
 */
@ApplicationScoped
public class ExternalEmissionStrategy implements EmissionStrategy {

    @RestClient
    @Inject
    PGClient pgClient;

    private static final Logger LOG = Logger.getLogger(ExternalEmissionStrategy.class);
    
    @ConfigProperty(name = "emission.external.policy-prefix", defaultValue = "EXT")
    String policyPrefix;
    
    @ConfigProperty(name = "emission.external.timeout-ms", defaultValue = "5000")
    int timeoutMs;
    
    @Override
    public Uni<Policy> emit(EmissionContext context) {
        LOG.infof("Starting external emission for order %s", context.order().orderCode());
        
        return Uni.createFrom().item(() -> {
            // Validazione specifica per emissione esterna
            validateExternalEmission(context);
            
            // Simula la chiamata al sistema PG esterno
            try {
                Thread.sleep(300); // Simula latenza del sistema esterno
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("External emission interrupted", e);
            }
            
            // Simula la risposta del sistema esterno
            ExternalEmissionResponse externalResponse = callExternalPG(context);
            
            // Crea la polizza basata sulla risposta esterna
            // Per ora, implementazione semplificata
            Policy policy = createSimplifiedPolicy(context, externalResponse);
            
            LOG.infof("External emission completed: policy %s for order %s", 
                     externalResponse.policyNumber(), context.order().orderCode());
            
            return policy;
        });
    }
    
    @Override
    public String getStrategyName() {
        return "EXTERNAL";
    }
    
    private void validateExternalEmission(EmissionContext context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for external emission");
        }
        
        if (!context.product().isExternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for external emission");
        }
        
        // Validazioni specifiche per emissione esterna
        if (context.customer().taxCode() == null || context.customer().taxCode().isBlank()) {
            throw new IllegalArgumentException("Tax code is required for external emission");
        }
    }
    
    private ExternalEmissionResponse callExternalPG(EmissionContext context) {
        LOG.debugf("Calling external PG for order %s", context.order().orderCode());
        
        // In una implementazione reale, qui ci sarebbe:
        // 1. Preparazione della richiesta per il PG esterno
        // 2. Chiamata REST/SOAP al sistema esterno
        // 3. Parsing della risposta
        // 4. Gestione degli errori specifici del PG
        
        // Per ora simula una risposta di successo
        String externalPolicyNumber = generateExternalPolicyNumber(context);
        
        return new ExternalEmissionResponse(
            externalPolicyNumber,
            context.product().price() != null ? context.product().price() : BigDecimal.ZERO,
            LocalDateTime.now(),
            LocalDateTime.now().plusYears(1),
            "SUCCESS",
            "Policy emitted successfully by external PG"
        );
    }
    
    private String generateExternalPolicyNumber(EmissionContext context) {
        // Formato per polizze esterne: PREFIX + PG_CODE + ORDER_CODE + TIMESTAMP
        String pgCode = context.product().insuranceCompany()
            .substring(0, Math.min(3, context.product().insuranceCompany().length()))
            .toUpperCase();
        
        String timestamp = String.valueOf(System.currentTimeMillis() % 1000000);
        
        return policyPrefix + pgCode + context.order().orderCode() + timestamp;
    }
    
    /**
     * Crea una polizza semplificata dalla risposta esterna
     */
    private Policy createSimplifiedPolicy(EmissionContext context, ExternalEmissionResponse response) {
        // Implementazione semplificata che crea una polizza con i campi essenziali
        // In una implementazione reale, questo mapperebbe tutti i campi necessari
        
        // Per ora, creiamo una polizza mock con i dati minimi necessari
        // Questo è un placeholder - in produzione si dovrebbe usare il servizio reale
        
        throw new UnsupportedOperationException("Policy creation not yet implemented - needs proper Policy mapping");
    }
    
    /**
     * Record per la risposta del sistema PG esterno
     */
    static record ExternalEmissionResponse(
        String policyNumber,
        BigDecimal premium,
        LocalDateTime startDate,
        LocalDateTime endDate,
        String status,
        String message
    ) {}
}
