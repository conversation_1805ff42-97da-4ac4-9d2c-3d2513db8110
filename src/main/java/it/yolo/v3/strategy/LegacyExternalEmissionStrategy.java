package it.yolo.v3.strategy;

import it.yolo.client.pg.PGClient;
import it.yolo.emission.dto.request.EmissionRequest;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.v3.domain.Policy;
import it.yolo.emission.External;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

/**
 * Strategia per l'emissione esterna che utilizza il codice legacy esistente.
 * Integra l'implementazione esistente ExternalEmissionImpl.
 */
@ApplicationScoped
@External
public class LegacyExternalEmissionStrategy implements EmissionStrategy {
    
    private static final Logger LOG = Logger.getLogger(LegacyExternalEmissionStrategy.class);
    
    @Inject
    @RestClient
    PGClient pgClient;
    
    @Inject
    JsonWebToken jsonWebToken;
    
    @ConfigProperty(name = "emission.external.policy-prefix", defaultValue = "EXT")
    String policyPrefix;
    
    @ConfigProperty(name = "emission.username")
    String emissionUsername;
    
    @ConfigProperty(name = "emission.password")
    String emissionPassword;
    
    @ConfigProperty(name = "tenant.name")
    String tenant;
    
    @Override
    public Uni<Policy> emit(EmissionContext context) {
        LOG.infof("Starting legacy external emission for order %s", context.order().orderCode());
        
        return Uni.createFrom().item(() -> {
            validateExternalEmission(context);
            
            // Usa il codice legacy esistente per l'emissione esterna
            String token = "Bearer " + jsonWebToken.getRawToken();
            
            try {
                // Crea la richiesta per il PGClient usando la struttura legacy
                EmissionRequest pgRequest = createPGRequest(context);
                
                // Converte EmissionRequest in JsonNode per il PGClient
                ObjectMapper mapper = new ObjectMapper();
                JsonNode requestNode = mapper.valueToTree(pgRequest);
                
                Response response = pgClient.emission(requestNode);
                
                if (response.getStatus() == 200) {
                    String pgResponse = response.readEntity(String.class);
                    
                    // Converte la risposta del PG nel nuovo modello Policy V3
                    Policy policy = convertPGResponseToV3Policy(pgResponse, context);
                    
                    LOG.infof("Legacy external emission completed: %s", policy.data().policyCode());
                    return policy;
                } else {
                    throw new RuntimeException("PG emission failed with status: " + response.getStatus());
                }
                
            } catch (Exception e) {
                LOG.errorf(e, "Legacy external emission failed for order %s", context.order().orderCode());
                throw new RuntimeException("External emission failed", e);
            }
        });
    }
    
    @Override
    public String getStrategyName() {
        return "EXTERNAL";
    }
    
    private void validateExternalEmission(EmissionContext context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for emission");
        }
        
        if (!context.product().isExternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for external emission");
        }
    }
    
    /**
     * Crea la richiesta per il PGClient usando la struttura legacy
     */
    private EmissionRequest createPGRequest(EmissionContext context) {
        // Basandosi sulla logica esistente in ExternalEmissionImpl
        
        EmissionRequestDto emissionDto = new EmissionRequestDto();
        
        // Popola i dati necessari per la chiamata al PG
        emissionDto.setEmissionUsername(emissionUsername);
        emissionDto.setEmissionPassword(emissionPassword);
        
        // Mappa i dati del contesto V3 verso la struttura legacy
        // Questo dovrebbe includere tutti i mapping necessari
        
        EmissionRequest request = new EmissionRequest();
        request.setData(emissionDto);
        request.setTenant(tenant);
        
        return request;
    }
    
    /**
     * Converte la risposta del PG nel nuovo modello Policy V3
     */
    private Policy convertPGResponseToV3Policy(String pgResponse, EmissionContext context) {
        // Parsing della risposta PG e conversione nel modello V3
        // Implementazione semplificata - in produzione dovrebbe essere completa
        
        // Per ora, placeholder che dovrebbe essere implementato completamente
        throw new UnsupportedOperationException("PG response to V3 conversion not yet implemented");
    }
}
