package it.yolo.v3.strategy;

import it.yolo.v3.domain.Product;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.util.Optional;

/**
 * Factory per la selezione della strategia di emissione appropriata.
 * 
 * Utilizza CDI per scoprire automaticamente tutte le implementazioni
 * di EmissionStrategy e seleziona quella corretta basandosi sulla
 * configurazione del prodotto.
 */
@ApplicationScoped
public class EmissionStrategyFactory {
    
    private static final Logger LOG = Logger.getLogger(EmissionStrategyFactory.class);
    
    @Inject
    Instance<EmissionStrategy> strategies;
    
    /**
     * Seleziona la strategia di emissione appropriata per il prodotto.
     * 
     * @param product Prodotto per cui selezionare la strategia
     * @return Strategia di emissione appropriata
     * @throws IllegalArgumentException se nessuna strategia è disponibile
     */
    public EmissionStrategy getStrategy(Product product) {
        if (product == null) {
            throw new IllegalArgumentException("Product cannot be null");
        }
        
        String emissionType = product.emissionType();
        if (emissionType == null || emissionType.isBlank()) {
            throw new IllegalArgumentException("Product emission type is not configured");
        }
        
        LOG.debugf("Selecting emission strategy for product %s, emission type: %s", 
                  product.code(), emissionType);
        
        // Cerca la strategia che può gestire questo tipo di emissione
        Optional<EmissionStrategy> strategy = strategies.stream()
            .filter(s -> s.getStrategyName().equalsIgnoreCase(emissionType))
            .findFirst();
        
        if (strategy.isEmpty()) {
            String availableStrategies = strategies.stream()
                .map(EmissionStrategy::getStrategyName)
                .reduce((a, b) -> a + ", " + b)
                .orElse("none");
                
            throw new IllegalArgumentException(
                String.format("No emission strategy found for type '%s'. Available strategies: %s", 
                             emissionType, availableStrategies));
        }
        
        EmissionStrategy selectedStrategy = strategy.get();
        LOG.infof("Selected emission strategy: %s for product %s", 
                 selectedStrategy.getStrategyName(), product.code());
        
        return selectedStrategy;
    }
    
    /**
     * Seleziona la strategia basandosi sul contesto di emissione.
     * 
     * @param context Contesto di emissione
     * @return Strategia di emissione appropriata
     */
    public EmissionStrategy getStrategy(EmissionContext context) {
        if (context == null) {
            throw new IllegalArgumentException("Emission context cannot be null");
        }
        
        return getStrategy(context.product());
    }
    
    /**
     * Verifica se esiste una strategia per il tipo di emissione specificato.
     * 
     * @param emissionType Tipo di emissione da verificare
     * @return true se esiste una strategia per il tipo specificato
     */
    public boolean hasStrategy(String emissionType) {
        if (emissionType == null || emissionType.isBlank()) {
            return false;
        }
        
        return strategies.stream()
            .anyMatch(s -> s.getStrategyName().equalsIgnoreCase(emissionType));
    }
    
    /**
     * Restituisce tutte le strategie disponibili.
     * 
     * @return Array con i nomi di tutte le strategie disponibili
     */
    public String[] getAvailableStrategies() {
        return strategies.stream()
            .map(EmissionStrategy::getStrategyName)
            .toArray(String[]::new);
    }
}
