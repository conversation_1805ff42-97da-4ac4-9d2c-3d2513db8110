package it.yolo.v3.strategy;

import it.yolo.v3.domain.Policy;
import io.smallrye.mutiny.Uni;

/**
 * Interfaccia per le strategie di emissione delle polizze.
 * 
 * Implementa il Strategy Pattern per gestire diversi tipi di emissione:
 * - Emissione interna (gestita direttamente dal sistema)
 * - Emissione esterna (delegata a sistemi PG esterni)
 */
public interface EmissionStrategy {
    
    /**
     * Emette una polizza utilizzando la strategia specifica.
     * 
     * @param context Contesto contenente tutti i dati necessari per l'emissione
     * @return Uni con la polizza emessa
     */
    Uni<Policy> emit(EmissionContext context);
    
    /**
     * Restituisce il nome identificativo della strategia.
     * 
     * @return Nome della strategia (es. "INTERNAL", "EXTERNAL_ADP")
     */
    String getStrategyName();
    
    /**
     * Verifica se la strategia può gestire il contesto fornito.
     * 
     * @param context Contesto da verificare
     * @return true se la strategia può gestire il contesto
     */
    default boolean canHandle(EmissionContext context) {
        return getStrategyName().equalsIgnoreCase(context.getEmissionType());
    }
}
