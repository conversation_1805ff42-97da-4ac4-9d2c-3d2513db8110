package it.yolo.v3.strategy;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.PolicyClientV3;
import it.yolo.v3.dto.request.PolicyRequestDtoV3;
import it.yolo.v3.dto.request.DataPolicyRequestDtoV3;
import it.yolo.v3.dto.request.ProductRequestPolicyV3;
import it.yolo.v3.dto.request.CustomerRequestPolicyV3;
import it.yolo.v3.dto.response.PolicyResponseDtoV3;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Strategia per l'emissione interna delle polizze V3.
 * Gestisce l'emissione di polizze tramite il sistema interno.
 */
@ApplicationScoped
public class InternalEmissionStrategyV3 implements EmissionStrategyV3 {
    
    private static final Logger LOG = Logger.getLogger(InternalEmissionStrategyV3.class);
    
    @Inject
    @RestClient
    PolicyClientV3 policyClient;
    
    @ConfigProperty(name = "emission.internal.policy-prefix", defaultValue = "INT")
    String policyPrefix;
    
    @Override
    public Uni<PolicyResponseDtoV3> emit(EmissionContextV3 context) {
        LOG.infof("V3 - Starting internal emission for order %s", context.orderCode());
        
        // Validazioni specifiche per emissione interna
        validateInternalEmission(context);
        
        // Prepara la richiesta per il PolicyClient
        PolicyRequestDtoV3 policyRequest = buildPolicyRequest(context);
        
        // Chiama il servizio delle polizze
        return policyClient.create(context.token(), policyRequest)
            .onItem().invoke(policy -> 
                LOG.infof("V3 - Internal emission completed for order %s -> policy %s", 
                         context.orderCode(), policy.data().policyCode()))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Internal emission failed for order %s", context.orderCode()));
    }
    
    @Override
    public String getStrategyName() {
        return "INTERNAL";
    }
    
    @Override
    public boolean canHandle(String emissionType) {
        return "internal".equalsIgnoreCase(emissionType) || 
               "email".equalsIgnoreCase(emissionType); // Email usa strategia interna
    }
    
    private void validateInternalEmission(EmissionContextV3 context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for internal emission");
        }
        
        if (!context.isInternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for internal emission");
        }
        
        // Validazioni specifiche per emissione interna
        if (context.product().data().price() == null || 
            context.product().data().price() <= 0) {
            throw new IllegalArgumentException("Product price must be greater than zero for internal emission");
        }
        
        if (context.customer().data().id() == null) {
            throw new IllegalArgumentException("Customer ID is required for internal emission");
        }
    }
    
    private PolicyRequestDtoV3 buildPolicyRequest(EmissionContextV3 context) {
        var orderData = context.order().data();
        var customerData = context.customer().data();
        var productData = context.product().data();
        
        // Costruisce il prodotto per la richiesta
        ProductRequestPolicyV3 productRequest = new ProductRequestPolicyV3(
            productData.id().longValue(),
            productData.code(),
            productData.description()
        );
        
        // Costruisce il cliente per la richiesta
        CustomerRequestPolicyV3 customerRequest = new CustomerRequestPolicyV3(
            customerData.id().longValue(),
            customerData.customerCode(),
            customerData.name(),
            customerData.surname(),
            customerData.taxCode(),
            customerData.primaryMail(),
            customerData.primaryPhone()
        );
        
        // Costruisce i dati della polizza
        DataPolicyRequestDtoV3 dataPolicyRequest = new DataPolicyRequestDtoV3(
            null, // id sarà generato dal sistema
            context.request().paymentType(),
            context.orderCode(),
            orderData.id().longValue(),
            null, // policyCode sarà generato dal sistema
            String.valueOf(orderData.id()),
            context.request().subscriptionId(),
            productRequest,
            null, // packet non necessario per ora
            customerRequest,
            orderData.choosenProperties(),
            BigDecimal.valueOf(orderData.insurancePremium() != null ? orderData.insurancePremium() : 0.0),
            orderData.startDate(),
            orderData.expirationDate(),
            1L, // quantity default
            true, // insuredIsContractor default
            orderData.type(),
            false, // markedAsRenewable default
            false, // policySubstitution default
            null, // masterPolicyNumber
            null, // nextBillingDate
            customerData.name(),
            null, // username
            null, // password
            true, // isWithdrawable default
            true  // isDeactivable default
        );
        
        return new PolicyRequestDtoV3(dataPolicyRequest);
    }
}
