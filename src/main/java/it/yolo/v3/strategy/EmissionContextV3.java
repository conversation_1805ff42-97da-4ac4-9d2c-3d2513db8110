package it.yolo.v3.strategy;

import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.CustomerResponseDtoV3;
import it.yolo.v3.dto.response.OrderResponseDtoV3;
import it.yolo.v3.dto.response.ProductResponseDtoV3;

/**
 * Contesto di emissione V3 che contiene tutti i dati necessari per l'emissione di una polizza.
 * Utilizzato dalle strategie di emissione per accedere ai dati in modo uniforme.
 */
public record EmissionContextV3(
        String orderCode,
        String token,
        OrderResponseDtoV3 order,
        CustomerResponseDtoV3 customer,
        ProductResponseDtoV3 product,
        EmissionRequestV3 request
) {
    
    public EmissionContextV3 {
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
        if (token == null || token.isBlank()) {
            throw new IllegalArgumentException("Token cannot be null or blank");
        }
        if (order == null) {
            throw new IllegalArgumentException("Order cannot be null");
        }
        if (customer == null) {
            throw new IllegalArgumentException("Customer cannot be null");
        }
        if (product == null) {
            throw new IllegalArgumentException("Product cannot be null");
        }
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
    }
    
    /**
     * Verifica se il contesto è valido per l'emissione
     */
    public boolean isValidForEmission() {
        return order.data() != null && 
               customer.data() != null && 
               product.data() != null &&
               product.data().configuration() != null;
    }
    
    /**
     * Ottiene il tipo di emissione dal prodotto
     */
    public String getEmissionType() {
        if (product.data() == null || product.data().configuration() == null) {
            return "internal"; // default
        }
        String emissionType = product.data().configuration().emissionType();
        return emissionType != null ? emissionType : "internal";
    }
    
    /**
     * Verifica se il prodotto è configurato per emissione interna
     */
    public boolean isInternalEmission() {
        return "internal".equalsIgnoreCase(getEmissionType()) || 
               "email".equalsIgnoreCase(getEmissionType());
    }
    
    /**
     * Verifica se il prodotto è configurato per emissione esterna
     */
    public boolean isExternalEmission() {
        return "external".equalsIgnoreCase(getEmissionType());
    }
    
    /**
     * Ottiene il prefisso della polizza dal prodotto
     */
    public String getPolicyPrefix() {
        if (product.data() == null || product.data().configuration() == null) {
            return "POL"; // default
        }
        String prefix = product.data().configuration().policyPrefix();
        return prefix != null ? prefix : "POL";
    }
    
    /**
     * Verifica se il prodotto richiede la generazione del certificato
     */
    public boolean requiresCertificate() {
        if (product.data() == null || product.data().configuration() == null) {
            return true; // default
        }
        Boolean requires = product.data().configuration().requiresCertificate();
        return requires != null ? requires : true;
    }
}
