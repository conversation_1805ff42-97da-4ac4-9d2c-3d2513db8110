package it.yolo.v3.strategy;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.response.PolicyResponseDtoV3;

/**
 * Interfaccia per le strategie di emissione V3.
 * Definisce il contratto per l'emissione di polizze con diversi approcci.
 */
public interface EmissionStrategyV3 {
    
    /**
     * Emette una polizza utilizzando la strategia specifica.
     * 
     * @param context Contesto di emissione contenente tutti i dati necessari
     * @return Uni contenente la polizza emessa
     */
    Uni<PolicyResponseDtoV3> emit(EmissionContextV3 context);
    
    /**
     * Restituisce il nome identificativo della strategia.
     * 
     * @return Nome della strategia (es. "INTERNAL", "EXTERNAL", "EMAIL")
     */
    String getStrategyName();
    
    /**
     * Verifica se la strategia può gestire il tipo di emissione specificato.
     * 
     * @param emissionType Tipo di emissione da verificare
     * @return true se la strategia può gestire il tipo, false altrimenti
     */
    default boolean canHandle(String emissionType) {
        return getStrategyName().equalsIgnoreCase(emissionType);
    }
}
