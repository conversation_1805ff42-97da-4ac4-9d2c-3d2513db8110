package it.yolo.v3.strategy;

import it.yolo.v3.domain.Customer;
import it.yolo.v3.domain.Order;
import it.yolo.v3.domain.Product;

/**
 * Contesto per l'emissione delle polizze.
 * Contiene tutti i dati necessari per le strategie di emissione.
 */
public record EmissionContext(
    Order order,
    Customer customer,
    Product product,
    String token
) {
    
    public EmissionContext {
        if (order == null) {
            throw new IllegalArgumentException("Order cannot be null");
        }
        if (customer == null) {
            throw new IllegalArgumentException("Customer cannot be null");
        }
        if (product == null) {
            throw new IllegalArgumentException("Product cannot be null");
        }
        if (token == null || token.isBlank()) {
            throw new IllegalArgumentException("Token cannot be null or blank");
        }
    }
    
    /**
     * Verifica se il contesto è valido per l'emissione
     */
    public boolean isValidForEmission() {
        return order.isValidForEmission() && 
               customer.isValidForEmission() && 
               product.isValidForEmission();
    }
    
    /**
     * Restituisce il tipo di emissione basato sul prodotto
     */
    public String getEmissionType() {
        return product.emissionType();
    }
}
