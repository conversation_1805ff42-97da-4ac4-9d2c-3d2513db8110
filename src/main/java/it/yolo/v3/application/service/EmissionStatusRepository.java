package it.yolo.v3.application.service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import javax.enterprise.context.ApplicationScoped;

/**
 * In-memory emission status repository
 * In production this would be a database or cache
 */
@ApplicationScoped
public class EmissionStatusRepository {

    private final Map<String, EmissionStatus> statusMap = new ConcurrentHashMap<>();

    public void updateStatus(String orderCode, EmissionStatus status) {
        statusMap.put(orderCode, status);
    }

    public EmissionStatus getStatus(String orderCode) {
        return statusMap.getOrDefault(orderCode, EmissionStatus.NOT_FOUND);
    }

    public enum EmissionStatus {
        PENDING("Processo appena avviato"),
        EMITTED("Polizza emessa, task background avviati"), 
        CERTIFICATE_IN_PROGRESS("Generazione certificato in corso"),
        COMMUNICATION_IN_PROGRESS("Invio email e upload documenti"),
        COMPLETE("Processo completato con successo"),
        FAILED("Errore durante elaborazione background"),
        NOT_FOUND("Order not found");

        private final String description;

        EmissionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
