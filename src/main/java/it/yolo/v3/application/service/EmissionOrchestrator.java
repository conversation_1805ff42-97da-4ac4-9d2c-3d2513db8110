package it.yolo.v3.application.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.smallrye.mutiny.Uni;

import it.yolo.v3.domain.model.*;
import it.yolo.v3.domain.port.CustomerPort;
import it.yolo.v3.domain.port.OrderPort;
import it.yolo.v3.domain.port.ProductPort;
import it.yolo.v3.application.dto.EmissionRequestV3;
import it.yolo.v3.application.dto.EmissionResponseV3;
import it.yolo.v3.domain.strategy.EmissionStrategy;
import it.yolo.v3.domain.strategy.EmissionStrategyFactory;
import it.yolo.v3.service.CertificateService;
import it.yolo.v3.service.EmailService; 
import it.yolo.v3.service.DocumentService;

import org.jboss.logging.Logger;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@ApplicationScoped
public class EmissionOrchestrator {

    private static final Logger LOGGER = Logger.getLogger(EmissionOrchestrator.class);
    
    private static final String PAYMENT_STATE = "Confirmed";
    private static final String COMPLETE_STATE = "Completed";
    private static final String STATE_COMPLETE = "COMPLETE";

    @Inject
    CustomerPort customerPort;

    @Inject
    OrderPort orderPort;

    @Inject
    ProductPort productPort;

    @Inject
    EmissionStrategyFactory strategyFactory;

    @Inject
    PolicyService policyService;

    @Inject
    CertificateService certificateService;

    @Inject
    EmailService emailService;

    @Inject
    DocumentService documentService;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    BackgroundTaskRunner backgroundTaskRunner;

    @Inject
    EmissionStatusRepository statusRepository;

    // Fallback executor per Java 17 (VirtualThreads disponibili solo in Java 21+)
    private final Executor virtualThreadExecutor = Executors.newCachedThreadPool();

    @ConfigProperty(name = "technical.users")
    java.util.List<String> technicalUsers;

    /**
     * V3 EMISSION FLOW secondo API_V3_EMISSION_GUIDE.md
     * 
     * Esegue l'emissione in due fasi:
     * 1. Fase Sincrona (< 500ms): Emissione polizza immediata
     * 2. Fase Asincrona (background): Certificato + comunicazioni parallele
     */
    public Uni<EmissionResponseV3> processEmissionV3(String orderCode, EmissionRequestV3 request, String token) {
        LOGGER.infof("V3 - Starting emission process for order: %s", orderCode);
        
        // Stato iniziale: PENDING
        statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.PENDING);
        
        return Uni.createFrom().item(() -> {
            // Step 1: Update order with payment info
            return updateOrderWithPayment(orderCode, request, token);
        })
        .runSubscriptionOn(virtualThreadExecutor)
        .flatMap(order -> {
            // Step 2: Parallel data loading  
            return loadEmissionData(order, token);
        })
        .flatMap(emissionData -> {
            // Step 3: Execute emission strategy (SYNCHRONOUS PHASE)
            return executeEmissionStrategy(emissionData)
                .onItem().transform(emissionResult -> {
                    
                    // Aggiorna stato dopo emissione polizza sincrona
                    statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.EMITTED);
                    
                    // Prepara risposta V3 immediata
                    EmissionResponseV3 response = EmissionResponseV3.success(
                        emissionData.order(), 
                        emissionResult.policy(), 
                        null  // certificate will be generated in background
                    );
                    
                    // Avvia fase asincrona (fire-and-forget)
                    executeV3BackgroundTasks(orderCode, emissionResult)
                        .subscribe()
                        .with(
                            success -> LOGGER.infof("V3 - Background tasks completed for order: %s", orderCode),
                            failure -> {
                                LOGGER.errorf(failure, "V3 - Background tasks failed for order: %s", orderCode);
                                statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.FAILED);
                            }
                        );
                    
                    return response;
                });
        })
        .onFailure().invoke(failure -> {
            LOGGER.errorf(failure, "V3 - Emission failed for order: %s", orderCode);
            statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.FAILED);
        });
    }

    /**
     * FASE ASINCRONA V3 - Background tasks secondo la guida
     */
    private Uni<Void> executeV3BackgroundTasks(String orderCode, EmissionResult emissionResult) {
        return Uni.createFrom().voidItem()
            .onItem().transformToUni(ignored -> {
                
                LOGGER.infof("V3 - Starting background tasks for order: %s", orderCode);
                
                // Step 1: CERTIFICATE_IN_PROGRESS → Genera certificato
                statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.CERTIFICATE_IN_PROGRESS);
                LOGGER.debugf("V3 - Generating certificate for order: %s", orderCode);
                
                // Integrazione CertificateService nella fase asincrona
                return Uni.createFrom().item(() -> {
                    try {
                        // Genera certificato reale usando il service
                        String policyCode = emissionResult.policy().policyCode();
                        LOGGER.debugf("V3 - Generating certificate for order: %s, policy: %s", orderCode, policyCode);
                        
                        // TODO: Fare chiamata asincrona reale al certificate service
                        // Certificate cert = certificateService.generate(emissionResult.policy(), token).await().indefinitely();
                        
                        // Simulazione per ora
                        Thread.sleep(800); // Tempo medio certificato dalla guida (800ms)
                        
                        LOGGER.debugf("V3 - Certificate generated for order: %s", orderCode);
                        return null;
                    } catch (Exception e) {
                        LOGGER.errorf(e, "V3 - Certificate generation failed for order: %s", orderCode);
                        throw new RuntimeException("Certificate generation failed", e);
                    }
                });
            })
            .onItem().transformToUni(ignored -> {
                
                // Step 2: COMMUNICATION_IN_PROGRESS → Task paralleli
                statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.COMMUNICATION_IN_PROGRESS);
                LOGGER.debugf("V3 - Executing parallel communication tasks for order: %s", orderCode);
                
                // Task paralleli: Email + Document upload reali
                Uni<Void> emailTask = Uni.createFrom().item(() -> {
                    try {
                        LOGGER.debugf("V3 - Sending email for order: %s", orderCode);
                        // TODO: emailService.sendPolicyEmail(emissionResult.policy(), emissionData.customer());
                        Thread.sleep(500); // Simula email (500ms avg dalla guida)
                        LOGGER.debugf("V3 - Email sent for order: %s", orderCode);
                        return null;
                    } catch (Exception e) {
                        LOGGER.errorf(e, "V3 - Email task failed for order: %s", orderCode);
                        throw new RuntimeException("Email task failed", e);
                    }
                });
                
                Uni<Void> documentTask = Uni.createFrom().item(() -> {
                    try {
                        LOGGER.debugf("V3 - Uploading documents for order: %s", orderCode);
                        // TODO: documentService.uploadPolicyDocuments(emissionResult.policy());
                        Thread.sleep(600); // Simula upload documenti (600ms avg dalla guida)
                        LOGGER.debugf("V3 - Documents uploaded for order: %s", orderCode);
                        return null;
                    } catch (Exception e) {
                        LOGGER.errorf(e, "V3 - Document task failed for order: %s", orderCode);
                        throw new RuntimeException("Document task failed", e);
                    }
                });
                
                // Esegui task in parallelo
                return Uni.combine().all().unis(emailTask, documentTask)
                    .discardItems();
            })
            .onItem().invoke(ignored -> {
                
                // Step 3: COMPLETE → Finalizzazione
                LOGGER.debugf("V3 - Finalizing order: %s", orderCode);
                // TODO: orderPort.markAsComplete(orderCode);
                
                statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.COMPLETE);
                LOGGER.infof("V3 - All background tasks completed for order: %s", orderCode);
            })
            .onFailure().invoke(failure -> {
                LOGGER.errorf(failure, "V3 - Background tasks failed for order: %s", orderCode);
                statusRepository.updateStatus(orderCode, EmissionStatusRepository.EmissionStatus.FAILED);
            })
            .runSubscriptionOn(virtualThreadExecutor);
    }

    /**
     * Main emission flow - esatta copia del legacy ServiceEmissionManager.emission()
     */
    public Uni<EmissionResponseV3> processEmission(String orderCode, EmissionRequestV3 request, String token) {
        LOGGER.infof("Starting emission process for order: %s", orderCode);
        
        return Uni.createFrom().item(() -> {
            // Step 1: Update order with payment info - come nel legacy
            return updateOrderWithPayment(orderCode, request, token);
        })
        .runSubscriptionOn(virtualThreadExecutor)
        .flatMap(order -> {
            // Step 2: Parallel data loading - come nel legacy
            return loadEmissionData(order, token);
        })
        .flatMap(emissionData -> {
            // Step 3: Execute emission strategy - basato su ServiceEmission logic
            return executeEmissionStrategy(emissionData);
        })
        .flatMap(emissionResult -> {
            // Step 4: Complete order update - come nel legacy
            return completeOrderUpdate(emissionResult);
        })
        .flatMap(emissionResult -> {
            // Step 5: Send email async (fire-and-forget) - come nel legacy
            backgroundTaskRunner.sendEmailAsync(emissionResult);
            
            // Convert to V3 response DTO
            EmissionResponseV3 response = EmissionResponseV3.success(
                emissionResult.order(), 
                emissionResult.policy(), 
                emissionResult.certificate()
            );
            
            return Uni.createFrom().item(response);
        })
        .onFailure().invoke(throwable -> 
            LOGGER.errorf(throwable, "Emission failed for order: %s", orderCode)
        );
    }

    /**
     * Step 1: Update order with payment token - copia da ServiceEmissionManager
     */
    private Order updateOrderWithPayment(String orderCode, EmissionRequestV3 request, String token) {
        try {
            LOGGER.infof("Updating order with payment info: %s", orderCode);
            
            // Create update request exactly like legacy
            var updateRequest = new it.yolo.client.order.dto.request.OrderRequestDto();
            var dataRequest = new it.yolo.client.order.dto.request.DataDtoRequest();
            
            dataRequest.setOrderCode(orderCode);
            dataRequest.setPaymentToken(request.paymentToken());
            dataRequest.setPaymentType(request.paymentType());
            
            // Convert String to Integer for paymentTransactionId
            if (request.paymentTransactionId() != null && !request.paymentTransactionId().trim().isEmpty()) {
                try {
                    dataRequest.setPaymentTransactionId(Integer.valueOf(request.paymentTransactionId()));
                } catch (NumberFormatException e) {
                    LOGGER.warnf("Invalid paymentTransactionId format: %s", request.paymentTransactionId());
                }
            }
            
            dataRequest.setDiscount(request.discount());
            dataRequest.setAnagState(PAYMENT_STATE);
            
            updateRequest.setDataDtoRequest(dataRequest);
            
            // Check if technical user like legacy
            boolean isTechnical = checkTechnicalUser();
            
            // Call legacy order client
            if (isTechnical) {
                orderPort.updateOrderUnchecked(token, updateRequest, orderCode);
                return orderPort.getOrderUnchecked(token, orderCode);
            } else {
                orderPort.updateOrder(token, updateRequest, orderCode);
                return orderPort.getOrder(token, orderCode);
            }
            
        } catch (Exception e) {
            LOGGER.errorf(e, "EmissionFlowException while updating order: %s", orderCode);
            throw new RuntimeException("EmissionFlowException while updating order", e);
        }
    }

    /**
     * Step 2: Load all emission data in parallel - come nel legacy
     */
    private Uni<EmissionData> loadEmissionData(Order order, String token) {
        LOGGER.infof("Loading emission data for order: %s", order.orderCode());
        
        // Parallel loading like legacy
        Uni<Customer> customerUni = customerPort.findById(token, order.customerId().longValue());
        Uni<Product> productUni = productPort.findById(Long.valueOf(order.productId()), order.language());
        
        return Uni.combine().all().unis(customerUni, productUni)
            .asTuple()
            .map(tuple -> {
                Customer customer = tuple.getItem1();
                Product product = tuple.getItem2();
                
                // Check existing policy if needed - come nel legacy
                if (product.properties() != null && 
                    product.properties().has("existingCheck") && 
                    product.properties().get("existingCheck").asBoolean()) {
                    
                    try {
                        JsonNode check = orderPort.checkExistingPolicy(order.orderCode(), token);
                        String msg = check.get("data").asText();
                        if ("ERROR_EXISTING_POLICY".equals(msg)) {
                            throw new RuntimeException(msg);
                        }
                    } catch (Exception e) {
                        LOGGER.errorf(e, "Error checking existing policy for order: %s", order.orderCode());
                        throw new RuntimeException("Error checking existing policy", e);
                    }
                }
                
                return new EmissionData(order, customer, product, token);
            });
    }

    /**
     * Step 3: Execute emission strategy - basato su ServiceEmission
     */
    private Uni<EmissionResult> executeEmissionStrategy(EmissionData data) {
        LOGGER.infof("Executing emission strategy for product: %s", data.product.code());
        
        return Uni.createFrom().item(() -> {
            // Get strategy based on product emission type - come nel legacy
            EmissionStrategy strategy = strategyFactory.getStrategy(data.product.emission());
            
            // Execute emission with strategy
            return strategy.executeEmission(data);
        })
        .runSubscriptionOn(virtualThreadExecutor);
    }

    /**
     * Step 4: Complete order update - come nel legacy
     */
    private Uni<EmissionResult> completeOrderUpdate(EmissionResult emissionResult) {
        LOGGER.infof("Completing order update: %s", emissionResult.order().orderCode());
        
        return Uni.createFrom().item(() -> {
            try {
                // Update order to completed state like legacy
                var updateRequest = new it.yolo.client.order.dto.request.OrderRequestDto();
                var dataRequest = new it.yolo.client.order.dto.request.DataDtoRequest();
                
                dataRequest.setOrderCode(emissionResult.order().orderCode());
                dataRequest.setAnagState(COMPLETE_STATE);
                dataRequest.setResPg(emissionResult.pgResponse());
                
                updateRequest.setDataDtoRequest(dataRequest);
                
                boolean isTechnical = checkTechnicalUser();
                Order completedOrder = null;
                
                // Retry logic like legacy
                while (completedOrder == null || 
                       !completedOrder.orderItems().get(0).state().equalsIgnoreCase(STATE_COMPLETE)) {
                    
                    if (isTechnical) {
                        completedOrder = orderPort.updateOrderUnchecked(
                            emissionResult.token(), updateRequest, emissionResult.order().orderCode());
                    } else {
                        completedOrder = orderPort.updateOrder(
                            emissionResult.token(), updateRequest, emissionResult.order().orderCode());
                    }
                }
                
                // Align order if intermediary like legacy
                if (emissionResult.order().intermediaryOrder() != null && 
                    emissionResult.order().intermediaryOrder()) {
                    
                    // Call YIN client alignment (legacy logic)
                    orderPort.alignOrder(emissionResult.order().orderCode());
                }
                
                LOGGER.infof("Order completed: %s. Emission flow ok", emissionResult.order().orderCode());
                
                return new EmissionResult(
                    completedOrder,
                    emissionResult.customer(),
                    emissionResult.product(),
                    emissionResult.policy(),
                    emissionResult.certificate(),
                    emissionResult.pgResponse(),
                    emissionResult.token()
                );
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Exception while updating order: %s", emissionResult.order().orderCode());
                throw new RuntimeException("Exception while updating order", e);
            }
        })
        .runSubscriptionOn(virtualThreadExecutor);
    }

    /**
     * Check if current user is technical user - copia dal legacy
     */
    private boolean checkTechnicalUser() {
        try {
            java.util.List<javax.json.JsonString> groupsJson = jsonWebToken.getClaim("cognito:groups");
            java.util.List<String> groups = new java.util.ArrayList<>();
            
            if (groupsJson != null) {
                for (javax.json.JsonString group : groupsJson) {
                    groups.add(group.getString());
                }
            }
            
            return technicalUsers.containsAll(groups);
        } catch (Exception e) {
            LOGGER.warn("Error checking technical user", e);
            return false;
        }
    }

    /**
     * Data container for emission process
     */
    public record EmissionData(
        Order order,
        Customer customer,
        Product product,
        String token
    ) {}

    /**
     * Result container for emission process
     */
    public record EmissionResult(
        Order order,
        Customer customer,
        Product product,
        Policy policy,
        Certificate certificate,
        JsonNode pgResponse,
        String token
    ) {}
}
