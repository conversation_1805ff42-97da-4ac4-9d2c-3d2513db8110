package it.yolo.v3.application.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.application.service.EmissionOrchestrator.EmissionResult;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Background task runner for async operations
 */
@ApplicationScoped
public class BackgroundTaskRunner {

    private static final Logger LOGGER = Logger.getLogger(BackgroundTaskRunner.class);

    @Inject
    EmailService emailService;

    @Inject
    DocumentService documentService;

    // Use regular executor for now - in Java 21+ this would be virtual threads
    private final Executor virtualThreadExecutor = Executors.newCachedThreadPool();

    /**
     * Send email async (fire-and-forget) - come nel legacy
     */
    public void sendEmailAsync(EmissionResult emissionResult) {
        LOGGER.infof("Starting async email task for order: %s", emissionResult.order().orderCode());
        
        Uni.createFrom().item(() -> {
            try {
                // Send email
                emailService.sendEmissionEmail(emissionResult).await().indefinitely();
                
                // Add documents if needed
                documentService.addDocumentsToPolicy(emissionResult).await().indefinitely();
                
                LOGGER.infof("Async email task completed for order: %s", 
                    emissionResult.order().orderCode());
                
                return null;
            } catch (Exception e) {
                LOGGER.errorf(e, "Async email task failed for order: %s", 
                    emissionResult.order().orderCode());
                // Don't rethrow - this is fire-and-forget
                return null;
            }
        })
        .runSubscriptionOn(virtualThreadExecutor)
        .subscribe()
        .with(
            result -> LOGGER.debugf("Email task subscription completed"),
            failure -> LOGGER.errorf(failure, "Email task subscription failed")
        );
    }
}
