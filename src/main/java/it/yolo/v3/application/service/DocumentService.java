package it.yolo.v3.application.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.application.service.EmissionOrchestrator.EmissionResult;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Document service - basato su DocumentClient legacy
 */
@ApplicationScoped
public class DocumentService {

    private static final Logger LOGGER = Logger.getLogger(DocumentService.class);

    /**
     * Add documents to policy - basato su ServiceEmissionManager logic
     */
    public Uni<Void> addDocumentsToPolicy(EmissionResult emissionResult) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.infof("Adding documents to policy: %s", emissionResult.policy().policyCode());
                
                // Check if documents should be attached based on product configuration
                var product = emissionResult.product();
                if (product.properties() != null &&
                    product.properties().has("attachDocumentation") &&
                    product.properties().get("attachDocumentation").has("emission")) {
                    
                    LOGGER.infof("Attaching documentation for policy: %s", emissionResult.policy().policyCode());
                    
                    // Call legacy document manager client
                    // This would be implemented with actual document attachment logic
                    // For now, just log the action
                    
                } else {
                    LOGGER.infof("No documentation attachment required for policy: %s", 
                        emissionResult.policy().policyCode());
                }
                
                return null;
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to add documents to policy: %s", 
                    emissionResult.policy().policyCode());
                throw new RuntimeException("Failed to add documents to policy", e);
            }
        });
    }
}
