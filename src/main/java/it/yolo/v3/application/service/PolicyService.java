package it.yolo.v3.application.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.Policy;
import it.yolo.client.policy.PolicyClient;
import it.yolo.service.ServicePolicy;

import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Policy service - basato su ServicePolicy legacy
 */
@ApplicationScoped
public class PolicyService {

    private static final Logger LOGGER = Logger.getLogger(PolicyService.class);

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    ServicePolicy legacyPolicyService;

    /**
     * Update policy after emission - copia da ServiceEmissionManager
     */
    public Uni<Policy> updateAfterEmission(Policy policy, String certificateFilename, 
                                         String certificateLink, String certificateType, 
                                         String productCode, String startDate) {
        
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.infof("Updating policy after emission: %s", policy.policyCode());
                
                // Create legacy emission response DTO for update
                it.yolo.emission.dto.response.EmissionResponseDto legacyEmissionResponse = 
                    new it.yolo.emission.dto.response.EmissionResponseDto();
                
                // Create legacy policy response DTO
                it.yolo.client.policy.dto.response.PolicyResponseDto legacyPolicyResponse = 
                    new it.yolo.client.policy.dto.response.PolicyResponseDto();
                
                var legacyPolicyData = new it.yolo.client.policy.dto.response.DataPolicyResponse();
                legacyPolicyData.setId(policy.id());
                legacyPolicyData.setPolicyCode(policy.policyCode());
                legacyPolicyData.setStartDate(policy.startDate());
                legacyPolicyData.setEndDate(policy.endDate());
                
                legacyPolicyResponse.setData(legacyPolicyData);
                legacyEmissionResponse.setPolicyResponseDto(legacyPolicyResponse);
                
                // Call legacy service
                var updatedLegacyPolicy = legacyPolicyService.updateAfterEmission(
                    legacyEmissionResponse, 
                    certificateFilename, 
                    certificateLink, 
                    certificateType, 
                    productCode, 
                    startDate
                );
                
                // Convert back to V3 model
                return Policy.fromLegacyPolicy(updatedLegacyPolicy);
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to update policy after emission: %s", policy.policyCode());
                throw new RuntimeException("Failed to update policy after emission", e);
            }
        });
    }

    /**
     * Get next policy number - copia da ServicePolicy
     */
    public Uni<Integer> getNextPolicy(String productCode, String token) {
        return Uni.createFrom().item(() -> {
            try {
                return legacyPolicyService.getNextPolicy(productCode, token);
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to get next policy number for product: %s", productCode);
                throw new RuntimeException("Failed to get next policy number", e);
            }
        });
    }

    /**
     * Read policy by code
     */
    public Uni<Policy> readPolicyByCode(String token, String policyCode) {
        return Uni.createFrom().item(() -> {
            try {
                var legacyPolicy = legacyPolicyService.read(policyCode, token);
                // Convert JsonNode to Policy model
                // For now return a basic Policy model
                return new Policy(
                    null, // ID not available from JsonNode
                    policyCode,
                    null, // startDate
                    null, // endDate
                    null, // status
                    null, // premium
                    null, // paymentFrequency
                    null, // customerId
                    null, // productId
                    null, // orderId
                    null, // certificateLink
                    legacyPolicy // Store full JsonNode as details
                );
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to read policy by code: %s", policyCode);
                throw new RuntimeException("Failed to read policy by code", e);
            }
        });
    }
}
