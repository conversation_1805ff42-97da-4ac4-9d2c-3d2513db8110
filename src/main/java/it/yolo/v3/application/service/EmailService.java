package it.yolo.v3.application.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.*;
import it.yolo.v3.application.service.EmissionOrchestrator.EmissionResult;
import it.yolo.service.ServiceCommunicationManager;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Email service - basato su ServiceCommunicationManager legacy
 */
@ApplicationScoped
public class EmailService {

    private static final Logger LOGGER = Logger.getLogger(EmailService.class);

    @Inject
    ServiceCommunicationManager legacyCommunicationService;

    /**
     * Send emission email - basato su ServiceEmissionManager.certificationToCommunicationRequest()
     */
    public Uni<Void> sendEmissionEmail(EmissionResult emissionResult) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.infof("Sending emission email for order: %s", 
                    emissionResult.order().orderCode());
                
                Product product = emissionResult.product();
                
                // Check if email is required (come nel legacy)
                if (product.properties() != null && 
                    product.properties().has("emailNotRequired") &&
                    product.properties().get("emailNotRequired").asText().equalsIgnoreCase("true")) {
                    
                    LOGGER.infof("Email not required for product: %s", product.code());
                    return null;
                }
                
                CommunicationManagerDtoRequest emailRequest;
                String certificateType = product.certificate();
                
                if ("external_generation_async".equalsIgnoreCase(certificateType) || 
                    "none".equalsIgnoreCase(certificateType)) {
                    
                    LOGGER.info("Sending email without certificate...");
                    emailRequest = createEmailRequestWithoutCertificate(emissionResult);
                } else {
                    LOGGER.info("Sending email with certificate...");
                    emailRequest = createEmailRequestWithCertificate(emissionResult);
                }
                
                // Call legacy service
                legacyCommunicationService.sendEmail(emailRequest);
                
                LOGGER.infof("Email sent successfully for order: %s", 
                    emissionResult.order().orderCode());
                
                return null;
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to send emission email for order: %s", 
                    emissionResult.order().orderCode());
                throw new RuntimeException("Failed to send emission email", e);
            }
        });
    }

    /**
     * Create email request with certificate - basato su legacy logic
     */
    private CommunicationManagerDtoRequest createEmailRequestWithCertificate(EmissionResult result) {
        // For now, create a minimal email request
        // In production, this would use the template mapping logic from legacy
        
        CommunicationManagerDtoRequest request = new CommunicationManagerDtoRequest();
        
        // Set basic properties - the actual implementation would use legacy template system
        // This is a placeholder implementation
        LOGGER.infof("Creating email with certificate for policy: %s", result.policy().policyCode());
        
        return request;
    }

    /**
     * Create email request without certificate - basato su legacy logic
     */
    private CommunicationManagerDtoRequest createEmailRequestWithoutCertificate(EmissionResult result) {
        CommunicationManagerDtoRequest request = new CommunicationManagerDtoRequest();
        
        // Set basic properties - the actual implementation would use legacy template system
        // This is a placeholder implementation
        LOGGER.infof("Creating email without certificate for policy: %s", result.policy().policyCode());
        
        return request;
    }
}
