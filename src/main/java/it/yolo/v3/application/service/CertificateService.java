package it.yolo.v3.application.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.Certificate;
import it.yolo.v3.domain.model.Policy;
import it.yolo.v3.domain.model.Product;
import it.yolo.client.document.DocumentClient;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;

import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

/**
 * Certificate service - basato su DocumentClient legacy
 */
@ApplicationScoped
public class CertificateService {

    private static final Logger LOGGER = Logger.getLogger(CertificateService.class);

    @Inject
    @RestClient
    DocumentClient documentClient;

    /**
     * Generate certificate - basato su ServiceEmissionManager.generateCertificate()
     */
    public Uni<Certificate> generateCertificate(CertificateRequestDto request) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.infof("Generating certificate for policy: %s", 
                    request.getPolicy().getData().getPolicyCode());
                
                Response response = documentClient.generateCertficate(request);
                CertificateResponseDto certificateResponse = response.readEntity(CertificateResponseDto.class);
                
                return Certificate.fromLegacyCertificate(certificateResponse);
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Certificate generation failed");
                throw new RuntimeException("Certificate generation failed", e);
            }
        });
    }

    /**
     * Check if certificate should be generated based on product configuration
     */
    public boolean shouldGenerateCertificate(Product product) {
        String certificateType = product.certificate();
        return certificateType != null && !"none".equalsIgnoreCase(certificateType);
    }

    /**
     * Get certificate type from product
     */
    public String getCertificateType(Product product) {
        return product.certificate() != null ? product.certificate() : "internal";
    }
}
