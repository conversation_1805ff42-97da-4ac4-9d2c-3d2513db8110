package it.yolo.v3.application.dto;

import it.yolo.v3.domain.model.*;

public record EmissionResponseV3(
    String orderCode,
    String policyNumber,  // Campo principale secondo la guida
    String state,         // Stato del processo  
    String details,       // Dettagli aggiuntivi
    String updatedAt      // Timestamp ultimo aggiornamento
) {
    
    public static EmissionResponseV3 success(Order order, Policy policy, Certificate certificate) {
        return new EmissionResponseV3(
            order.orderCode(),
            policy.policyCode(),
            "EMITTED",
            "Policy successfully emitted",
            java.time.LocalDateTime.now().toString()
        );
    }
    
    public static EmissionResponseV3 pending(String orderCode) {
        return new EmissionResponseV3(
            orderCode,
            null,
            "PENDING",
            "Emission process started",
            java.time.LocalDateTime.now().toString()
        );
    }
    
    public static EmissionResponseV3 error(String orderCode, String errorMessage) {
        return new EmissionResponseV3(
            orderCode,
            null,
            "FAILED",
            errorMessage,
            java.time.LocalDateTime.now().toString()
        );
    }
}
