package it.yolo.v3.application.dto;

public record EmissionRequestV3(
    String paymentToken,
    String paymentType,
    String paymentTransactionId,
    String discount,
    String subscriptionId
) {
    
    public EmissionRequestV3 {
        if (paymentToken == null || paymentToken.trim().isEmpty()) {
            throw new IllegalArgumentException("Payment token cannot be null or empty");
        }
    }
    
    public static EmissionRequestV3 fromLegacyRequest(it.yolo.model.request.EmissionManagerRequest legacyRequest) {
        var data = legacyRequest.getData();
        return new EmissionRequestV3(
            data.getPaymentToken(),
            data.getPaymentType(),
            data.getPaymentTransactionId() != null ? data.getPaymentTransactionId().toString() : null,
            data.getDiscount(),
            data.getSubscriptionId()
        );
    }
}
