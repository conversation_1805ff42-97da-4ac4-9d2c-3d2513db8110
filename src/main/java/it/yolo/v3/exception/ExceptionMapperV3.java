package it.yolo.v3.exception;

import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import java.time.Instant;

/**
 * Mapper per la gestione centralizzata delle eccezioni V3
 */
@Provider
public class ExceptionMapperV3 implements ExceptionMapper<EmissionExceptionV3> {
    
    private static final Logger LOG = Logger.getLogger(ExceptionMapperV3.class);
    
    @Override
    public Response toResponse(EmissionExceptionV3 exception) {
        LOG.errorf(exception, "V3 - Handling emission exception: %s", exception.getMessage());
        
        ErrorResponseV3 errorResponse = new ErrorResponseV3(
            exception.getErrorCode(),
            exception.getMessage(),
            exception.getOrderCode(),
            Instant.now()
        );
        
        Response.Status status = mapToHttpStatus(exception);
        
        return RestResponse.status(status)
            .entity(errorResponse)
            .type(MediaType.APPLICATION_JSON)
            .build();
    }
    
    private Response.Status mapToHttpStatus(EmissionExceptionV3 exception) {
        return switch (exception.getErrorCode()) {
            case "ORDER_NOT_FOUND", "CUSTOMER_NOT_FOUND", "PRODUCT_NOT_FOUND" -> Response.Status.NOT_FOUND;
            case "VALIDATION_ERROR" -> Response.Status.BAD_REQUEST;
            case "STRATEGY_ERROR" -> Response.Status.UNPROCESSABLE_ENTITY;
            case "POLICY_ERROR", "CERTIFICATE_ERROR", "EMAIL_ERROR", "EXTERNAL_PG_ERROR" -> Response.Status.INTERNAL_SERVER_ERROR;
            default -> Response.Status.INTERNAL_SERVER_ERROR;
        };
    }
    
    /**
     * Record per la risposta di errore
     */
    public record ErrorResponseV3(
        String errorCode,
        String message,
        String orderCode,
        Instant timestamp
    ) {}
}

/**
 * Mapper per eccezioni generiche
 */
@Provider
class GenericExceptionMapperV3 implements ExceptionMapper<Exception> {
    
    private static final Logger LOG = Logger.getLogger(GenericExceptionMapperV3.class);
    
    @Override
    public Response toResponse(Exception exception) {
        LOG.errorf(exception, "V3 - Handling generic exception: %s", exception.getMessage());
        
        ExceptionMapperV3.ErrorResponseV3 errorResponse = new ExceptionMapperV3.ErrorResponseV3(
            "INTERNAL_ERROR",
            "An internal error occurred",
            null,
            Instant.now()
        );
        
        return RestResponse.status(Response.Status.INTERNAL_SERVER_ERROR)
            .entity(errorResponse)
            .type(MediaType.APPLICATION_JSON)
            .build();
    }
}

/**
 * Mapper per eccezioni di validazione
 */
@Provider
class ValidationExceptionMapperV3 implements ExceptionMapper<javax.validation.ValidationException> {
    
    private static final Logger LOG = Logger.getLogger(ValidationExceptionMapperV3.class);
    
    @Override
    public Response toResponse(javax.validation.ValidationException exception) {
        LOG.warnf("V3 - Validation error: %s", exception.getMessage());
        
        ExceptionMapperV3.ErrorResponseV3 errorResponse = new ExceptionMapperV3.ErrorResponseV3(
            "VALIDATION_ERROR",
            "Request validation failed: " + exception.getMessage(),
            null,
            Instant.now()
        );
        
        return RestResponse.status(Response.Status.BAD_REQUEST)
            .entity(errorResponse)
            .type(MediaType.APPLICATION_JSON)
            .build();
    }
}
