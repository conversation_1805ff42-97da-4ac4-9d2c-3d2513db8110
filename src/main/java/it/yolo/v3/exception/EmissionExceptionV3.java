package it.yolo.v3.exception;

/**
 * Eccezione base per errori di emissione V3
 */
public class EmissionExceptionV3 extends RuntimeException {
    
    private final String errorCode;
    private final String orderCode;
    
    public EmissionExceptionV3(String message) {
        super(message);
        this.errorCode = "EMISSION_ERROR";
        this.orderCode = null;
    }
    
    public EmissionExceptionV3(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.orderCode = null;
    }
    
    public EmissionExceptionV3(String message, String errorCode, String orderCode) {
        super(message);
        this.errorCode = errorCode;
        this.orderCode = orderCode;
    }
    
    public EmissionExceptionV3(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "EMISSION_ERROR";
        this.orderCode = null;
    }
    
    public EmissionExceptionV3(String message, String errorCode, String orderCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.orderCode = orderCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getOrderCode() {
        return orderCode;
    }
}

/**
 * Eccezione per ordini non trovati
 */
class OrderNotFoundExceptionV3 extends EmissionExceptionV3 {
    public OrderNotFoundExceptionV3(String orderCode) {
        super("Order not found: " + orderCode, "ORDER_NOT_FOUND", orderCode);
    }
}

/**
 * Eccezione per clienti non trovati
 */
class CustomerNotFoundExceptionV3 extends EmissionExceptionV3 {
    public CustomerNotFoundExceptionV3(String customerId, String orderCode) {
        super("Customer not found: " + customerId, "CUSTOMER_NOT_FOUND", orderCode);
    }
}

/**
 * Eccezione per prodotti non trovati
 */
class ProductNotFoundExceptionV3 extends EmissionExceptionV3 {
    public ProductNotFoundExceptionV3(String productId, String orderCode) {
        super("Product not found: " + productId, "PRODUCT_NOT_FOUND", orderCode);
    }
}

/**
 * Eccezione per errori di validazione
 */
class ValidationExceptionV3 extends EmissionExceptionV3 {
    public ValidationExceptionV3(String message, String orderCode) {
        super(message, "VALIDATION_ERROR", orderCode);
    }
}

/**
 * Eccezione per errori di strategia
 */
class StrategyExceptionV3 extends EmissionExceptionV3 {
    public StrategyExceptionV3(String message, String orderCode) {
        super(message, "STRATEGY_ERROR", orderCode);
    }
}

/**
 * Eccezione per errori di polizza
 */
class PolicyExceptionV3 extends EmissionExceptionV3 {
    public PolicyExceptionV3(String message, String orderCode) {
        super(message, "POLICY_ERROR", orderCode);
    }
    
    public PolicyExceptionV3(String message, String orderCode, Throwable cause) {
        super(message, "POLICY_ERROR", orderCode, cause);
    }
}

/**
 * Eccezione per errori di certificato
 */
class CertificateExceptionV3 extends EmissionExceptionV3 {
    public CertificateExceptionV3(String message, String orderCode) {
        super(message, "CERTIFICATE_ERROR", orderCode);
    }
    
    public CertificateExceptionV3(String message, String orderCode, Throwable cause) {
        super(message, "CERTIFICATE_ERROR", orderCode, cause);
    }
}

/**
 * Eccezione per errori di email
 */
class EmailExceptionV3 extends EmissionExceptionV3 {
    public EmailExceptionV3(String message, String orderCode) {
        super(message, "EMAIL_ERROR", orderCode);
    }
    
    public EmailExceptionV3(String message, String orderCode, Throwable cause) {
        super(message, "EMAIL_ERROR", orderCode, cause);
    }
}

/**
 * Eccezione per errori di PG esterno
 */
class ExternalPGExceptionV3 extends EmissionExceptionV3 {
    public ExternalPGExceptionV3(String message, String orderCode) {
        super(message, "EXTERNAL_PG_ERROR", orderCode);
    }
    
    public ExternalPGExceptionV3(String message, String orderCode, Throwable cause) {
        super(message, "EXTERNAL_PG_ERROR", orderCode, cause);
    }
}
