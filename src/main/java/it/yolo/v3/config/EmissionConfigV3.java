package it.yolo.v3.config;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.Duration;

/**
 * Configurazione centralizzata per l'emissione V3
 */
@ApplicationScoped
public class EmissionConfigV3 {
    
    // Configurazioni generali
    @Inject
    @ConfigProperty(name = "emission.v3.enabled", defaultValue = "true")
    boolean emissionEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.v3.max-concurrent-emissions", defaultValue = "100")
    int maxConcurrentEmissions;
    
    @Inject
    @ConfigProperty(name = "emission.v3.timeout-seconds", defaultValue = "30")
    int timeoutSeconds;
    
    // Configurazioni per emissione interna
    @Inject
    @ConfigProperty(name = "emission.internal.policy-prefix", defaultValue = "INT")
    String internalPolicyPrefix;
    
    @Inject
    @ConfigProperty(name = "emission.internal.enabled", defaultValue = "true")
    boolean internalEmissionEnabled;
    
    // Configurazioni per emissione esterna
    @Inject
    @ConfigProperty(name = "emission.external.policy-prefix", defaultValue = "EXT")
    String externalPolicyPrefix;
    
    @Inject
    @ConfigProperty(name = "emission.external.enabled", defaultValue = "true")
    boolean externalEmissionEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.external.timeout-ms", defaultValue = "10000")
    int externalTimeoutMs;
    
    @Inject
    @ConfigProperty(name = "emission.external.retry-attempts", defaultValue = "3")
    int externalRetryAttempts;
    
    // Configurazioni per task in background
    @Inject
    @ConfigProperty(name = "emission.background.certificate-enabled", defaultValue = "true")
    boolean certificateEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.background.email-enabled", defaultValue = "true")
    boolean emailEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.background.document-upload-enabled", defaultValue = "true")
    boolean documentUploadEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.background.max-retry-attempts", defaultValue = "3")
    int backgroundMaxRetryAttempts;
    
    @Inject
    @ConfigProperty(name = "emission.background.retry-delay-seconds", defaultValue = "5")
    int backgroundRetryDelaySeconds;
    
    // Configurazioni per certificati
    @Inject
    @ConfigProperty(name = "emission.certificate.format", defaultValue = "PDF")
    String certificateFormat;
    
    @Inject
    @ConfigProperty(name = "emission.certificate.template", defaultValue = "standard")
    String certificateTemplate;
    
    @Inject
    @ConfigProperty(name = "emission.certificate.language", defaultValue = "it")
    String certificateLanguage;
    
    // Configurazioni per email
    @Inject
    @ConfigProperty(name = "email.service.enabled", defaultValue = "true")
    boolean emailServiceEnabled;
    
    @Inject
    @ConfigProperty(name = "email.from.address", defaultValue = "<EMAIL>")
    String emailFromAddress;
    
    @Inject
    @ConfigProperty(name = "email.from.name", defaultValue = "YOLO Insurance")
    String emailFromName;
    
    @Inject
    @ConfigProperty(name = "email.templates.emission-confirmation", defaultValue = "emission_confirmation")
    String emissionConfirmationTemplate;
    
    // Configurazioni per logging
    @Inject
    @ConfigProperty(name = "emission.logging.detailed", defaultValue = "false")
    boolean detailedLogging;
    
    @Inject
    @ConfigProperty(name = "emission.logging.performance", defaultValue = "true")
    boolean performanceLogging;
    
    // Configurazioni per monitoraggio
    @Inject
    @ConfigProperty(name = "emission.monitoring.metrics-enabled", defaultValue = "true")
    boolean metricsEnabled;
    
    @Inject
    @ConfigProperty(name = "emission.monitoring.health-check-enabled", defaultValue = "true")
    boolean healthCheckEnabled;
    
    // Getters
    public boolean isEmissionEnabled() {
        return emissionEnabled;
    }
    
    public int getMaxConcurrentEmissions() {
        return maxConcurrentEmissions;
    }
    
    public Duration getTimeout() {
        return Duration.ofSeconds(timeoutSeconds);
    }
    
    public String getInternalPolicyPrefix() {
        return internalPolicyPrefix;
    }
    
    public boolean isInternalEmissionEnabled() {
        return internalEmissionEnabled;
    }
    
    public String getExternalPolicyPrefix() {
        return externalPolicyPrefix;
    }
    
    public boolean isExternalEmissionEnabled() {
        return externalEmissionEnabled;
    }
    
    public Duration getExternalTimeout() {
        return Duration.ofMillis(externalTimeoutMs);
    }
    
    public int getExternalRetryAttempts() {
        return externalRetryAttempts;
    }
    
    public boolean isCertificateEnabled() {
        return certificateEnabled;
    }
    
    public boolean isEmailEnabled() {
        return emailEnabled;
    }
    
    public boolean isDocumentUploadEnabled() {
        return documentUploadEnabled;
    }
    
    public int getBackgroundMaxRetryAttempts() {
        return backgroundMaxRetryAttempts;
    }
    
    public Duration getBackgroundRetryDelay() {
        return Duration.ofSeconds(backgroundRetryDelaySeconds);
    }
    
    public String getCertificateFormat() {
        return certificateFormat;
    }
    
    public String getCertificateTemplate() {
        return certificateTemplate;
    }
    
    public String getCertificateLanguage() {
        return certificateLanguage;
    }
    
    public boolean isEmailServiceEnabled() {
        return emailServiceEnabled;
    }
    
    public String getEmailFromAddress() {
        return emailFromAddress;
    }
    
    public String getEmailFromName() {
        return emailFromName;
    }
    
    public String getEmissionConfirmationTemplate() {
        return emissionConfirmationTemplate;
    }
    
    public boolean isDetailedLogging() {
        return detailedLogging;
    }
    
    public boolean isPerformanceLogging() {
        return performanceLogging;
    }
    
    public boolean isMetricsEnabled() {
        return metricsEnabled;
    }
    
    public boolean isHealthCheckEnabled() {
        return healthCheckEnabled;
    }
}
