package it.yolo.v3.infrastructure.adapter;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.Customer;
import it.yolo.v3.domain.port.CustomerPort;
import it.yolo.client.customer.CustomerClient;
import it.yolo.service.ServiceCustomer;

import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Implementation of CustomerPort using legacy CustomerClient
 */
@ApplicationScoped
public class CustomerPortImpl implements CustomerPort {

    private static final Logger LOGGER = Logger.getLogger(CustomerPortImpl.class);

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    ServiceCustomer serviceCustomer;

    @Override
    public Uni<Customer> findById(String token, Long customerId) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.debugf("Finding customer by ID: %d", customerId);
                
                var legacyCustomer = customerClient.findById(token, customerId)
                    .readEntity(it.yolo.client.customer.dto.CustomerResponseDto.class);
                
                return Customer.fromLegacyCustomer(legacyCustomer);
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to find customer by ID: %d", customerId);
                throw new RuntimeException("Failed to find customer", e);
            }
        });
    }

    @Override
    public Uni<Customer> findByNdg(String token, String ndgCode) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.debugf("Finding customer by NDG: %s", ndgCode);
                
                var legacyCustomer = serviceCustomer.findByNdg(token, ndgCode);
                
                return Customer.fromLegacyCustomer(legacyCustomer);
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to find customer by NDG: %s", ndgCode);
                throw new RuntimeException("Failed to find customer", e);
            }
        });
    }
}
