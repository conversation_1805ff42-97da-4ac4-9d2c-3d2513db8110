package it.yolo.v3.infrastructure.adapter;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.Product;
import it.yolo.v3.domain.port.ProductPort;
import it.yolo.service.ServiceProduct;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Implementation of ProductPort using legacy ServiceProduct
 */
@ApplicationScoped
public class ProductPortImpl implements ProductPort {

    private static final Logger LOGGER = Logger.getLogger(ProductPortImpl.class);

    @Inject
    ServiceProduct serviceProduct;

    @Override
    public Uni<Product> findById(Long productId, String language) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.debugf("Finding product by ID: %d", productId);
                
                var legacyProduct = serviceProduct.findById(productId, language);
                
                return Product.fromLegacyProduct(legacyProduct);
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to find product by ID: %d", productId);
                throw new RuntimeException("Failed to find product", e);
            }
        });
    }

    @Override
    public Uni<Product> findByCode(String productCode, String language) {
        return Uni.createFrom().item(() -> {
            try {
                LOGGER.debugf("Finding product by code: %s", productCode);
                
                // For now, use the legacy service (may not have findByCode method)
                // This would need to be implemented based on actual legacy service capabilities
                throw new UnsupportedOperationException("Find by code not implemented yet");
                
            } catch (Exception e) {
                LOGGER.errorf(e, "Failed to find product by code: %s", productCode);
                throw new RuntimeException("Failed to find product", e);
            }
        });
    }
}
