package it.yolo.v3.infrastructure.adapter;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.v3.domain.model.Order;
import it.yolo.v3.domain.port.OrderPort;
import it.yolo.client.order.OrderClient;
import it.yolo.client.yin.YinClientV2;

import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Implementation of OrderPort using legacy OrderClient
 */
@ApplicationScoped
public class OrderPortImpl implements OrderPort {

    private static final Logger LOGGER = Logger.getLogger(OrderPortImpl.class);

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    YinClientV2 yinClient;

    @Override
    public Order getOrder(String token, String orderCode) {
        try {
            LOGGER.debugf("Getting order: %s", orderCode);
            
            var legacyOrder = orderClient.getOrder(token, orderCode)
                .readEntity(it.yolo.client.order.dto.response.OrderResponseDto.class);
            
            return Order.fromLegacyOrder(legacyOrder);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to get order: %s", orderCode);
            throw new RuntimeException("Failed to get order", e);
        }
    }

    @Override
    public Order getOrderUnchecked(String token, String orderCode) {
        try {
            LOGGER.debugf("Getting order unchecked: %s", orderCode);
            
            var legacyOrder = orderClient.getOrderUnchecked(token, orderCode)
                .readEntity(it.yolo.client.order.dto.response.OrderResponseDto.class);
            
            return Order.fromLegacyOrder(legacyOrder);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to get order unchecked: %s", orderCode);
            throw new RuntimeException("Failed to get order unchecked", e);
        }
    }

    @Override
    public Order updateOrder(String token, it.yolo.client.order.dto.request.OrderRequestDto request, String orderCode) {
        try {
            LOGGER.debugf("Updating order: %s", orderCode);
            
            var legacyOrder = orderClient.update(token, request, orderCode)
                .readEntity(it.yolo.client.order.dto.response.OrderResponseDto.class);
            
            return Order.fromLegacyOrder(legacyOrder);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to update order: %s", orderCode);
            throw new RuntimeException("Failed to update order", e);
        }
    }

    @Override
    public Order updateOrderUnchecked(String token, it.yolo.client.order.dto.request.OrderRequestDto request, String orderCode) {
        try {
            LOGGER.debugf("Updating order unchecked: %s", orderCode);
            
            var legacyOrder = orderClient.uncheckedUpdate(token, request, orderCode)
                .readEntity(it.yolo.client.order.dto.response.OrderResponseDto.class);
            
            return Order.fromLegacyOrder(legacyOrder);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to update order unchecked: %s", orderCode);
            throw new RuntimeException("Failed to update order unchecked", e);
        }
    }

    @Override
    public JsonNode checkExistingPolicy(String orderCode, String token) {
        try {
            LOGGER.debugf("Checking existing policy for order: %s", orderCode);
            
            return orderClient.checkExistingPolicy(orderCode, token)
                .readEntity(JsonNode.class);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to check existing policy for order: %s", orderCode);
            throw new RuntimeException("Failed to check existing policy", e);
        }
    }

    @Override
    public void alignOrder(String orderCode) {
        try {
            LOGGER.debugf("Aligning order: %s", orderCode);
            
            yinClient.alignOder(orderCode);
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Failed to align order: %s", orderCode);
            throw new RuntimeException("Failed to align order", e);
        }
    }
}
