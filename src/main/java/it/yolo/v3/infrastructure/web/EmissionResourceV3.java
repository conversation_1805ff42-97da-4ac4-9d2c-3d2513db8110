package it.yolo.v3.infrastructure.web;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.application.dto.EmissionRequestV3;
import it.yolo.v3.application.dto.EmissionResponseV3;
import it.yolo.v3.application.service.EmissionOrchestrator;
import it.yolo.v3.application.service.EmissionStatusRepository;
import it.yolo.v3.application.service.EmissionStatusRepository.EmissionStatus;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.logging.Logger;
import io.quarkus.security.Authenticated;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.function.Supplier;
import java.time.LocalDateTime;

/**
 * V3 Emission API - Seguendo esattamente la guida API_V3_EMISSION_GUIDE.md
 * Endpoint: /api/v3/emissions/{orderCode}
 */
@Path("/api/v3/emissions")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Emission V3", description = "V3 Emission Management API")
public class EmissionResourceV3 {

    private static final Logger log = Logger.getLogger(EmissionResourceV3.class);

    @Inject
    EmissionOrchestrator orchestrator;

    @Inject
    EmissionStatusRepository statusRepository;

    /**
     * POST /api/v3/emissions/{orderCode} - Endpoint principale secondo la guida
     * 
     * Flusso:
     * 1. Fase Sincrona (< 500ms): validazione + recupero dati + emissione polizza  
     * 2. Fase Asincrona: certificato + email + documenti + aggiornamento ordine
     */
    @POST
    @Path("/{orderCode}")
    @Operation(summary = "Avvia processo di emissione asincrono", 
               description = "Avvia il processo di emissione per un ordine specifico seguendo il flusso della guida")
    @APIResponse(responseCode = "201", description = "Processo di emissione avviato con successo",
                 content = @Content(schema = @Schema(implementation = EmissionResponseV3.class)))
    @APIResponse(responseCode = "409", description = "Ordine già emesso")
    @APIResponse(responseCode = "500", description = "Errore interno del server")
    @Authenticated
    public Uni<RestResponse<EmissionResponseV3>> startEmission(
            @PathParam("orderCode") String orderCode,
            @HeaderParam("Authorization") String token) {
        
        return Uni.createFrom().item(new Supplier<RestResponse<EmissionResponseV3>>() {
            @Override
            public RestResponse<EmissionResponseV3> get() {
                try {
                    log.infof("V3 - Starting emission process for order: %s", orderCode);
                    
                    // Step 1: Inizializza stato PENDING 
                    statusRepository.updateStatus(orderCode, EmissionStatus.PENDING);
                    
                    // Step 2: Avvia processo sincrono + asincrono
                    orchestrator.processEmissionV3(orderCode, new EmissionRequestV3(null, null, null, null, null), token)
                        .subscribe()
                        .with(
                            response -> {
                                log.infof("V3 - Emission completed for order: %s, policy: %s", 
                                    orderCode, response.policyNumber());
                            },
                            error -> {
                                log.errorf("V3 - Emission failed for order: %s - %s", orderCode, error.getMessage());
                                statusRepository.updateStatus(orderCode, EmissionStatus.FAILED);
                            }
                        );
                    
                    // Step 3: Risposta immediata 201 Created con Location header
                    EmissionResponseV3 response = EmissionResponseV3.pending(orderCode);
                    
                    return RestResponse.status(RestResponse.Status.CREATED, response);
                    
                } catch (Exception e) {
                    log.errorf("V3 - Failed to start emission for order: %s - %s", orderCode, e.getMessage());
                    statusRepository.updateStatus(orderCode, EmissionStatus.FAILED);
                    
                    EmissionResponseV3 errorResponse = EmissionResponseV3.error(orderCode, 
                        "Failed to start emission process: " + e.getMessage());
                            
                    return RestResponse.status(RestResponse.Status.INTERNAL_SERVER_ERROR, errorResponse);
                }
            }
        });
    }

    /**
     * GET /api/v3/emissions/{orderCode}/status - Endpoint status secondo la guida
     */
    @GET
    @Path("/{orderCode}/status")
    @Operation(summary = "Recupera lo stato del processo di emissione", 
               description = "Verifica lo stato dell'emissione secondo la guida API")
    @APIResponse(responseCode = "200", description = "Status recuperato con successo")
    @Authenticated
    public Uni<RestResponse<EmissionStatusResponse>> getEmissionStatus(
            @PathParam("orderCode") String orderCode) {

        return Uni.createFrom().item(new Supplier<RestResponse<EmissionStatusResponse>>() {
            @Override
            public RestResponse<EmissionStatusResponse> get() {
                try {
                    EmissionStatus status = statusRepository.getStatus(orderCode);
                    
                    if (status == EmissionStatus.NOT_FOUND) {
                        return RestResponse.status(RestResponse.Status.NOT_FOUND, 
                            new EmissionStatusResponse(orderCode, null, "NOT_FOUND", 
                                "Nessun processo trovato per questo ordine", 
                                LocalDateTime.now().toString()));
                    }
                    
                    EmissionStatusResponse response = new EmissionStatusResponse(
                        orderCode,
                        null, // policyNumber - da recuperare dal processo
                        status.name(),
                        status.getDescription(),
                        LocalDateTime.now().toString()
                    );
                    
                    return RestResponse.ok(response);
                    
                } catch (Exception e) {
                    log.errorf("V3 - Error retrieving status for order: %s - %s", orderCode, e.getMessage());
                    
                    EmissionStatusResponse errorResponse = new EmissionStatusResponse(
                        orderCode, 
                        null, 
                        "ERROR", 
                        "Error retrieving status: " + e.getMessage(), 
                        LocalDateTime.now().toString()
                    );
                    
                    return RestResponse.status(RestResponse.Status.INTERNAL_SERVER_ERROR, errorResponse);
                }
            }
        });
    }

    private String getStatusDescription(EmissionStatus status) {
        return switch (status) {
            case PENDING -> "Processo appena avviato";
            case EMITTED -> "Polizza emessa, task background avviati";
            case CERTIFICATE_IN_PROGRESS -> "Generazione certificato in corso";
            case COMMUNICATION_IN_PROGRESS -> "Invio email e upload documenti";
            case COMPLETE -> "Processo completato con successo";
            case FAILED -> "Errore durante elaborazione background";
            default -> "Status sconosciuto";
        };
    }

    /**
     * DTO per la risposta del status secondo la guida
     */
    public record EmissionStatusResponse(
        String orderCode,
        String policyNumber,
        String state,
        String details,
        String updatedAt
    ) {}
}
