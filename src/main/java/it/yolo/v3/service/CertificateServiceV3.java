package it.yolo.v3.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.DocumentClientV3;
import it.yolo.v3.client.DocumentManagerClientV3;
import it.yolo.v3.client.OrderClientV3;
import it.yolo.v3.client.PolicyClientV3;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

/**
 * Servizio V3 per la gestione dei certificati.
 * Gestisce la generazione, il download e l'upload dei certificati delle polizze.
 */
@ApplicationScoped
public class CertificateServiceV3 {
    
    private static final Logger LOG = Logger.getLogger(CertificateServiceV3.class);
    
    @Inject
    @RestClient
    DocumentClientV3 documentClient;
    
    @Inject
    @RestClient
    DocumentManagerClientV3 documentManagerClient;
    
    @Inject
    @RestClient
    OrderClientV3 orderClient;
    
    @Inject
    @RestClient
    PolicyClientV3 policyClient;
    
    @Inject
    ObjectMapper objectMapper;
    
    /**
     * Genera il certificato per un ordine
     */
    public Uni<CertificateV3> generateCertificate(String orderCode, String token) {
        LOG.infof("V3 - Generating certificate for order %s", orderCode);
        
        return orderClient.findByOrderCodeUnchecked(token, orderCode)
            .flatMap(order -> {
                // Prepara la richiesta per la generazione del certificato
                ObjectNode certificateRequest = buildCertificateRequest(order, token);
                
                // Chiama il servizio di generazione certificati
                return documentClient.generateCertificate(certificateRequest)
                    .onItem().transform(response -> {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            JsonNode responseData = response.readEntity(JsonNode.class);
                            return parseCertificateResponse(responseData, orderCode);
                        } else {
                            throw new RuntimeException("Certificate generation failed with status: " + response.getStatus());
                        }
                    });
            })
            .onItem().invoke(certificate -> 
                LOG.infof("V3 - Certificate generated successfully for order %s: %s", 
                         orderCode, certificate.fileName()))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Certificate generation failed for order %s", orderCode));
    }
    
    /**
     * Carica il certificato nel document manager
     */
    public Uni<Void> uploadCertificateToDocumentManager(String orderCode, String token) {
        LOG.infof("V3 - Uploading certificate to document manager for order %s", orderCode);
        
        return generateCertificate(orderCode, token)
            .flatMap(certificate -> {
                // Prepara la richiesta per l'upload
                ObjectNode uploadRequest = buildUploadRequest(certificate, orderCode);
                
                // Carica nel document manager
                return documentManagerClient.addDocument(token, orderCode, uploadRequest)
                    .onItem().transform(response -> {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            LOG.infof("V3 - Certificate uploaded successfully for order %s", orderCode);
                            return null;
                        } else {
                            throw new RuntimeException("Certificate upload failed with status: " + response.getStatus());
                        }
                    });
            })
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Certificate upload failed for order %s", orderCode));
    }
    
    /**
     * Ottiene il link di download per un certificato
     */
    public Uni<String> getCertificateDownloadLink(String orderCode, String token) {
        LOG.debugf("V3 - Getting certificate download link for order %s", orderCode);
        
        return generateCertificate(orderCode, token)
            .flatMap(certificate -> {
                // Prepara la richiesta per il download link
                ObjectNode downloadRequest = buildDownloadRequest(certificate);
                
                return documentClient.downloadLink(downloadRequest)
                    .onItem().transform(response -> {
                        if (response.getStatus() >= 200 && response.getStatus() < 300) {
                            JsonNode responseData = response.readEntity(JsonNode.class);
                            return extractDownloadLink(responseData);
                        } else {
                            throw new RuntimeException("Download link generation failed with status: " + response.getStatus());
                        }
                    });
            });
    }
    
    private ObjectNode buildCertificateRequest(Object order, String token) {
        try {
            ObjectNode request = objectMapper.createObjectNode();
            
            // Aggiunge i dati dell'ordine
            request.set("order", objectMapper.valueToTree(order));
            
            // Aggiunge metadati per la generazione
            request.put("format", "PDF");
            request.put("template", "standard");
            request.put("language", "it");
            
            return request;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build certificate request", e);
        }
    }
    
    private ObjectNode buildUploadRequest(CertificateV3 certificate, String orderCode) {
        try {
            ObjectNode request = objectMapper.createObjectNode();
            
            request.put("fileName", certificate.fileName());
            request.put("contentType", certificate.contentType());
            request.put("fileSize", certificate.fileSize());
            request.put("orderCode", orderCode);
            request.put("documentType", "CERTIFICATE");
            
            if (certificate.content() != null) {
                request.put("content", certificate.content());
            }
            
            return request;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build upload request", e);
        }
    }
    
    private ObjectNode buildDownloadRequest(CertificateV3 certificate) {
        try {
            ObjectNode request = objectMapper.createObjectNode();
            
            request.put("fileName", certificate.fileName());
            request.put("documentType", "CERTIFICATE");
            
            return request;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build download request", e);
        }
    }
    
    private CertificateV3 parseCertificateResponse(JsonNode response, String orderCode) {
        try {
            String fileName = response.has("fileName") ? 
                response.get("fileName").asText() : 
                orderCode + "_certificate.pdf";
                
            String contentType = response.has("contentType") ? 
                response.get("contentType").asText() : 
                "application/pdf";
                
            long fileSize = response.has("fileSize") ? 
                response.get("fileSize").asLong() : 
                0L;
                
            String content = response.has("content") ? 
                response.get("content").asText() : 
                null;
                
            String downloadLink = response.has("downloadLink") ? 
                response.get("downloadLink").asText() : 
                null;
            
            return new CertificateV3(fileName, contentType, fileSize, content, downloadLink);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse certificate response", e);
        }
    }
    
    private String extractDownloadLink(JsonNode response) {
        if (response.has("downloadLink")) {
            return response.get("downloadLink").asText();
        } else if (response.has("url")) {
            return response.get("url").asText();
        } else {
            throw new RuntimeException("Download link not found in response");
        }
    }
    
    /**
     * Record per rappresentare un certificato
     */
    public record CertificateV3(
        String fileName,
        String contentType,
        long fileSize,
        String content,
        String downloadLink
    ) {}
}
