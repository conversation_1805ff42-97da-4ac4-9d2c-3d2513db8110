package it.yolo.v3.service;

import it.yolo.v3.domain.Customer;
import it.yolo.v3.domain.Order;
import it.yolo.v3.domain.Product;
import it.yolo.v3.dto.EmissionResponse;
import it.yolo.v3.port.CustomerPort;
import it.yolo.v3.port.OrderPort;
import it.yolo.v3.port.ProductPort;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Orchestratore per la fase sincrona del processo di emissione V3.
 *
 * Responsabilità:
 * 1. Coordinare il recupero dei dati in parallelo (Order, Customer, Product)
 * 2. Validare i dati per l'emissione
 * 3. Delegare l'emissione al PolicyService
 * 4. Avviare i task in background (fire-and-forget)
 * 5. Restituire la risposta immediata al client
 */
@ApplicationScoped
public class EmissionOrchestrator {

    private static final Logger LOG = Logger.getLogger(EmissionOrchestrator.class);

    @Inject
    OrderPort orderPort;

    @Inject
    CustomerPort customerPort;

    @Inject
    ProductPort productPort;

    @Inject
    PolicyService policyService;

    @Inject
    BackgroundTaskRunner backgroundTaskRunner;

    /**
     * Avvia il processo di emissione sincrono.
     *
     * Fase 1: Recupero dati in parallelo (< 200ms)
     * Fase 2: Validazione e emissione polizza (< 200ms)
     * Fase 3: Avvio task in background (fire-and-forget)
     * Fase 4: Risposta immediata al client
     *
     * @param orderCode Codice dell'ordine
     * @param token Token di autorizzazione
     * @return Uni con la risposta di emissione
     */
    public Uni<EmissionResponse> startEmissionProcess(String orderCode, String token) {
        LOG.infof("Starting emission process for order %s", orderCode);

        // Fase 1: Recupero dati in parallelo
        Uni<Order> orderUni = orderPort.findByCode(orderCode, token)
            .onItem().invoke(order -> LOG.debugf("Order retrieved: %s", order.orderCode()))
            .onFailure().invoke(failure -> LOG.errorf(failure, "Failed to retrieve order %s", orderCode));

        // Recupera customer e product in parallelo basandosi sui dati dell'ordine
        Uni<Customer> customerUni = orderUni
            .flatMap(order -> customerPort.findById(order.customerId(), token))
            .onItem().invoke(customer -> LOG.debugf("Customer retrieved: %s", customer.id()))
            .onFailure().invoke(failure -> LOG.errorf(failure, "Failed to retrieve customer for order %s", orderCode));

        Uni<Product> productUni = orderUni
            .flatMap(order -> productPort.findById(order.productId(), token))
            .onItem().invoke(product -> LOG.debugf("Product retrieved: %s", product.id()))
            .onFailure().invoke(failure -> LOG.errorf(failure, "Failed to retrieve product for order %s", orderCode));

        // Combina tutti i risultati in parallelo
        return Uni.combine().all().unis(orderUni, customerUni, productUni).asTuple()
            .onItem().invoke(tuple -> LOG.infof("All data retrieved for order %s", orderCode))
            .flatMap(tuple -> {
                Order order = tuple.getItem1();
                Customer customer = tuple.getItem2();
                Product product = tuple.getItem3();

                LOG.infof("Starting policy emission for order %s", orderCode);

                // Fase 2: Emissione della polizza (operazione critica sincrona)
                return policyService.emit(order, customer, product, token);
            })
            .map(policy -> {
                LOG.infof("Policy emitted successfully: %s for order %s",
                         policy.data().policyCode(), orderCode);

                // Fase 3: Avvia task in background (fire-and-forget)
                // Non attendiamo il completamento di questa operazione
                try {
                    backgroundTaskRunner.executePostEmissionTasks(policy, token);
                    LOG.debugf("Background tasks started for policy %s", policy.data().policyCode());
                } catch (Exception e) {
                    // Log dell'errore ma non interrompiamo il flusso principale
                    LOG.errorf(e, "Failed to start background tasks for policy %s", policy.data().policyCode());
                }

                // Fase 4: Crea la risposta immediata per il client
                return new EmissionResponse(orderCode, policy.data().policyCode());
            })
            .onFailure().invoke(failure ->
                LOG.errorf(failure, "Emission process failed for order %s", orderCode)
            );
    }
}
