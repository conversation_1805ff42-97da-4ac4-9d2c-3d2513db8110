package it.yolo.v3.service;

import io.smallrye.common.annotation.RunOnVirtualThread;
import it.yolo.v3.domain.Policy;
import it.yolo.v3.dto.EmissionState;
import it.yolo.v3.port.OrderPort;
import it.yolo.v3.repository.EmissionStatusRepository;
import it.yolo.v3.service.CertificateService.Certificate;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Gestore dei task in background per le operazioni post-emissione.
 *
 * Usa Virtual Threads per esecuzione leggera e non bloccante.
 * Gestisce tutte le operazioni secondarie dopo l'emissione della polizza:
 * 1. Generazione certificato
 * 2. Invio email di conferma
 * 3. Upload documenti
 * 4. Aggiornamento finale dell'ordine
 *
 * Ogni fase aggiorna lo stato nel repository per il tracking.
 */
@ApplicationScoped
public class BackgroundTaskRunner {

    private static final Logger LOG = Logger.getLogger(BackgroundTaskRunner.class);

    @Inject
    CertificateService certificateService;

    @Inject
    EmailService emailService;

    @Inject
    DocumentService documentService;

    @Inject
    OrderPort orderPort;

    @Inject
    EmissionStatusRepository statusRepository;

    /**
     * Esegue i task post-emissione in background.
     *
     * Questo metodo viene chiamato in modalità fire-and-forget
     * dopo che la polizza è stata emessa con successo.
     *
     * Usa @VirtualThreads per esecuzione su thread virtuale leggero.
     * Il codice all'interno può essere scritto in modo imperativo/bloccante.
     *
     * @param policy Polizza emessa
     * @param token Token di autorizzazione
     */
    @RunOnVirtualThread
    public void executePostEmissionTasks(Policy policy, String token) {
        // Estrae l'orderCode dal contesto - dovrebbe essere passato separatamente
        // o estratto dalla polizza in modo affidabile
        String orderCode = extractOrderCodeFromPolicy(policy);
        String policyNumber = policy.data().policyCode();

        LOG.infof("Starting background tasks for policy %s (order %s)", policyNumber, orderCode);

        try {
            // Fase 1: Generazione Certificato
            LOG.infof("Phase 1: Generating certificate for policy %s", policyNumber);
            statusRepository.updateStatus(orderCode, EmissionState.CERTIFICATE_IN_PROGRESS,
                                        "Generating policy certificate")
                .await().indefinitely();

            Certificate certificate = certificateService.generate(policy, token)
                .await().indefinitely();

            LOG.infof("Certificate generated successfully for policy %s", policyNumber);

            // Fase 2: Comunicazioni e Upload (in parallelo)
            LOG.infof("Phase 2: Starting communication and document upload for policy %s", policyNumber);
            statusRepository.updateStatus(orderCode, EmissionState.COMMUNICATION_IN_PROGRESS,
                                        "Sending email and uploading documents")
                .await().indefinitely();

            // Esegue email e upload documenti in parallelo
            Uni<Void> emailUni = emailService.sendPolicyEmail(policy, certificate, token);
            Uni<Void> docsUni = documentService.uploadEmissionDocuments(policy, certificate, token);

            // Attende il completamento di entrambe le operazioni
            Uni.join().all(emailUni, docsUni).andFailFast()
                .await().indefinitely();

            LOG.infof("Email sent and documents uploaded for policy %s", policyNumber);

            // Fase 3: Completamento Ordine
            LOG.infof("Phase 3: Completing order %s", orderCode);
            orderPort.markAsComplete(orderCode, token)
                .await().indefinitely();

            // Fase 4: Aggiornamento Stato Finale
            statusRepository.updateStatus(orderCode, EmissionState.COMPLETE,
                                        "All background tasks completed successfully")
                .await().indefinitely();

            LOG.infof("Background tasks completed successfully for policy %s (order %s)",
                     policyNumber, orderCode);

        } catch (Exception e) {
            LOG.errorf(e, "Background tasks failed for policy %s (order %s)", policyNumber, orderCode);

            // Aggiorna lo stato a FAILED con dettagli dell'errore
            try {
                statusRepository.updateStatus(orderCode, EmissionState.FAILED,
                                            "Background task failed: " + e.getMessage())
                    .await().indefinitely();

                // Marca anche l'ordine come fallito
                orderPort.markAsFailed(orderCode, e.getMessage(), token)
                    .await().indefinitely();

            } catch (Exception updateException) {
                LOG.errorf(updateException, "Failed to update failure status for policy %s", policyNumber);
            }

            // In una implementazione reale, qui si potrebbe:
            // 1. Inviare notifica al sistema di alerting (es. Sentry)
            // 2. Schedulare un retry automatico
            // 3. Creare un ticket di supporto
        }
    }
    
    /**
     * Estrae l'orderCode dalla polizza.
     * Metodo di utilità per recuperare l'orderCode in modo sicuro.
     */
    private String extractOrderCodeFromPolicy(Policy policy) {
        // Implementazione temporanea - in una implementazione reale
        // questo dovrebbe essere un campo diretto nella polizza
        // o passato come parametro separato
        
        if (policy.data().orderId() != null) {
            return policy.data().orderId().toString();
        }
        
        // Fallback: cerca nei dati aggiuntivi della polizza
        // o usa un pattern di naming standard
        throw new IllegalStateException("Cannot extract order code from policy: " + policy.data().policyCode());
    }
}
