package it.yolo.v3.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.OrderClientV3;
import it.yolo.v3.client.CustomerClientV3;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Servizio V3 per l'invio di email.
 * Gestisce l'invio di notifiche email per conferme di emissione, certificati, ecc.
 */
@ApplicationScoped
public class EmailServiceV3 {
    
    private static final Logger LOG = Logger.getLogger(EmailServiceV3.class);
    
    @Inject
    @RestClient
    OrderClientV3 orderClient;
    
    @Inject
    @RestClient
    CustomerClientV3 customerClient;
    
    @Inject
    CertificateServiceV3 certificateService;
    
    @Inject
    ObjectMapper objectMapper;
    
    @ConfigProperty(name = "email.service.enabled", defaultValue = "true")
    boolean emailServiceEnabled;
    
    @ConfigProperty(name = "email.templates.emission-confirmation", defaultValue = "emission_confirmation")
    String emissionConfirmationTemplate;
    
    @ConfigProperty(name = "email.from.address", defaultValue = "<EMAIL>")
    String fromAddress;
    
    @ConfigProperty(name = "email.from.name", defaultValue = "YOLO Insurance")
    String fromName;
    
    /**
     * Invia email di conferma emissione
     */
    public Uni<Void> sendEmissionConfirmation(String orderCode, String token) {
        if (!emailServiceEnabled) {
            LOG.infof("V3 - Email service disabled, skipping emission confirmation for order %s", orderCode);
            return Uni.createFrom().voidItem();
        }
        
        LOG.infof("V3 - Sending emission confirmation email for order %s", orderCode);
        
        return orderClient.findByOrderCodeUnchecked(token, orderCode)
            .flatMap(order -> {
                if (order.data().customer() == null || order.data().customer().id() == null) {
                    return Uni.createFrom().failure(
                        new RuntimeException("Customer data not found in order"));
                }
                
                return customerClient.findById(token, order.data().customer().id().longValue())
                    .flatMap(customer -> {
                        // Prepara i dati per l'email
                        EmailDataV3 emailData = buildEmissionConfirmationEmailData(order, customer, orderCode);
                        
                        // Invia l'email
                        return sendEmail(emailData);
                    });
            })
            .onItem().invoke(() -> 
                LOG.infof("V3 - Emission confirmation email sent successfully for order %s", orderCode))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Failed to send emission confirmation email for order %s", orderCode));
    }
    
    /**
     * Invia email con certificato allegato
     */
    public Uni<Void> sendCertificateEmail(String orderCode, String token) {
        if (!emailServiceEnabled) {
            LOG.infof("V3 - Email service disabled, skipping certificate email for order %s", orderCode);
            return Uni.createFrom().voidItem();
        }
        
        LOG.infof("V3 - Sending certificate email for order %s", orderCode);
        
        return Uni.combine().all()
            .unis(
                orderClient.findByOrderCodeUnchecked(token, orderCode),
                certificateService.generateCertificate(orderCode, token)
            )
            .asTuple()
            .flatMap(tuple -> {
                var order = tuple.getItem1();
                var certificate = tuple.getItem2();
                
                if (order.data().customer() == null || order.data().customer().id() == null) {
                    return Uni.createFrom().failure(
                        new RuntimeException("Customer data not found in order"));
                }
                
                return customerClient.findById(token, order.data().customer().id().longValue())
                    .flatMap(customer -> {
                        // Prepara i dati per l'email con certificato
                        EmailDataV3 emailData = buildCertificateEmailData(order, customer, certificate, orderCode);
                        
                        // Invia l'email
                        return sendEmail(emailData);
                    });
            })
            .onItem().invoke(() -> 
                LOG.infof("V3 - Certificate email sent successfully for order %s", orderCode))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Failed to send certificate email for order %s", orderCode));
    }
    
    private EmailDataV3 buildEmissionConfirmationEmailData(Object order, Object customer, String orderCode) {
        try {
            // Estrae i dati del cliente
            JsonNode customerNode = objectMapper.valueToTree(customer);
            JsonNode customerData = customerNode.get("data");
            
            String toEmail = customerData.get("primaryMail").asText();
            String customerName = customerData.get("name").asText() + " " + customerData.get("surname").asText();
            
            // Prepara il contenuto dell'email
            String subject = "Conferma Emissione Polizza - Ordine " + orderCode;
            String htmlContent = buildEmissionConfirmationHtml(customerName, orderCode);
            String textContent = buildEmissionConfirmationText(customerName, orderCode);
            
            return new EmailDataV3(
                fromAddress,
                fromName,
                toEmail,
                customerName,
                subject,
                htmlContent,
                textContent,
                null, // nessun allegato per la conferma
                emissionConfirmationTemplate
            );
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build emission confirmation email data", e);
        }
    }
    
    private EmailDataV3 buildCertificateEmailData(Object order, Object customer, 
                                                 CertificateServiceV3.CertificateV3 certificate, String orderCode) {
        try {
            // Estrae i dati del cliente
            JsonNode customerNode = objectMapper.valueToTree(customer);
            JsonNode customerData = customerNode.get("data");
            
            String toEmail = customerData.get("primaryMail").asText();
            String customerName = customerData.get("name").asText() + " " + customerData.get("surname").asText();
            
            // Prepara il contenuto dell'email
            String subject = "Certificato Polizza - Ordine " + orderCode;
            String htmlContent = buildCertificateEmailHtml(customerName, orderCode, certificate);
            String textContent = buildCertificateEmailText(customerName, orderCode, certificate);
            
            // Prepara l'allegato
            EmailAttachmentV3 attachment = new EmailAttachmentV3(
                certificate.fileName(),
                certificate.contentType(),
                certificate.content()
            );
            
            return new EmailDataV3(
                fromAddress,
                fromName,
                toEmail,
                customerName,
                subject,
                htmlContent,
                textContent,
                attachment,
                "certificate_email"
            );
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to build certificate email data", e);
        }
    }
    
    private Uni<Void> sendEmail(EmailDataV3 emailData) {
        // In un'implementazione reale, qui si chiamerebbe un servizio di email
        // come SendGrid, Amazon SES, o un servizio interno
        
        LOG.infof("V3 - Sending email to %s with subject: %s", emailData.toEmail(), emailData.subject());
        
        return Uni.createFrom().item(() -> {
            // Simula l'invio dell'email
            try {
                Thread.sleep(100); // Simula latenza del servizio email
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Email sending interrupted", e);
            }
            
            LOG.debugf("V3 - Email sent successfully to %s", emailData.toEmail());
            return null;
        });
    }
    
    private String buildEmissionConfirmationHtml(String customerName, String orderCode) {
        return String.format("""
            <html>
            <body>
                <h2>Conferma Emissione Polizza</h2>
                <p>Gentile %s,</p>
                <p>La informiamo che la sua polizza per l'ordine <strong>%s</strong> è stata emessa con successo.</p>
                <p>Riceverà a breve il certificato di polizza.</p>
                <p>Cordiali saluti,<br>Il Team YOLO</p>
            </body>
            </html>
            """, customerName, orderCode);
    }
    
    private String buildEmissionConfirmationText(String customerName, String orderCode) {
        return String.format("""
            Conferma Emissione Polizza
            
            Gentile %s,
            
            La informiamo che la sua polizza per l'ordine %s è stata emessa con successo.
            Riceverà a breve il certificato di polizza.
            
            Cordiali saluti,
            Il Team YOLO
            """, customerName, orderCode);
    }
    
    private String buildCertificateEmailHtml(String customerName, String orderCode, 
                                           CertificateServiceV3.CertificateV3 certificate) {
        return String.format("""
            <html>
            <body>
                <h2>Certificato Polizza</h2>
                <p>Gentile %s,</p>
                <p>In allegato trova il certificato della sua polizza per l'ordine <strong>%s</strong>.</p>
                <p>Nome file: %s</p>
                <p>Cordiali saluti,<br>Il Team YOLO</p>
            </body>
            </html>
            """, customerName, orderCode, certificate.fileName());
    }
    
    private String buildCertificateEmailText(String customerName, String orderCode, 
                                           CertificateServiceV3.CertificateV3 certificate) {
        return String.format("""
            Certificato Polizza
            
            Gentile %s,
            
            In allegato trova il certificato della sua polizza per l'ordine %s.
            Nome file: %s
            
            Cordiali saluti,
            Il Team YOLO
            """, customerName, orderCode, certificate.fileName());
    }
    
    /**
     * Record per i dati dell'email
     */
    public record EmailDataV3(
        String fromEmail,
        String fromName,
        String toEmail,
        String toName,
        String subject,
        String htmlContent,
        String textContent,
        EmailAttachmentV3 attachment,
        String template
    ) {}
    
    /**
     * Record per gli allegati email
     */
    public record EmailAttachmentV3(
        String fileName,
        String contentType,
        String content
    ) {}
}
