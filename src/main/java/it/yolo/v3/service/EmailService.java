package it.yolo.v3.service;

import it.yolo.v3.domain.Policy;
import it.yolo.v3.service.CertificateService.Certificate;
import it.yolo.client.communicationManager.CommunicationManagerClient;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;


/**
 * Servizio per l'invio delle email di conferma emissione.
 */
@ApplicationScoped
public class EmailService {

    private static final Logger LOG = Logger.getLogger(EmailService.class);

    @Inject
    @RestClient
    CommunicationManagerClient communicationClient;
    
    /**
     * Invia l'email di conferma emissione con certificato allegato.
     * 
     * @param policy Polizza emessa
     * @param certificate Certificato da allegare
     * @param token Token di autorizzazione
     * @return Uni che completa quando l'email è stata inviata
     */
    public Uni<Void> sendPolicyEmail(Policy policy, Certificate certificate, String token) {
        LOG.infof("Sending policy email for policy %s", policy.data().policyCode());
        
        return Uni.createFrom().item(() -> {
            try {
                String subject = "Conferma Emissione Polizza " + policy.data().policyCode();
                String content = composeEmailContent(policy, certificate);
                
                // Estrae l'ID del cliente dalla polizza
                String customerId = policy.data().customer() != null ? 
                    policy.data().customer().id() : null;
                
                if (customerId == null) {
                    throw new RuntimeException("Customer ID not found in policy");
                }
                
                CommunicationManagerDtoRequest emailRequest = createEmailRequest(policy, certificate);
                
                // Per ora, simulazione dell'invio
                // In implementazione reale, chiamare il client di comunicazione
                LOG.infof("Email request created for customer %s", customerId);
                
                // Simulazione invio email riuscito
                LOG.infof("Email sent successfully for policy %s", policy.data().policyCode());
                return null;
            } catch (Exception e) {
                LOG.errorf(e, "Exception while sending email for policy %s", policy.data().policyCode());
                throw new RuntimeException("Email sending failed", e);
            }
        });
    }
    
    private String composeEmailContent(Policy policy, Certificate certificate) {
        return String.format("""
            Gentile Cliente,
            
            La Sua polizza numero %s è stata emessa con successo.
            
            Dettagli polizza:
            - Numero: %s
            - Data emissione: %s
            - Data inizio copertura: %s
            - Data fine copertura: %s
            
            In allegato trova il certificato di polizza.
            
            Cordiali saluti,
            Il Team YOLO Insurance
            """, 
            policy.data().policyCode(),
            policy.data().policyCode(),
            policy.data().startDate(),
            policy.data().startDate(),
            policy.data().endDate()
        );
    }
    
    private void sendEmail(Integer customerId, String subject, String content, Certificate certificate) {
        // Simula l'invio tramite servizio esterno (es. SendGrid, AWS SES)
        LOG.debugf("Sending email to customer %s: %s", customerId, subject);

        // In una implementazione reale, qui ci sarebbe la chiamata al servizio di email
        // es. communicationManagerClient.sendEmail(...)
    }

    /**
     * Crea la richiesta per il servizio di comunicazione
     */
    private CommunicationManagerDtoRequest createEmailRequest(Policy policy, Certificate certificate) {
        CommunicationManagerDtoRequest request = new CommunicationManagerDtoRequest();
        // Popola la richiesta con i dati della polizza e del certificato
        // Questo dipende dalla struttura specifica del DTO legacy
        return request;
    }
}
