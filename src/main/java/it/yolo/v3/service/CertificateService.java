package it.yolo.v3.service;

import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.v3.domain.Policy;
import it.yolo.client.document.DocumentClient;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.time.LocalDateTime;

/**
 * Servizio per la generazione dei certificati di polizza.
 */
@ApplicationScoped
public class CertificateService {

    private static final Logger LOG = Logger.getLogger(CertificateService.class);

    @Inject
    @RestClient
    DocumentClient documentClient;
    
    /**
     * Genera il certificato per una polizza emessa.
     * 
     * @param policy Polizza per cui generare il certificato
     * @param token Token di autorizzazione
     * @return Uni con il certificato generato
     */
    public Uni<Certificate> generate(Policy policy, String token) {
        LOG.infof("Generating certificate for policy %s", policy.data().policyCode());
        
        return Uni.createFrom().item(() -> {
            // Crea la richiesta per il servizio di certificati
            CertificateRequestDto request = createCertificateRequest(policy);
            
            try {
                Response response = documentClient.generateCertficate(request);
                
                if (response.getStatus() == 200) {
                    String certificateContent = response.readEntity(String.class);
                    LOG.infof("Certificate generated successfully for policy %s", policy.data().policyCode());
                    
                    return new Certificate(
                        "CERT_" + policy.data().policyCode(),
                        policy.data().policyCode(),
                        "PDF",
                        certificateContent,
                        LocalDateTime.now()
                    );
                } else {
                    LOG.errorf("Error generating certificate for policy %s: HTTP %d", 
                             policy.data().policyCode(), response.getStatus());
                    throw new RuntimeException("Certificate generation failed");
                }
            } catch (Exception e) {
                LOG.errorf(e, "Exception while generating certificate for policy %s", policy.data().policyCode());
                throw new RuntimeException("Certificate generation failed", e);
            }
        });
    }
    
    /**
     * Crea la richiesta per il servizio di generazione certificati
     */
    private CertificateRequestDto createCertificateRequest(Policy policy) {
        CertificateRequestDto request = new CertificateRequestDto();
        
        // Crea un PolicyResponseDto dal dominio Policy V3
        // Questo è un mapping tra il nuovo modello V3 e il DTO legacy
        
        // Per ora, implementazione semplificata
        // In un'implementazione completa, dovremmo mappare tutti i campi necessari
        
        return request;
    }
    
    /**
     * Record per rappresentare un certificato generato
     */
    public record Certificate(
        String id,
        String policyNumber,
        String format,
        String content,
        LocalDateTime generatedAt
    ) {}
}
