package it.yolo.v3.service;

import io.quarkus.runtime.annotations.RunOnVirtualThread;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.DocumentClientV3;
import it.yolo.v3.client.DocumentManagerClientV3;
import it.yolo.v3.client.OrderClientV3;
import it.yolo.v3.dto.response.EmissionStatusV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Servizio V3 per l'esecuzione di task in background utilizzando Virtual Threads.
 * Gestisce le operazioni asincrone post-emissione: generazione certificati, invio email, upload documenti.
 */
@ApplicationScoped
public class BackgroundTaskRunnerV3 {
    
    private static final Logger LOG = Logger.getLogger(BackgroundTaskRunnerV3.class);
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    @Inject
    @RestClient
    DocumentClientV3 documentClient;
    
    @Inject
    @RestClient
    DocumentManagerClientV3 documentManagerClient;
    
    @Inject
    @RestClient
    OrderClientV3 orderClient;
    
    @Inject
    CertificateServiceV3 certificateService;
    
    @Inject
    EmailServiceV3 emailService;
    
    @ConfigProperty(name = "emission.background.certificate-enabled", defaultValue = "true")
    boolean certificateEnabled;
    
    @ConfigProperty(name = "emission.background.email-enabled", defaultValue = "true")
    boolean emailEnabled;
    
    @ConfigProperty(name = "emission.background.document-upload-enabled", defaultValue = "true")
    boolean documentUploadEnabled;
    
    /**
     * Esegue tutti i task in background per un ordine emesso.
     * Questo metodo viene eseguito su Virtual Thread per non bloccare il thread principale.
     * 
     * @param orderCode Codice dell'ordine
     * @param token Token di autorizzazione
     */
    @RunOnVirtualThread
    public void executeAsyncTasks(String orderCode, String token) {
        LOG.infof("V3 - Starting background tasks for order %s", orderCode);
        
        try {
            // Fase 1: Generazione certificato
            if (certificateEnabled) {
                statusRepository.updateState(orderCode, EmissionStatusV3.CERTIFICATE_IN_PROGRESS);
                generateCertificate(orderCode, token);
            }
            
            // Fase 2: Post-processing (email e upload documenti in parallelo)
            statusRepository.updateState(orderCode, EmissionStatusV3.POST_PROCESSING);
            executePostProcessingTasks(orderCode, token);
            
            // Fase 3: Finalizzazione
            finalizeOrder(orderCode, token);
            
            // Aggiorna stato a completato
            statusRepository.updateState(orderCode, EmissionStatusV3.COMPLETE);
            LOG.infof("V3 - Background tasks completed successfully for order %s", orderCode);
            
        } catch (Throwable t) {
            LOG.errorf(t, "V3 - Background tasks failed for order %s", orderCode);
            statusRepository.updateStateToFailed(orderCode, t.getMessage());
        }
    }
    
    /**
     * Genera il certificato per l'ordine
     */
    private void generateCertificate(String orderCode, String token) {
        LOG.debugf("V3 - Generating certificate for order %s", orderCode);
        
        try {
            // Genera il certificato utilizzando il servizio dedicato
            var certificate = certificateService.generateCertificate(orderCode, token)
                .await().indefinitely();
            
            LOG.infof("V3 - Certificate generated successfully for order %s", orderCode);
            
        } catch (Exception e) {
            LOG.errorf(e, "V3 - Certificate generation failed for order %s", orderCode);
            throw new RuntimeException("Certificate generation failed", e);
        }
    }
    
    /**
     * Esegue i task di post-processing in parallelo
     */
    private void executePostProcessingTasks(String orderCode, String token) {
        LOG.debugf("V3 - Starting post-processing tasks for order %s", orderCode);
        
        try {
            // Crea i Uni per le operazioni parallele
            Uni<Void> emailTask = emailEnabled ? 
                sendEmailNotification(orderCode, token) : 
                Uni.createFrom().voidItem();
                
            Uni<Void> documentTask = documentUploadEnabled ? 
                uploadDocuments(orderCode, token) : 
                Uni.createFrom().voidItem();
            
            // Esegue le operazioni in parallelo e attende il completamento
            Uni.join().all(emailTask, documentTask)
                .andFailFast() // Fallisce velocemente se una delle operazioni fallisce
                .await().indefinitely();
            
            LOG.infof("V3 - Post-processing tasks completed for order %s", orderCode);
            
        } catch (Exception e) {
            LOG.errorf(e, "V3 - Post-processing tasks failed for order %s", orderCode);
            throw new RuntimeException("Post-processing failed", e);
        }
    }
    
    /**
     * Invia notifica email
     */
    private Uni<Void> sendEmailNotification(String orderCode, String token) {
        LOG.debugf("V3 - Sending email notification for order %s", orderCode);
        
        return emailService.sendEmissionConfirmation(orderCode, token)
            .onItem().invoke(() -> 
                LOG.infof("V3 - Email notification sent for order %s", orderCode))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Email notification failed for order %s", orderCode));
    }
    
    /**
     * Carica documenti nel document manager
     */
    private Uni<Void> uploadDocuments(String orderCode, String token) {
        LOG.debugf("V3 - Uploading documents for order %s", orderCode);
        
        return certificateService.uploadCertificateToDocumentManager(orderCode, token)
            .onItem().invoke(() -> 
                LOG.infof("V3 - Documents uploaded for order %s", orderCode))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Document upload failed for order %s", orderCode));
    }
    
    /**
     * Finalizza l'ordine aggiornando il suo stato
     */
    private void finalizeOrder(String orderCode, String token) {
        LOG.debugf("V3 - Finalizing order %s", orderCode);
        
        try {
            // Aggiorna lo stato dell'ordine a confermato
            orderClient.confirmed(token, orderCode)
                .await().indefinitely();
            
            LOG.infof("V3 - Order %s finalized successfully", orderCode);
            
        } catch (Exception e) {
            LOG.errorf(e, "V3 - Order finalization failed for order %s", orderCode);
            // Non lanciamo eccezione qui perché l'emissione è già avvenuta con successo
            // Logghiamo solo l'errore
        }
    }
    
    /**
     * Esegue solo la generazione del certificato (per testing o casi specifici)
     */
    @RunOnVirtualThread
    public void executeOnlyCertificateGeneration(String orderCode, String token) {
        LOG.infof("V3 - Starting certificate-only task for order %s", orderCode);
        
        try {
            statusRepository.updateState(orderCode, EmissionStatusV3.CERTIFICATE_IN_PROGRESS);
            generateCertificate(orderCode, token);
            statusRepository.updateState(orderCode, EmissionStatusV3.COMPLETE);
            
        } catch (Throwable t) {
            LOG.errorf(t, "V3 - Certificate-only task failed for order %s", orderCode);
            statusRepository.updateStateToFailed(orderCode, t.getMessage());
        }
    }
}
