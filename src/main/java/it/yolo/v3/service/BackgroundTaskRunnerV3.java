package it.yolo.v3.service;

import it.yolo.model.request.EmissionManagerRequest;
import it.yolo.model.request.EmissionManagerRequestDto;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.EmissionStatusV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.service.ServiceEmissionManager;
import io.smallrye.common.annotation.Blocking;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

@ApplicationScoped
public class BackgroundTaskRunnerV3 {
    private static final Logger log = Logger.getLogger(BackgroundTaskRunnerV3.class);

    @Inject
    StatusRepositoryV3 statusRepository;

    @Inject
    ServiceEmissionManager emissionManager;

    @Blocking
    public void executeAsyncTasks(String orderCode, String token, EmissionRequestV3 request){
        try {
            statusRepository.update(orderCode, EmissionStatusV3.CERTIFICATE_IN_PROGRESS);

            EmissionManagerRequestDto dto = new EmissionManagerRequestDto();
            dto.setOrderCode(orderCode);
            if (request != null) {
                dto.setDiscount(request.discount());
                dto.setPaymentToken(request.paymentToken());
                dto.setPaymentType(request.paymentType());
                dto.setPaymentTransactionId(request.paymentTransactionId());
                dto.setSubscriptionId(request.subscriptionId());
            }
            EmissionManagerRequest v1Req = new EmissionManagerRequest();
            v1Req.setData(dto);

            OrderResponseDto res = emissionManager.emission(v1Req, token, orderCode);
            log.infof("V3 - emission completed for order %s", orderCode);
            statusRepository.update(orderCode, EmissionStatusV3.COMPLETE);
        } catch (Throwable t){
            log.errorf(t, "V3 - emission failed for order %s", orderCode);
            statusRepository.fail(orderCode);
        }
    }
}

