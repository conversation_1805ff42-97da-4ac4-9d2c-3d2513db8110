package it.yolo.v3.service;

import it.yolo.v3.domain.Policy;
import it.yolo.v3.service.CertificateService.Certificate;
import it.yolo.client.documentManager.DocumentManagerClient;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.LocalDateTime;

/**
 * Servizio per l'upload e gestione dei documenti di polizza.
 */
@ApplicationScoped
public class DocumentService {

    private static final Logger LOG = Logger.getLogger(DocumentService.class);

    @Inject
    @RestClient
    DocumentManagerClient documentManagerClient;
    
    /**
     * Carica i documenti di emissione nel sistema documentale.
     * 
     * @param policy Polizza emessa
     * @param certificate Certificato da caricare
     * @param token Token di autorizzazione
     * @return Uni che completa quando i documenti sono stati caricati
     */
    public Uni<Void> uploadEmissionDocuments(Policy policy, Certificate certificate, String token) {
        LOG.infof("Uploading emission documents for policy %s", policy.data().policyCode());
        
        return Uni.createFrom().item(() -> {
            // Simula l'upload dei documenti
            try {
                Thread.sleep(600); // Simula latenza del servizio documentale
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Document upload interrupted", e);
            }
            
            // Simula l'upload del certificato
            uploadCertificate(policy, certificate, token);
            
            // Simula l'upload di altri documenti correlati
            uploadPolicyDocuments(policy, token);
            
            LOG.infof("Emission documents uploaded successfully for policy %s", policy.data().policyCode());
            return null;
        });
    }
    
    private void uploadCertificate(Policy policy, Certificate certificate, String token) {
        LOG.debugf("Uploading certificate %s for policy %s", certificate.id(), policy.data().policyCode());
        
        // In una implementazione reale, qui ci sarebbe la chiamata al servizio documentale
        // es. documentManagerClient.upload(certificate, "policy", policy.policyNumber())
        
        // Simula l'upload
        DocumentMetadata metadata = new DocumentMetadata(
            certificate.id(),
            "certificate",
            certificate.format(),
            policy.data().policyCode(),
            certificate.generatedAt()
        );
        
        LOG.debugf("Certificate uploaded: %s", metadata);
    }
    
    private void uploadPolicyDocuments(Policy policy, String token) {
        LOG.debugf("Uploading policy documents for policy %s", policy.data().policyCode());
        
        // Simula l'upload di documenti aggiuntivi (condizioni di polizza, etc.)
        // In una implementazione reale, qui ci sarebbero chiamate multiple al servizio documentale
    }
    
    /**
     * Record per i metadati dei documenti
     */
    private record DocumentMetadata(
        String id,
        String type,
        String format,
        String policyNumber,
        LocalDateTime uploadedAt
    ) {}
}
