package it.yolo.v3.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.CustomerResponseDtoV3;
import it.yolo.v3.dto.response.OrderResponseDtoV3;
import it.yolo.v3.dto.response.PolicyResponseDtoV3;
import it.yolo.v3.dto.response.ProductResponseDtoV3;
import it.yolo.v3.strategy.EmissionContextV3;
import it.yolo.v3.strategy.EmissionStrategyFactoryV3;
import it.yolo.v3.strategy.EmissionStrategyV3;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Servizio di business V3 per la gestione delle polizze.
 * Coordina l'emissione delle polizze utilizzando il pattern Strategy.
 */
@ApplicationScoped
public class PolicyServiceV3 {
    
    private static final Logger LOG = Logger.getLogger(PolicyServiceV3.class);
    
    @Inject
    EmissionStrategyFactoryV3 strategyFactory;
    
    /**
     * Prepara e emette una polizza utilizzando la strategia appropriata.
     * 
     * @param orderCode Codice dell'ordine
     * @param request Richiesta di emissione
     * @param token Token di autorizzazione
     * @param order Dati dell'ordine
     * @param customer Dati del cliente
     * @param product Dati del prodotto
     * @return Uni contenente il numero della polizza emessa
     */
    public Uni<String> prepareAndEmit(String orderCode, EmissionRequestV3 request, String token,
                                     OrderResponseDtoV3 order, CustomerResponseDtoV3 customer, 
                                     ProductResponseDtoV3 product) {
        
        LOG.infof("V3 - Preparing emission for order %s", orderCode);
        
        return Uni.createFrom().item(() -> {
            // Crea il contesto di emissione
            EmissionContextV3 context = new EmissionContextV3(
                orderCode, token, order, customer, product, request
            );
            
            // Seleziona la strategia appropriata
            EmissionStrategyV3 strategy = strategyFactory.getStrategy(context);
            
            LOG.infof("V3 - Using strategy %s for order %s", strategy.getStrategyName(), orderCode);
            
            return context;
        })
        .flatMap(context -> {
            // Ottiene la strategia e esegue l'emissione
            EmissionStrategyV3 strategy = strategyFactory.getStrategy(context);
            return strategy.emit(context);
        })
        .map(policy -> {
            String policyNumber = policy.data().policyCode();
            LOG.infof("V3 - Policy emission completed: %s for order %s", policyNumber, orderCode);
            return policyNumber;
        })
        .onFailure().invoke(throwable -> 
            LOG.errorf(throwable, "V3 - Policy emission failed for order %s", orderCode));
    }
    
    /**
     * Versione semplificata che accetta solo il contesto
     */
    public Uni<String> prepareAndEmit(String orderCode, EmissionRequestV3 request, String token) {
        LOG.infof("V3 - Starting simplified emission for order %s", orderCode);
        
        // Questa versione richiede che i dati siano già stati recuperati dall'orchestratore
        // e passati tramite un contesto completo
        return Uni.createFrom().failure(
            new UnsupportedOperationException("Use the full prepareAndEmit method with all data")
        );
    }
    
    /**
     * Valida i dati necessari per l'emissione
     */
    public Uni<Void> validateEmissionData(String orderCode, OrderResponseDtoV3 order, 
                                         CustomerResponseDtoV3 customer, ProductResponseDtoV3 product) {
        
        LOG.debugf("V3 - Validating emission data for order %s", orderCode);
        
        return Uni.createFrom().item(() -> {
            // Validazione dell'ordine
            if (order == null || order.data() == null) {
                throw new IllegalArgumentException("Order data is required for emission");
            }
            
            if (!orderCode.equals(order.data().orderCode())) {
                throw new IllegalArgumentException("Order code mismatch");
            }
            
            // Validazione del cliente
            if (customer == null || customer.data() == null) {
                throw new IllegalArgumentException("Customer data is required for emission");
            }
            
            if (customer.data().id() == null) {
                throw new IllegalArgumentException("Customer ID is required");
            }
            
            // Validazione del prodotto
            if (product == null || product.data() == null) {
                throw new IllegalArgumentException("Product data is required for emission");
            }
            
            if (product.data().id() == null) {
                throw new IllegalArgumentException("Product ID is required");
            }
            
            // Validazione della configurazione del prodotto
            if (product.data().configuration() == null) {
                throw new IllegalArgumentException("Product configuration is required for emission");
            }
            
            // Verifica che esista una strategia per il tipo di emissione
            String emissionType = product.data().configuration().emissionType();
            if (!strategyFactory.isStrategyAvailable(emissionType)) {
                throw new IllegalArgumentException(
                    "No emission strategy available for type: " + emissionType);
            }
            
            LOG.debugf("V3 - Emission data validation passed for order %s", orderCode);
            return null;
        });
    }
    
    /**
     * Verifica se un prodotto può essere emesso
     */
    public boolean canEmitProduct(ProductResponseDtoV3 product) {
        if (product == null || product.data() == null || product.data().configuration() == null) {
            return false;
        }
        
        String emissionType = product.data().configuration().emissionType();
        return strategyFactory.isStrategyAvailable(emissionType);
    }
    
    /**
     * Ottiene il tipo di emissione per un prodotto
     */
    public String getEmissionType(ProductResponseDtoV3 product) {
        if (product == null || product.data() == null || product.data().configuration() == null) {
            return "internal"; // default
        }
        
        String emissionType = product.data().configuration().emissionType();
        return emissionType != null ? emissionType : "internal";
    }
}
