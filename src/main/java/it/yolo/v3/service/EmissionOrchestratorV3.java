package it.yolo.v3.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.EmissionResponseV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

@ApplicationScoped
public class EmissionOrchestratorV3 {
    private static final Logger log = Logger.getLogger(EmissionOrchestratorV3.class);

    @Inject
    StatusRepositoryV3 statusRepository;

    @Inject
    PolicyServiceV3 policyService;

    @Inject
    BackgroundTaskRunnerV3 backgroundRunner;

    public Uni<EmissionResponseV3> startEmissionProcess(String orderCode, EmissionRequestV3 request, String token){
        statusRepository.init(orderCode);
        return policyService.prepareAndEmit(orderCode, request, token)
                .invoke(policyNumber -> {
                    log.infof("V3 - Emission sync part done for order %s -> policy %s", orderCode, policyNumber);
                    backgroundRunner.executeAsyncTasks(orderCode, token);
                })
                .map(policyNumber -> new EmissionResponseV3(orderCode, policyNumber));
    }
}

