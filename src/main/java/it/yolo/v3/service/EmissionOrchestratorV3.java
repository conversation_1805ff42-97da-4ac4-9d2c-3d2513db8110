package it.yolo.v3.service;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.CustomerClientV3;
import it.yolo.v3.client.OrderClientV3;
import it.yolo.v3.client.ProductClientV3;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.CustomerResponseDtoV3;
import it.yolo.v3.dto.response.EmissionResponseV3;
import it.yolo.v3.dto.response.OrderResponseDtoV3;
import it.yolo.v3.dto.response.ProductResponseDtoV3;
import it.yolo.v3.repository.StatusRepositoryV3;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Orchestratore principale V3 per il processo di emissione.
 * Coordina il recupero dei dati, l'emissione della polizza e l'avvio dei task in background.
 */
@ApplicationScoped
public class EmissionOrchestratorV3 {
    
    private static final Logger LOG = Logger.getLogger(EmissionOrchestratorV3.class);
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    @Inject
    PolicyServiceV3 policyService;
    
    @Inject
    BackgroundTaskRunnerV3 backgroundRunner;
    
    @Inject
    @RestClient
    OrderClientV3 orderClient;
    
    @Inject
    @RestClient
    CustomerClientV3 customerClient;
    
    @Inject
    @RestClient
    ProductClientV3 productClient;
    
    /**
     * Avvia il processo di emissione per un ordine.
     * Questo è il metodo principale che coordina tutto il flusso.
     * 
     * @param orderCode Codice dell'ordine
     * @param request Richiesta di emissione
     * @param token Token di autorizzazione
     * @return Uni contenente la risposta di emissione
     */
    public Uni<EmissionResponseV3> startEmissionProcess(String orderCode, EmissionRequestV3 request, String token) {
        LOG.infof("V3 - Starting emission process for order %s", orderCode);
        
        // 1. Inizializza lo stato nel repository
        statusRepository.init(orderCode);
        
        // 2. Recupera i dati necessari in parallelo
        return fetchDataInParallel(orderCode, token)
            .flatMap(dataBundle -> {
                // 3. Valida i dati recuperati
                return policyService.validateEmissionData(
                    orderCode, 
                    dataBundle.order(), 
                    dataBundle.customer(), 
                    dataBundle.product()
                ).replaceWith(dataBundle);
            })
            .flatMap(dataBundle -> {
                // 4. Esegue l'emissione della polizza
                return policyService.prepareAndEmit(
                    orderCode, 
                    request, 
                    token,
                    dataBundle.order(),
                    dataBundle.customer(),
                    dataBundle.product()
                );
            })
            .invoke(policyNumber -> {
                // 5. Aggiorna lo stato con il numero di polizza
                statusRepository.updateWithPolicyNumber(orderCode, policyNumber);
                
                // 6. Avvia i task in background (fire-and-forget)
                LOG.infof("V3 - Emission sync part done for order %s -> policy %s", orderCode, policyNumber);
                backgroundRunner.executeAsyncTasks(orderCode, token);
            })
            .map(policyNumber -> {
                // 7. Restituisce la risposta immediata
                return new EmissionResponseV3(orderCode, policyNumber);
            })
            .onFailure().invoke(throwable -> {
                LOG.errorf(throwable, "V3 - Emission process failed for order %s", orderCode);
                statusRepository.updateStateToFailed(orderCode, throwable.getMessage());
            });
    }
    
    /**
     * Recupera tutti i dati necessari in parallelo per ottimizzare le performance.
     */
    private Uni<DataBundleV3> fetchDataInParallel(String orderCode, String token) {
        LOG.debugf("V3 - Fetching data in parallel for order %s", orderCode);
        
        // Recupera l'ordine
        Uni<OrderResponseDtoV3> orderUni = orderClient.findByOrderCodeUnchecked(token, orderCode)
            .onItem().invoke(order -> 
                LOG.debugf("V3 - Order data fetched for %s", orderCode))
            .onFailure().invoke(throwable -> 
                LOG.errorf(throwable, "V3 - Failed to fetch order %s", orderCode));
        
        // Recupera l'ordine prima per ottenere customer e product ID
        return orderUni.flatMap(order -> {
            if (order.data() == null) {
                return Uni.createFrom().failure(
                    new RuntimeException("Order data is null for order: " + orderCode));
            }
            
            // Estrae gli ID necessari dall'ordine
            Integer customerId = order.data().customerId();
            String productId = order.data().productId();
            
            if (customerId == null) {
                return Uni.createFrom().failure(
                    new RuntimeException("Customer ID not found in order: " + orderCode));
            }
            
            if (productId == null) {
                return Uni.createFrom().failure(
                    new RuntimeException("Product ID not found in order: " + orderCode));
            }
            
            // Recupera customer e product in parallelo
            Uni<CustomerResponseDtoV3> customerUni = customerClient.findById(token, customerId.longValue())
                .onItem().invoke(customer -> 
                    LOG.debugf("V3 - Customer data fetched for order %s", orderCode))
                .onFailure().invoke(throwable -> 
                    LOG.errorf(throwable, "V3 - Failed to fetch customer %d for order %s", customerId, orderCode));
            
            Uni<ProductResponseDtoV3> productUni = productClient.findById(token, "it", Long.parseLong(productId))
                .onItem().invoke(product -> 
                    LOG.debugf("V3 - Product data fetched for order %s", orderCode))
                .onFailure().invoke(throwable -> 
                    LOG.errorf(throwable, "V3 - Failed to fetch product %s for order %s", productId, orderCode));
            
            // Combina tutti i risultati
            return Uni.combine().all().unis(orderUni, customerUni, productUni)
                .asTuple()
                .map(tuple -> {
                    OrderResponseDtoV3 orderData = tuple.getItem1();
                    CustomerResponseDtoV3 customerData = tuple.getItem2();
                    ProductResponseDtoV3 productData = tuple.getItem3();
                    
                    LOG.infof("V3 - All data fetched successfully for order %s", orderCode);
                    return new DataBundleV3(orderData, customerData, productData);
                });
        });
    }
    
    /**
     * Verifica se un ordine può essere emesso.
     */
    public Uni<Boolean> canEmitOrder(String orderCode, String token) {
        LOG.debugf("V3 - Checking if order %s can be emitted", orderCode);
        
        return fetchDataInParallel(orderCode, token)
            .map(dataBundle -> {
                // Verifica se il prodotto può essere emesso
                boolean canEmit = policyService.canEmitProduct(dataBundle.product());
                
                LOG.debugf("V3 - Order %s can be emitted: %s", orderCode, canEmit);
                return canEmit;
            })
            .onFailure().recoverWithItem(false);
    }
    
    /**
     * Ottiene informazioni sull'ordine senza avviare l'emissione.
     */
    public Uni<OrderInfoV3> getOrderInfo(String orderCode, String token) {
        LOG.debugf("V3 - Getting order info for %s", orderCode);
        
        return fetchDataInParallel(orderCode, token)
            .map(dataBundle -> {
                String emissionType = policyService.getEmissionType(dataBundle.product());
                boolean canEmit = policyService.canEmitProduct(dataBundle.product());
                
                return new OrderInfoV3(
                    orderCode,
                    dataBundle.order().data().customerId(),
                    dataBundle.order().data().productId(),
                    emissionType,
                    canEmit,
                    dataBundle.customer().data().primaryMail(),
                    dataBundle.product().data().description()
                );
            });
    }
    
    /**
     * Record per raggruppare i dati recuperati
     */
    private record DataBundleV3(
        OrderResponseDtoV3 order,
        CustomerResponseDtoV3 customer,
        ProductResponseDtoV3 product
    ) {}
    
    /**
     * Record per le informazioni dell'ordine
     */
    public record OrderInfoV3(
        String orderCode,
        Integer customerId,
        String productId,
        String emissionType,
        boolean canEmit,
        String customerEmail,
        String productDescription
    ) {}
}
