package it.yolo.v3.port;

import it.yolo.v3.domain.Order;
import io.smallrye.mutiny.Uni;

/**
 * Port per l'accesso ai dati degli ordini.
 * Astrae l'accesso al servizio Order esterno.
 */
public interface OrderPort {
    
    /**
     * Recupera un ordine per codice
     * 
     * @param orderCode Codice dell'ordine
     * @param token Token di autorizzazione
     * @return Uni con l'ordine trovato
     */
    Uni<Order> findByCode(String orderCode, String token);
    
    /**
     * Aggiorna lo stato di un ordine
     * 
     * @param orderCode Codice dell'ordine
     * @param newState Nuovo stato
     * @param token Token di autorizzazione
     * @return Uni con l'ordine aggiornato
     */
    Uni<Order> updateState(String orderCode, String newState, String token);
    
    /**
     * Marca un ordine come completato
     * 
     * @param orderCode Codice dell'ordine
     * @param token Token di autorizzazione
     * @return Uni con l'ordine aggiornato
     */
    Uni<Order> markAsComplete(String orderCode, String token);
    
    /**
     * Marca un ordine come fallito
     * 
     * @param orderCode Codice dell'ordine
     * @param errorMessage Messaggio di errore
     * @param token Token di autorizzazione
     * @return Uni con l'ordine aggiornato
     */
    Uni<Order> markAsFailed(String orderCode, String errorMessage, String token);
}
