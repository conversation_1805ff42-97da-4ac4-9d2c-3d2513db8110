package it.yolo.v3.port;

import it.yolo.v3.domain.Customer;
import io.smallrye.mutiny.Uni;

/**
 * Port per l'accesso ai dati dei clienti.
 * Astrae l'accesso al servizio Customer esterno.
 */
public interface CustomerPort {
    
    /**
     * Recupera un cliente per ID
     * 
     * @param customerId ID del cliente
     * @param token Token di autorizzazione
     * @return Uni con il cliente trovato
     */
    Uni<Customer> findById(String customerId, String token);
    
    /**
     * Recupera un cliente per codice fiscale
     * 
     * @param taxCode Codice fiscale
     * @param token Token di autorizzazione
     * @return Uni con il cliente trovato
     */
    Uni<Customer> findByTaxCode(String taxCode, String token);
    
    /**
     * Recupera un cliente per NDG (Numero Di Gestione)
     * 
     * @param ndg NDG del cliente
     * @param token Token di autorizzazione
     * @return Uni con il cliente trovato
     */
    Uni<Customer> findByNdg(String ndg, String token);
}
