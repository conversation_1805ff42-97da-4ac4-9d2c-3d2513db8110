package it.yolo.v3.port;

import it.yolo.v3.domain.Product;
import io.smallrye.mutiny.Uni;

/**
 * Port per l'accesso ai dati dei prodotti.
 * Astrae l'accesso al servizio Product esterno.
 */
public interface ProductPort {
    
    /**
     * Recupera un prodotto per ID
     * 
     * @param productId ID del prodotto
     * @param token Token di autorizzazione
     * @return Uni con il prodotto trovato
     */
    Uni<Product> findById(String productId, String token);
    
    /**
     * Recupera un prodotto per codice
     * 
     * @param productCode Codice del prodotto
     * @param token Token di autorizzazione
     * @return Uni con il prodotto trovato
     */
    Uni<Product> findByCode(String productCode, String token);
}
