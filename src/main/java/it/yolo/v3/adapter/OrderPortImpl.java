package it.yolo.v3.adapter;

import it.yolo.client.order.OrderClient;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.dto.response.DataOrderResponseDto;
import it.yolo.client.order.dto.request.OrderRequestDto;
import it.yolo.client.order.dto.request.DataDtoRequest;
import it.yolo.v3.domain.Order;
import it.yolo.v3.port.OrderPort;
import io.smallrye.mutiny.Uni;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;

/**
 * Implementazione reale del OrderPort che utilizza OrderClient.
 * Converte i DTO legacy nei modelli di dominio V3.
 */
@ApplicationScoped
public class OrderPortImpl implements OrderPort {
    
    private static final Logger LOG = Logger.getLogger(OrderPortImpl.class);
    
    @Inject
    @RestClient
    OrderClient orderClient;
    
    @Override
    public Uni<Order> findByCode(String orderCode, String token) {
        LOG.infof("Retrieving order by code: %s", orderCode);
        
        return Uni.createFrom().item(() -> orderClient.getOrder(token, orderCode))
            .flatMap(response -> {
                try {
                    if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                        OrderResponseDto orderDto = response.readEntity(OrderResponseDto.class);
                        Order order = mapToOrder(orderDto);
                        LOG.infof("Order retrieved successfully: %s", orderCode);
                        return Uni.createFrom().item(order);
                    } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                        LOG.warnf("Order not found: %s", orderCode);
                        return Uni.createFrom().failure(new NotFoundException("Order not found: " + orderCode));
                    } else {
                        String errorMsg = String.format("Order service error [code=%s]: HTTP %d", 
                                                       orderCode, response.getStatus());
                        LOG.error(errorMsg);
                        return Uni.createFrom().failure(new RuntimeException(errorMsg));
                    }
                } finally {
                    response.close();
                }
            })
            .onFailure().recoverWithUni(failure -> {
                LOG.errorf(failure, "Error retrieving order %s", orderCode);
                return Uni.createFrom().failure(
                    new RuntimeException("Order service unavailable", failure)
                );
            });
    }
    
    @Override
    public Uni<Order> updateState(String orderCode, String newState, String token) {
        LOG.infof("Updating order %s state to: %s", orderCode, newState);
        
        // Prima recupera l'ordine corrente
        return findByCode(orderCode, token)
            .flatMap(currentOrder -> {
                // Crea la richiesta di aggiornamento
                OrderRequestDto updateRequest = new OrderRequestDto();
                DataDtoRequest data = new DataDtoRequest();
                data.setOrderCode(orderCode);
                data.setAnagState(newState);
                updateRequest.setDataDtoRequest(data);
                
                return Uni.createFrom().item(() -> orderClient.update(token, updateRequest, orderCode))
                    .flatMap(response -> {
                        try {
                            if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                                OrderResponseDto updatedDto = response.readEntity(OrderResponseDto.class);
                                Order updatedOrder = mapToOrder(updatedDto);
                                LOG.infof("Order state updated successfully: %s -> %s", orderCode, newState);
                                return Uni.createFrom().item(updatedOrder);
                            } else {
                                String errorMsg = String.format("Failed to update order state [code=%s]: HTTP %d", 
                                                               orderCode, response.getStatus());
                                LOG.error(errorMsg);
                                return Uni.createFrom().failure(new RuntimeException(errorMsg));
                            }
                        } finally {
                            response.close();
                        }
                    });
            });
    }
    
    @Override
    public Uni<Order> markAsComplete(String orderCode, String token) {
        LOG.infof("Marking order as complete: %s", orderCode);
        
        return Uni.createFrom().item(() -> orderClient.confirmed(token, orderCode))
            .flatMap(response -> {
                try {
                    if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                        LOG.infof("Order marked as complete: %s", orderCode);
                        // Recupera l'ordine aggiornato
                        return findByCode(orderCode, token);
                    } else {
                        String errorMsg = String.format("Failed to mark order as complete [code=%s]: HTTP %d", 
                                                       orderCode, response.getStatus());
                        LOG.error(errorMsg);
                        return Uni.createFrom().failure(new RuntimeException(errorMsg));
                    }
                } finally {
                    response.close();
                }
            });
    }
    
    @Override
    public Uni<Order> markAsFailed(String orderCode, String errorMessage, String token) {
        LOG.errorf("Marking order as failed: %s - %s", orderCode, errorMessage);
        
        return Uni.createFrom().item(() -> orderClient.failed(token, orderCode))
            .flatMap(response -> {
                try {
                    if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                        LOG.infof("Order marked as failed: %s", orderCode);
                        // Recupera l'ordine aggiornato
                        return findByCode(orderCode, token);
                    } else {
                        String errorMsg = String.format("Failed to mark order as failed [code=%s]: HTTP %d", 
                                                       orderCode, response.getStatus());
                        LOG.error(errorMsg);
                        return Uni.createFrom().failure(new RuntimeException(errorMsg));
                    }
                } finally {
                    response.close();
                }
            });
    }
    
    /**
     * Converte OrderResponseDto nel modello di dominio Order V3
     */
    private Order mapToOrder(OrderResponseDto dto) {
        if (dto == null || dto.getResponse() == null) {
            throw new IllegalArgumentException("Invalid order DTO");
        }
        
        DataOrderResponseDto data = dto.getResponse();
        
        return new Order(
            data.getId() != null ? data.getId().toString() : null,
            data.getOrderCode(),
            data.getCustomerId() != null ? data.getCustomerId().toString() : null,
            data.getProductId(),
            extractState(data),
            data.getPaymentToken(),
            data.getPaymentType(),
            data.getPaymentTransactionId(),
            data.getStart_date(),
            data.getExpiration_date()
        );
    }
    
    /**
     * Estrae lo stato dell'ordine dai dati legacy
     */
    private String extractState(DataOrderResponseDto data) {
        // Logica per determinare lo stato basandosi sui dati legacy
        if (data.getAnagStates() != null) {
            // Cerca lo stato più recente negli anagStates
            return "Confirmed"; // Semplificazione per ora
        }
        return "Unknown";
    }
}
