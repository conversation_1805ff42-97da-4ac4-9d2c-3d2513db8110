package it.yolo.v3.adapter;

import it.yolo.client.product.ProductClient;
import it.yolo.client.product.response.dto.ProductResponseDto;
import it.yolo.client.dto.iad.response.DataProduct;
import it.yolo.v3.domain.Product;
import it.yolo.v3.port.ProductPort;
import io.smallrye.mutiny.Uni;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import java.math.BigDecimal;

/**
 * Implementazione reale del ProductPort che utilizza ProductClient.
 * Converte i DTO legacy nei modelli di dominio V3.
 */
@ApplicationScoped
public class ProductPortImpl implements ProductPort {
    
    private static final Logger LOG = Logger.getLogger(ProductPortImpl.class);
    
    @Inject
    @RestClient
    ProductClient productClient;
    
    @Override
    public Uni<Product> findById(String productId, String token) {
        LOG.infof("Retrieving product by ID: %s", productId);
        
        return Uni.createFrom().item(() -> {
            try {
                Long id = Long.parseLong(productId);
                return productClient.findById(token, "it", id); // Default language "it"
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException(
                    String.format("Invalid product ID format: '%s'. Must be a numeric value", productId), e);
            }
        }).flatMap(response -> {
            try {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    ProductResponseDto productDto = response.readEntity(ProductResponseDto.class);
                    Product product = mapToProduct(productDto);
                    LOG.infof("Product retrieved: %s", productId);
                    return Uni.createFrom().item(product);
                } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                    LOG.warnf("Product not found for ID: %s", productId);
                    return Uni.createFrom().failure(new NotFoundException("Product not found"));
                } else {
                    String errorMsg = String.format(
                        "Product service error [ID=%s]: HTTP %d",
                        productId, response.getStatus()
                    );
                    LOG.error(errorMsg);
                    return Uni.createFrom().failure(new RuntimeException(errorMsg));
                }
            } finally {
                response.close();
            }
        }).onFailure().recoverWithUni(failure -> {
            LOG.errorf(failure, "Error retrieving product by ID %s", productId);
            return Uni.createFrom().failure(
                new RuntimeException("Product service unavailable", failure)
            );
        });
    }
    
    @Override
    public Uni<Product> findByCode(String productCode, String token) {
        LOG.infof("Retrieving product by code: %s", productCode);
        
        // Il ProductClient non ha un metodo diretto per code,
        // quindi per ora implementiamo una logica semplificata
        return Uni.createFrom().failure(
            new UnsupportedOperationException("findByCode not implemented in legacy client")
        );
    }
    
    /**
     * Converte ProductResponseDto nel modello di dominio Product V3
     */
    private Product mapToProduct(ProductResponseDto dto) {
        if (dto == null || dto.getDataProduct() == null) {
            throw new IllegalArgumentException("Invalid product DTO");
        }
        
        DataProduct data = dto.getDataProduct();
        
        return new Product(
            data.getId() != null ? data.getId().toString() : null,
            data.getCode(),
            data.getDescription(),
            data.getInsuranceCompany(),
            parsePrice(data.getPrice()),
            determineEmissionType(data),
            data.getQuotatorType(),
                data.getBusiness() != null ? data.getBusiness().toString() : null
        );
    }
    
    /**
     * Converte il prezzo da String a BigDecimal
     */
    private BigDecimal parsePrice(String priceStr) {
        if (priceStr == null || priceStr.isBlank()) {
            return BigDecimal.ZERO;
        }
        
        try {
            return new BigDecimal(priceStr);
        } catch (NumberFormatException e) {
            LOG.warnf("Invalid price format: %s, defaulting to 0", priceStr);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Determina il tipo di emissione basandosi sui dati del prodotto
     */
    private String determineEmissionType(DataProduct data) {
        // Logica per determinare se è INTERNAL o EXTERNAL
        // Basata sulla configurazione del prodotto
        
        if (data.getProperties() != null && data.getProperties().hasNonNull("emission")) {
            String emissionConfig = data.getProperties().get("emission").asText();
            if ("external".equalsIgnoreCase(emissionConfig)) {
                return "EXTERNAL";
            }
        }
        
        // Default a INTERNAL se non specificato
        return "INTERNAL";
    }
}
