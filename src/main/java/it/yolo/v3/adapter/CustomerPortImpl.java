package it.yolo.v3.adapter;

import it.yolo.client.customer.CustomerClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.customer.dto.DataCustomerResponseDto;
import it.yolo.v3.domain.Customer;
import it.yolo.v3.port.CustomerPort;
import io.smallrye.mutiny.Uni;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

/**
 * Implementazione reale del CustomerPort che utilizza CustomerClient.
 * Converte i DTO legacy nei modelli di dominio V3.
 */
@ApplicationScoped
public class CustomerPortImpl implements CustomerPort {
    
    private static final Logger LOG = Logger.getLogger(CustomerPortImpl.class);
    
    @Inject
    @RestClient
    CustomerClient customerClient;
    
    @Override
    public Uni<Customer> findById(String customerId, String token) {
        LOG.infof("Retrieving customer by ID: %s", customerId);
        
        return Uni.createFrom().item(() -> {
            try {
                Long id = Long.parseLong(customerId);
                return customerClient.findById(token, id);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException(
                    String.format("Invalid customer ID format: '%s'. Must be a numeric value", customerId), e);
            }
        }).flatMap(response -> {
            try {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    CustomerResponseDto customerDto = response.readEntity(CustomerResponseDto.class);
                    Customer customer = mapToCustomer(customerDto);
                    LOG.infof("Customer retrieved: %s", customerId);
                    return Uni.createFrom().item(customer);
                } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                    LOG.warnf("Customer not found for ID: %s", customerId);
                    return Uni.createFrom().failure(new NotFoundException("Customer not found"));
                } else {
                    String errorMsg = String.format(
                        "Customer service error [ID=%s]: HTTP %d",
                        customerId, response.getStatus()
                    );
                    LOG.error(errorMsg);
                    return Uni.createFrom().failure(new RuntimeException(errorMsg));
                }
            } finally {
                response.close();
            }
        }).onFailure().recoverWithUni(failure -> {
            LOG.errorf(failure, "Error retrieving customer by ID %s", customerId);
            return Uni.createFrom().failure(
                new RuntimeException("Customer service unavailable", failure)
            );
        });
    }
    
    @Override
    public Uni<Customer> findByTaxCode(String taxCode, String token) {
        LOG.infof("Retrieving customer by tax code: %s", taxCode);
        
        // Il CustomerClient non ha un metodo diretto per tax code,
        // quindi per ora implementiamo una logica semplificata
        return Uni.createFrom().failure(
            new UnsupportedOperationException("findByTaxCode not implemented in legacy client")
        );
    }
    
    @Override
    public Uni<Customer> findByNdg(String ndg, String token) {
        LOG.infof("Retrieving customer by NDG: %s", ndg);
        
        return Uni.createFrom().item(() -> customerClient.findByNdg(token, ndg))
            .flatMap(response -> {
                try {
                    if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                        CustomerResponseDto customerDto = response.readEntity(CustomerResponseDto.class);
                        Customer customer = mapToCustomer(customerDto);
                        LOG.infof("Customer retrieved by NDG: %s", ndg);
                        return Uni.createFrom().item(customer);
                    } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                        LOG.warnf("Customer not found for NDG: %s", ndg);
                        return Uni.createFrom().failure(new NotFoundException("Customer not found"));
                    } else {
                        String errorMsg = String.format(
                            "Customer service error [NDG=%s]: HTTP %d",
                            ndg, response.getStatus()
                        );
                        LOG.error(errorMsg);
                        return Uni.createFrom().failure(new RuntimeException(errorMsg));
                    }
                } finally {
                    response.close();
                }
            }).onFailure().recoverWithUni(failure -> {
                LOG.errorf(failure, "Error retrieving customer by NDG %s", ndg);
                return Uni.createFrom().failure(
                    new RuntimeException("Customer service unavailable", failure)
                );
            });
    }
    
    /**
     * Converte CustomerResponseDto nel modello di dominio Customer V3
     */
    private Customer mapToCustomer(CustomerResponseDto dto) {
        if (dto == null || dto.getData() == null) {
            throw new IllegalArgumentException("Invalid customer DTO");
        }
        
        DataCustomerResponseDto data = dto.getData();
        
        return new Customer(
            data.getId() != null ? data.getId().toString() : null,
            data.getCustomerCode(),
            data.getName(),
            data.getSurname(),
            data.getTaxCode(),
            data.getPrimaryMail(),
            data.getPrimaryPhone(),
            buildAddress(data),
            data.getCity(),
            data.getZipCode(),
            data.getProvince()
        );
    }
    
    /**
     * Costruisce l'indirizzo completo dai dati del cliente
     */
    private String buildAddress(DataCustomerResponseDto data) {
        StringBuilder address = new StringBuilder();
        
        if (data.getStreet() != null && !data.getStreet().isBlank()) {
            address.append(data.getStreet());
        }
        
        if (data.getStreetNumber() != null && !data.getStreetNumber().isBlank()) {
            if (address.length() > 0) {
                address.append(" ");
            }
            address.append(data.getStreetNumber());
        }
        
        return address.length() > 0 ? address.toString() : null;
    }
}
