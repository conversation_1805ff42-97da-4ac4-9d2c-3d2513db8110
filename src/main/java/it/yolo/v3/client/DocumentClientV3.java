package it.yolo.v3.client;

import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il servizio dei documenti
 */
@RegisterRestClient(configKey = "iad-document")
public interface DocumentClientV3 {

    @POST
    @Path("/upload")
    Uni<Response> uploadDocument(Object uploadRequest);

    @POST
    @Path("/generate")
    Uni<Response> generateCertificate(Object certificateRequest);

    @POST
    @Path("/downloadLink")
    Uni<Response> downloadLink(Object downloadRequest);

    @POST
    @Path("/download")
    Uni<Response> download(Object downloadRequest);
}
