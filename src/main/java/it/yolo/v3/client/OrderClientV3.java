package it.yolo.v3.client;

import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import io.smallrye.mutiny.Uni;
import it.yolo.exception.EmissionFlowException;
import it.yolo.v3.dto.response.OrderResponseDtoV3;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il servizio degli ordini
 */
@RegisterRestClient(configKey = "iad-order")
@Path("/v3")
public interface OrderClientV3 {

    @GET
    @Path("/code/{order_code}")
    Uni<OrderResponseDtoV3> findByOrderCode(
            @RestHeader("Authorization") String token, 
            @PathParam("order_code") String orderCode
    );

    @GET
    @Path("order/unchecked/code/{orderCode}")
    Uni<OrderResponseDtoV3> findByOrderCodeUnchecked(
            @RestHeader("Authorization") String token, 
            @PathParam("orderCode") String orderCode
    );

    @GET
    @Path("order/{id}")
    Uni<OrderResponseDtoV3> findById(
            @RestHeader("Authorization") String token, 
            @PathParam("id") Long id
    );

    @GET
    @Path("order/unchecked/{id}")
    Uni<OrderResponseDtoV3> findByIdUnchecked(
            @RestHeader("Authorization") String token, 
            @PathParam("id") Long id
    );

    @PUT
    @Path("order/confirmed")
    Uni<Response> confirmed(
            @RestHeader("Authorization") String token, 
            @QueryParam("order_code") String orderCode
    );

    @PUT
    @Path("order/failed")
    Uni<Response> failed(
            @RestHeader("Authorization") String token, 
            @QueryParam("orderCode") String orderCode
    );

    @GET
    @Path("order/start/{orderCode}")
    Uni<Response> updateStartDate(
            @RestHeader("Authorization") String token, 
            @PathParam("orderCode") String orderCode
    );

    @GET
    @Path("/order/checkExistingPolicy/{orderCode}")
    Uni<Response> checkExistingPolicy(
            @PathParam("orderCode") String orderCode, 
            @RestHeader("Authorization") String token
    );

    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 400 || response.getStatus() == 401 || 
            response.getStatus() == 403 || response.getStatus() == 404 ||
            response.getStatus() == 500 || response.getStatus() == 502 || 
            response.getStatus() == 503) {
            return new EmissionFlowException("Errore chiamata order V3", 
                "Status response order: " + response.getStatus());
        }
        return null;
    }
}
