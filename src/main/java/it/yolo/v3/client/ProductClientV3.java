package it.yolo.v3.client;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.response.ProductResponseDtoV3;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;

/**
 * Client REST V3 per il servizio dei prodotti
 */
@RegisterRestClient(configKey = "iad-product")
@Path("/v3/products")
public interface ProductClientV3 {

    @GET
    @Path("{id}")
    Uni<ProductResponseDtoV3> findById(
            @RestHeader("Authorization") String token, 
            @RestHeader("x-tenant-language") String language, 
            @PathParam("id") Long id
    );

    @GET
    @Path("code/{code}")
    Uni<ProductResponseDtoV3> findByCode(
            @RestHeader("Authorization") String token, 
            @RestHeader("x-tenant-language") String language, 
            @PathParam("code") String code
    );
}
