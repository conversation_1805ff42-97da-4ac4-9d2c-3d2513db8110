package it.yolo.v3.client;

import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.PolicyRequestDtoV3;
import it.yolo.v3.dto.response.PolicyResponseDtoV3;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il servizio delle polizze
 */
@RegisterRestClient(configKey = "iad-policy")
@Path("v2")
public interface PolicyClientV3 {

    @POST
    @Path("policies")
    Uni<PolicyResponseDtoV3> create(
            @RestHeader("Authorization") String token, 
            PolicyRequestDtoV3 request
    );

    @GET
    @Path("policies/{id}/customer")
    Uni<Response> get(
            @RestHeader("Authorization") String token, 
            @PathParam("id") Integer id
    );

    @PUT
    @Path("policies/emission")
    Uni<Response> updateAfterEmission(
            @RestHeader("Authorization") String token,
            PolicyRequestDtoV3 request
    );

    @GET
    @Path("code/generation/{product_code}")
    Uni<Response> getNextPolicy(
            @RestHeader("Authorization") String token, 
            @PathParam("product_code") String productCode
    );

    @GET
    @Path("states/state/{name}")
    Uni<Response> getStateByName(
            @RestHeader("Authorization") String token, 
            @PathParam("name") String name
    );

    @GET
    @Path("policies/code/{policyCode}")
    Uni<PolicyResponseDtoV3> findByCode(
            @RestHeader("Authorization") String token, 
            @PathParam("policyCode") String policyCode
    );
}
