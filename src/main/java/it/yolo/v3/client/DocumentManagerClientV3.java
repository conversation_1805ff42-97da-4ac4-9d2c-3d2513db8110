package it.yolo.v3.client;

import com.fasterxml.jackson.databind.JsonNode;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il Document Manager
 */
@RegisterRestClient(configKey = "iad-document-manager")
@Path("v1/document-manager")
public interface DocumentManagerClientV3 {
    
    @POST
    @Path("docs")
    Uni<Response> addDocument(
            @RestHeader("Authorization") String token, 
            @QueryParam("insuranceId") String insuranceId,
            JsonNode request
    );
}
