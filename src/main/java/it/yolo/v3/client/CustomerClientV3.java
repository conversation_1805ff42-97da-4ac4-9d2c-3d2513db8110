package it.yolo.v3.client;

import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import io.smallrye.mutiny.Uni;
import it.yolo.exception.EmissionFlowException;
import it.yolo.v3.dto.response.CustomerResponseDtoV3;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il servizio dei clienti
 */
@RegisterRestClient(configKey = "iad-customer")
@Path("v1/customers")
public interface CustomerClientV3 {

    @GET
    @Path("{id}")
    Uni<CustomerResponseDtoV3> findById(
            @RestHeader("Authorization") String token,
            @PathParam("id") Long id
    );

    @GET
    @Path("ndg/{ndg}")
    Uni<CustomerResponseDtoV3> findByNdg(
            @RestHeader("Authorization") String token, 
            @PathParam("ndg") String ndg
    );

    @PUT
    @Path("{tax_code}")
    Uni<Response> updateByTaxCode(
            @PathParam("tax_code") String taxCode, 
            Object customerRequest
    );

    @PUT
    @Path("ndg/{ndg}")
    Uni<Response> updateByNdg(
            @PathParam("ndg") String ndg, 
            Object customerRequest, 
            @RestHeader("Authorization") String token
    );

    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 400 || response.getStatus() == 401 || 
            response.getStatus() == 403 || response.getStatus() == 404 ||
            response.getStatus() == 422 || response.getStatus() == 500 || 
            response.getStatus() == 502 || response.getStatus() == 503) {
            return new EmissionFlowException("Errore chiamata customer V3", 
                "Status response customer: " + response.getStatus());
        }
        return null;
    }
}
