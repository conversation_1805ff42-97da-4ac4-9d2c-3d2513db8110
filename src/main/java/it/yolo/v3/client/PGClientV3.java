package it.yolo.v3.client;

import com.fasterxml.jackson.databind.JsonNode;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

/**
 * Client REST V3 per il Payment Gateway
 */
@Path("/")
@RegisterRestClient(configKey = "provider-gateway")
public interface PGClientV3 {

    @POST
    @Path("v2/emission")
    Uni<Response> emission(JsonNode request);

    @POST
    @Path("v2/info")
    Uni<Response> getCertificate(Object request);

    @POST
    @Path("v2/emission/callback")
    Uni<Response> callback(Object request);

    @POST
    @Path("v2/receipt")
    Uni<Response> receipt(Object request);

    @POST
    @Path("v2/certificate")
    Uni<Response> certificateV2(Object certificateRequest);

    @POST
    @Path("/v1/certificate")
    Uni<Response> certificate(Object certificateRequest);
}
