package it.yolo.v3.repository;

import it.yolo.v3.dto.response.EmissionStatusV3;
import it.yolo.v3.dto.response.EmissionStatusDetailV3;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Repository in-memory per gestire gli stati delle emissioni V3.
 * Utilizza ConcurrentHashMap per garantire thread-safety.
 * 
 * In un ambiente di produzione reale si userebbe Redis o un DB,
 * ma per questa implementazione una mappa concorrente è sufficiente.
 */
@ApplicationScoped
public class StatusRepositoryV3 {
    
    private static final Logger LOG = Logger.getLogger(StatusRepositoryV3.class);
    
    private final ConcurrentHashMap<String, EmissionStatusDetailV3> statusMap = new ConcurrentHashMap<>();
    
    /**
     * Inizializza lo stato per un nuovo ordine
     */
    public void init(String orderCode) {
        LOG.infof("V3 - Initializing status for order %s", orderCode);
        EmissionStatusDetailV3 status = new EmissionStatusDetailV3(
            orderCode,
            null, // policyNumber sarà impostato dopo l'emissione
            EmissionStatusV3.PENDING,
            "Processo appena avviato",
            Instant.now()
        );
        statusMap.put(orderCode, status);
    }
    
    /**
     * Aggiorna lo stato con il numero di polizza dopo l'emissione
     */
    public void updateWithPolicyNumber(String orderCode, String policyNumber) {
        LOG.infof("V3 - Updating status for order %s with policy %s", orderCode, policyNumber);
        statusMap.computeIfPresent(orderCode, (key, current) -> 
            new EmissionStatusDetailV3(
                current.orderCode(),
                policyNumber,
                EmissionStatusV3.EMITTED,
                "Polizza emessa, task background avviati",
                Instant.now()
            )
        );
    }
    
    /**
     * Aggiorna lo stato del processo
     */
    public void updateState(String orderCode, EmissionStatusV3 newStatus) {
        updateState(orderCode, newStatus, newStatus.getDescription());
    }
    
    /**
     * Aggiorna lo stato del processo con dettagli personalizzati
     */
    public void updateState(String orderCode, EmissionStatusV3 newStatus, String details) {
        LOG.infof("V3 - Updating status for order %s to %s", orderCode, newStatus);
        statusMap.computeIfPresent(orderCode, (key, current) -> 
            new EmissionStatusDetailV3(
                current.orderCode(),
                current.policyNumber(),
                newStatus,
                details,
                Instant.now()
            )
        );
    }
    
    /**
     * Aggiorna lo stato a FAILED con messaggio di errore
     */
    public void updateStateToFailed(String orderCode, String errorMessage) {
        LOG.errorf("V3 - Setting status to FAILED for order %s: %s", orderCode, errorMessage);
        statusMap.computeIfPresent(orderCode, (key, current) -> 
            new EmissionStatusDetailV3(
                current.orderCode(),
                current.policyNumber(),
                EmissionStatusV3.FAILED,
                "Errore: " + errorMessage,
                Instant.now()
            )
        );
    }
    
    /**
     * Recupera lo stato attuale di un ordine
     */
    public EmissionStatusDetailV3 getStatus(String orderCode) {
        EmissionStatusDetailV3 status = statusMap.get(orderCode);
        if (status == null) {
            LOG.warnf("V3 - Status not found for order %s", orderCode);
            return new EmissionStatusDetailV3(
                orderCode,
                null,
                EmissionStatusV3.NOT_FOUND,
                "Order not found",
                Instant.now()
            );
        }
        return status;
    }
    
    /**
     * Verifica se un ordine esiste nel repository
     */
    public boolean exists(String orderCode) {
        return statusMap.containsKey(orderCode);
    }
    
    /**
     * Rimuove un ordine dal repository (per cleanup)
     */
    public void remove(String orderCode) {
        LOG.infof("V3 - Removing status for order %s", orderCode);
        statusMap.remove(orderCode);
    }
    
    /**
     * Ottiene il numero di ordini attualmente tracciati
     */
    public int size() {
        return statusMap.size();
    }
    
    /**
     * Pulisce tutti gli stati (per testing)
     */
    public void clear() {
        LOG.warn("V3 - Clearing all status entries");
        statusMap.clear();
    }
}
