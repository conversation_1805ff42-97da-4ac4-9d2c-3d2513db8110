package it.yolo.v3.repository;

import it.yolo.v3.dto.response.EmissionStatusV3;

import javax.enterprise.context.ApplicationScoped;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@ApplicationScoped
public class StatusRepositoryV3 {
    private final Map<String, EmissionStatusV3> states = new ConcurrentHashMap<>();

    public void init(String orderCode) {
        states.put(orderCode, EmissionStatusV3.PENDING);
    }

    public void update(String orderCode, EmissionStatusV3 status) {
        states.put(orderCode, status);
    }

    public Optional<EmissionStatusV3> get(String orderCode) {
        return Optional.ofNullable(states.get(orderCode));
    }

    public void fail(String orderCode) {
        states.put(orderCode, EmissionStatusV3.FAILED);
    }
}

