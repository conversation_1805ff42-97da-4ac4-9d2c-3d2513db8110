package it.yolo.v3.repository;

import it.yolo.v3.dto.EmissionState;
import it.yolo.v3.dto.EmissionStatus;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Repository in-memory per gestire lo stato dei processi di emissione.
 * In una implementazione reale, questo potrebbe essere sostituito con Redis o un database.
 */
@ApplicationScoped
public class EmissionStatusRepository {
    
    private static final Logger LOG = Logger.getLogger(EmissionStatusRepository.class);
    
    private final ConcurrentHashMap<String, EmissionStatus> statusMap = new ConcurrentHashMap<>();
    
    /**
     * Salva o aggiorna lo stato di un processo di emissione
     */
    public Uni<EmissionStatus> save(EmissionStatus status) {
        return Uni.createFrom().item(() -> {
            LOG.infof("Saving emission status for order %s: %s", status.orderCode(), status.state());
            statusMap.put(status.orderCode(), status);
            return status;
        });
    }
    
    /**
     * Recupera lo stato di un processo di emissione per codice ordine
     */
    public Uni<EmissionStatus> findByOrderCode(String orderCode) {
        return Uni.createFrom().item(() -> {
            EmissionStatus status = statusMap.get(orderCode);
            LOG.debugf("Retrieved emission status for order %s: %s", orderCode, 
                      status != null ? status.state() : "NOT_FOUND");
            return status;
        });
    }
    
    /**
     * Aggiorna lo stato di un processo esistente
     */
    public Uni<EmissionStatus> updateStatus(String orderCode, EmissionState newState) {
        return updateStatus(orderCode, newState, null);
    }
    
    /**
     * Aggiorna lo stato e i dettagli di un processo esistente
     */
    public Uni<EmissionStatus> updateStatus(String orderCode, EmissionState newState, String details) {
        return Uni.createFrom().item(() -> {
            EmissionStatus currentStatus = statusMap.get(orderCode);
            if (currentStatus == null) {
                LOG.warnf("Attempting to update non-existing emission status for order %s", orderCode);
                // Crea un nuovo status se non esiste
                currentStatus = new EmissionStatus(orderCode, null, EmissionState.PENDING, null, Instant.now());
            }
            
            EmissionStatus updatedStatus = details != null 
                ? currentStatus.withStateAndDetails(newState, details)
                : currentStatus.withState(newState);
                
            statusMap.put(orderCode, updatedStatus);
            LOG.infof("Updated emission status for order %s: %s -> %s", 
                     orderCode, currentStatus.state(), newState);
            return updatedStatus;
        });
    }
    
    /**
     * Aggiorna il numero di polizza dopo l'emissione
     */
    public Uni<EmissionStatus> updatePolicyNumber(String orderCode, String policyNumber) {
        return Uni.createFrom().item(() -> {
            EmissionStatus currentStatus = statusMap.get(orderCode);
            if (currentStatus == null) {
                throw new IllegalStateException("Cannot update policy number for non-existing emission status: " + orderCode);
            }
            
            EmissionStatus updatedStatus = currentStatus.withPolicyNumber(policyNumber);
            statusMap.put(orderCode, updatedStatus);
            LOG.infof("Updated policy number for order %s: %s", orderCode, policyNumber);
            return updatedStatus;
        });
    }
    
    /**
     * Rimuove lo stato di un processo (per cleanup)
     */
    public Uni<Boolean> remove(String orderCode) {
        return Uni.createFrom().item(() -> {
            EmissionStatus removed = statusMap.remove(orderCode);
            LOG.infof("Removed emission status for order %s: %s", orderCode, removed != null);
            return removed != null;
        });
    }
    
    /**
     * Restituisce il numero di stati attualmente tracciati (per monitoring)
     */
    public int size() {
        return statusMap.size();
    }
}
