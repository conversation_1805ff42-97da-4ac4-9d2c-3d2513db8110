package it.yolo.v3.domain;

import java.time.LocalDateTime;

/**
 * Modello di dominio semplificato per l'Ordine nella V3.
 * Contiene solo i campi essenziali per l'emissione.
 */
public record Order(
    String id,
    String orderCode,
    String customerId,
    String productId,
    String state,
    String paymentToken,
    String paymentType,
    String paymentTransactionId,
    LocalDateTime createdAt,
    LocalDateTime updatedAt
) {

    public Order {
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
        if (customerId == null || customerId.isBlank()) {
            throw new IllegalArgumentException("Customer ID cannot be null or blank");
        }
        if (productId == null || productId.isBlank()) {
            throw new IllegalArgumentException("Product ID cannot be null or blank");
        }
    }

    /**
     * Verifica se l'ordine è in uno stato valido per l'emissione
     */
    public boolean isValidForEmission() {
        return "Confirmed".equalsIgnoreCase(state) || "PAYMENT_CONFIRMED".equalsIgnoreCase(state);
    }

    /**
     * Verifica se l'ordine ha un pagamento confermato
     */
    public boolean hasConfirmedPayment() {
        return paymentToken != null && !paymentToken.isBlank() &&
               paymentTransactionId != null && !paymentTransactionId.isBlank();
    }
}
