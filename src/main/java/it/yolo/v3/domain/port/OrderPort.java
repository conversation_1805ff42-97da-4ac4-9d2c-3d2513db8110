package it.yolo.v3.domain.port;

import com.fasterxml.jackson.databind.JsonNode;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.domain.model.Order;

/**
 * Port for order operations
 */
public interface OrderPort {
    
    /**
     * Get order by code
     */
    Order getOrder(String token, String orderCode);
    
    /**
     * Get order by code (unchecked)
     */
    Order getOrderUnchecked(String token, String orderCode);
    
    /**
     * Update order
     */
    Order updateOrder(String token, it.yolo.client.order.dto.request.OrderRequestDto request, String orderCode);
    
    /**
     * Update order (unchecked)
     */
    Order updateOrderUnchecked(String token, it.yolo.client.order.dto.request.OrderRequestDto request, String orderCode);
    
    /**
     * Check existing policy
     */
    JsonNode checkExistingPolicy(String orderCode, String token);
    
    /**
     * Align order (YIN client call)
     */
    void alignOrder(String orderCode);
}
