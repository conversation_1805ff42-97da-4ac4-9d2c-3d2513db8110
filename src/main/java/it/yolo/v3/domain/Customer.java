package it.yolo.v3.domain;

/**
 * Modello di dominio semplificato per il Cliente nella V3.
 * Contiene solo i campi essenziali per l'emissione.
 */
public record Customer(
    String id,
    String customerCode,
    String name,
    String surname,
    String taxCode,
    String email,
    String phone,
    String address,
    String city,
    String zipCode,
    String province
) {
    
    public Customer {
        if (id == null || id.isBlank()) {
            throw new IllegalArgumentException("Customer ID cannot be null or blank");
        }
        if (taxCode == null || taxCode.isBlank()) {
            throw new IllegalArgumentException("Tax code cannot be null or blank");
        }
    }
    
    /**
     * Restituisce il nome completo del cliente
     */
    public String getFullName() {
        if (name != null && surname != null) {
            return name + " " + surname;
        } else if (name != null) {
            return name;
        } else if (surname != null) {
            return surname;
        }
        return "N/A";
    }
    
    /**
     * Verifica se il cliente ha tutti i dati necessari per l'emissione
     */
    public boolean isValidForEmission() {
        return taxCode != null && !taxCode.isBlank() &&
               email != null && !email.isBlank();
    }
}
