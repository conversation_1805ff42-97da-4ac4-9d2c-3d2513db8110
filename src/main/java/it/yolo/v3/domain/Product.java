package it.yolo.v3.domain;

import java.math.BigDecimal;

/**
 * Modello di dominio semplificato per il Prodotto nella V3.
 * Contiene solo i campi essenziali per l'emissione.
 */
public record Product(
    String id,
    String code,
    String description,
    String insuranceCompany,
    BigDecimal price,
    String emissionType,  // "INTERNAL" o "EXTERNAL"
    String quotatorType,
    String business
) {
    
    public Product {
        if (id == null || id.isBlank()) {
            throw new IllegalArgumentException("Product ID cannot be null or blank");
        }
        if (code == null || code.isBlank()) {
            throw new IllegalArgumentException("Product code cannot be null or blank");
        }
        if (insuranceCompany == null || insuranceCompany.isBlank()) {
            throw new IllegalArgumentException("Insurance company cannot be null or blank");
        }
    }
    
    /**
     * Verifica se il prodotto usa emissione interna
     */
    public boolean isInternalEmission() {
        return "INTERNAL".equalsIgnoreCase(emissionType);
    }
    
    /**
     * Verifica se il prodotto usa emissione esterna
     */
    public boolean isExternalEmission() {
        return "EXTERNAL".equalsIgnoreCase(emissionType);
    }
    
    /**
     * Verifica se il prodotto è valido per l'emissione
     */
    public boolean isValidForEmission() {
        return code != null && !code.isBlank() &&
               insuranceCompany != null && !insuranceCompany.isBlank() &&
               (isInternalEmission() || isExternalEmission());
    }
}
