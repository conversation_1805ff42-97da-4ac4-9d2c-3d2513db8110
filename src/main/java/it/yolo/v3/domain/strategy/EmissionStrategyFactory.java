package it.yolo.v3.domain.strategy;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Factory per la selezione delle strategie di emission
 */
@ApplicationScoped
public class EmissionStrategyFactory {

    private static final Logger LOGGER = Logger.getLogger(EmissionStrategyFactory.class);

    @Inject
    @LegacyInternalEmission
    EmissionStrategy internalStrategy;

    @Inject
    @LegacyExternalEmission
    EmissionStrategy externalStrategy;

    /**
     * Get emission strategy based on product emission type
     */
    public EmissionStrategy getStrategy(String emissionType) {
        LOGGER.debugf("Selecting emission strategy for type: %s", emissionType);
        
        return switch (emissionType) {
            case "internal" -> internalStrategy;
            case "external" -> externalStrategy;
            case "email" -> internalStrategy; // Email emissions use internal strategy
            default -> {
                LOGGER.warnf("Unknown emission type: %s, defaulting to internal", emissionType);
                yield internalStrategy;
            }
        };
    }
}
