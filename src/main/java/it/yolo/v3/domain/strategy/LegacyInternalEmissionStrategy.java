package it.yolo.v3.domain.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import it.yolo.v3.domain.model.*;
import it.yolo.v3.application.service.EmissionOrchestrator.*;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.client.document.DocumentClient;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.service.ServiceOrder;

import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Internal emission strategy - copia esatta da InternalEmissionImpl
 */
@ApplicationScoped
@LegacyInternalEmission
public class LegacyInternalEmissionStrategy implements EmissionStrategy {

    private static final Logger LOGGER = Logger.getLogger(LegacyInternalEmissionStrategy.class);
    
    private static final String STATE_ID_POLICY = "9";
    private static final int ITEM_INDEX = 0;
    private static final String NONE_CERTIFICATE = "none";

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceOrder serviceOrder;

    @Override
    public String getStrategyType() {
        return "internal";
    }

    @Override
    public EmissionResult executeEmission(EmissionData data) {
        LOGGER.infof("Executing internal emission for order: %s", data.order().orderCode());
        
        String token = "Bearer " + jsonWebToken.getRawToken();
        
        try {
            // Step 1: Create policy request exactly like legacy
            PolicyRequestDto policyRequest = generateRequestPolicy(data);
            
            // Step 2: Call policy service exactly like legacy
            Response response = policyClient.create(token, policyRequest);
            String emissionResponse = response.readEntity(String.class);
            
            // Step 3: Update order with emission details exactly like legacy
            it.yolo.client.order.dto.response.OrderResponseDto orderResponse = 
                serviceOrder.updateEmissionDetail(token, 
                    data.order().orderCode(),
                    data.order().orderItems().get(0).id(), 
                    emissionResponse);
            
            // Step 4: Convert response to policy exactly like legacy
            PolicyResponseDto policyResponseDto = stringToPolicyResponseDto(emissionResponse);
            
            // Step 5: Create emission result
            Policy policy = Policy.fromLegacyPolicy(policyResponseDto);
            Order updatedOrder = Order.fromLegacyOrder(orderResponse);
            
            // Step 6: Generate certificate exactly like legacy
            Certificate certificate = generateCertificate(data, policy);
            
            LOGGER.infof("Internal emission completed for order: %s, policy: %s", 
                data.order().orderCode(), policy.policyCode());
            
            return new EmissionResult(
                updatedOrder,
                data.customer(),
                data.product(),
                policy,
                certificate,
                null, // PG response is null for internal
                data.token()
            );
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Internal emission failed for order: %s", data.order().orderCode());
            throw new RuntimeException("Internal emission failed", e);
        }
    }

    /**
     * Generate policy request - copia esatta da InternalEmissionImpl.generateRequestPolicy()
     */
    private PolicyRequestDto generateRequestPolicy(EmissionData emissionData) {
        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest = new DataPolicyRequestDto();
        
        var order = emissionData.order();
        var orderItem = order.orderItems().get(0);
        var product = emissionData.product();
        var customer = emissionData.customer();
        
        // Map dates exactly like legacy
        dataPolicyRequest.setStartDate(orderItem.startDate());
        dataPolicyRequest.setType(order.productType());
        dataPolicyRequest.setEndDate(orderItem.expirationDate());
        
        // Handle date formatting exactly like legacy
        JsonNode properties = product.properties();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        
        if (properties != null && properties.has("minusDay") && properties.get("minusDay").asBoolean()) {
            LocalDateTime endDate = LocalDateTime.parse(
                dataPolicyRequest.getEndDate().replace("T", " "), dateTimeFormatter);
            dataPolicyRequest.setEndDate(endDate.minusDays(1).toString());
        }
        
        // Handle start date exactly like legacy
        int lastDotIndex = dataPolicyRequest.getStartDate().lastIndexOf(".");
        if (lastDotIndex != -1 && dataPolicyRequest.getStartDate().length() - lastDotIndex - 4 >= 0) {
            dataPolicyRequest.setStartDate(dataPolicyRequest.getStartDate().substring(0, lastDotIndex + 4));
        }
        
        String date = dataPolicyRequest.getStartDate().replace("T", " ");
        LocalDateTime startDate = LocalDate.parse(date.split(" ")[0]).atStartOfDay();
        if (startDate.isBefore(LocalDateTime.now())) {
            startDate = LocalDateTime.now();
        }
        dataPolicyRequest.setStartDate(startDate.toString());
        
        // Generate policy code (will be set by legacy code)
        dataPolicyRequest.setPolicyCode("TEMP_POLICY_CODE"); // Temporary, will be overridden
        dataPolicyRequest.setOrderIdCode(String.valueOf(order.id()));
        
        // Set customer and product IDs
        dataPolicyRequest.getCustomer().setId(customer.id());
        dataPolicyRequest.getProduct().setId(product.id());
        
        // Set payment info exactly like legacy
        if (order.paymentToken() != null) {
            dataPolicyRequest.getPayment().setPaymentToken(order.paymentToken());
        }
        if (order.paymentType() != null) {
            dataPolicyRequest.getPayment().setPaymentType(order.paymentType());
        }
        if (order.paymentTransactionId() != null) {
            dataPolicyRequest.getPayment().setPaymentTransactionId(
                Integer.valueOf(order.paymentTransactionId()));
            dataPolicyRequest.getPayment().setId(
                Integer.valueOf(order.paymentTransactionId()));
        }
        
        // Set additional fields exactly like legacy
        dataPolicyRequest.setInsurancePremium(
            product.price() != null ? java.math.BigDecimal.valueOf(product.price()) : null);
        dataPolicyRequest.setQuantity(orderItem.quantity());
        dataPolicyRequest.setName(product.code().toUpperCase());
        dataPolicyRequest.setOrderItemId(orderItem.id());
        
        policyRequestDto.setDto(dataPolicyRequest);
        return policyRequestDto;
    }

    /**
     * Generate certificate - based on legacy logic
     */
    private Certificate generateCertificate(EmissionData data, Policy policy) {
        String certificateType = data.product().certificate();
        
        if (NONE_CERTIFICATE.equalsIgnoreCase(certificateType)) {
            return getNoneCertificate();
        }
        
        try {
            // Create certificate request exactly like legacy
            CertificateRequestDto certificateRequest = createCertificateRequest(data, policy);
            
            // Call document service exactly like legacy
            Response response = documentClient.generateCertficate(certificateRequest);
            CertificateResponseDto certificateResponse = response.readEntity(CertificateResponseDto.class);
            
            // Convert to domain model
            return new Certificate(
                certificateResponse.getFile(),
                certificateResponse.getLink(),
                certificateResponse.getNomeFile() != null ? 
                    certificateResponse.getNomeFile() : (policy.policyCode() + ".pdf"),
                certificateResponse.getType()
            );
            
        } catch (Exception e) {
            LOGGER.errorf(e, "Certificate generation failed for policy: %s", policy.policyCode());
            throw new RuntimeException("Certificate generation failed", e);
        }
    }

    /**
     * Create certificate request from emission data
     */
    private CertificateRequestDto createCertificateRequest(EmissionData data, Policy policy) {
        CertificateRequestDto request = new CertificateRequestDto();
        
        // Convert V3 models back to legacy DTOs for certificate generation
        // This is needed because the certificate service expects legacy DTOs
        
        // Set product (already JsonNode)
        request.setProduct(convertProductToJsonNode(data.product()));
        
        // Set policy
        PolicyResponseDto policyDto = new PolicyResponseDto();
        it.yolo.client.policy.dto.response.DataPolicyResponse policyData = 
            new it.yolo.client.policy.dto.response.DataPolicyResponse();
        policyData.setId(policy.id());
        policyData.setPolicyCode(policy.policyCode());
        policyData.setStartDate(policy.startDate());
        policyData.setEndDate(policy.endDate());
        policyDto.setData(policyData);
        request.setPolicy(policyDto);
        
        // Set order (convert back to legacy DTO)
        request.setOrder(convertOrderToLegacyDto(data.order()));
        
        // Set customer (convert back to legacy DTO)  
        request.setCustomer(convertCustomerToLegacyDto(data.customer()));
        
        return request;
    }

    /**
     * Convert string response to PolicyResponseDto - exactly like legacy
     */
    private PolicyResponseDto stringToPolicyResponseDto(String string) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.readValue(string, PolicyResponseDto.class);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Get none certificate - exactly like legacy
     */
    private Certificate getNoneCertificate() {
        return new Certificate(
            NONE_CERTIFICATE,
            NONE_CERTIFICATE,
            NONE_CERTIFICATE,
            NONE_CERTIFICATE
        );
    }

    // Helper methods for converting V3 models back to legacy DTOs
    // These are needed because the certificate service still expects legacy DTOs
    
    private JsonNode convertProductToJsonNode(Product product) {
        ObjectMapper mapper = new ObjectMapper();
        var productNode = mapper.createObjectNode();
        var dataNode = mapper.createObjectNode();
        
        dataNode.put("id", product.id());
        dataNode.put("code", product.code());
        dataNode.put("name", product.name());
        dataNode.set("configuration", product.configuration());
        
        productNode.set("data", dataNode);
        return productNode;
    }

    private it.yolo.client.order.dto.response.OrderResponseDto convertOrderToLegacyDto(Order order) {
        // Create minimal legacy DTO for certificate generation
        var legacyOrder = new it.yolo.client.order.dto.response.OrderResponseDto();
        var data = new it.yolo.client.order.dto.response.DataOrderResponseDto();
        
        data.setId(order.id().intValue());
        data.setOrderCode(order.orderCode());
        data.setCustomerId(order.customerId());
        data.setProductId(order.productId());
        
        legacyOrder.setResponse(data);
        return legacyOrder;
    }

    private it.yolo.client.customer.dto.CustomerResponseDto convertCustomerToLegacyDto(Customer customer) {
        // Create minimal legacy DTO for certificate generation
        var legacyCustomer = new it.yolo.client.customer.dto.CustomerResponseDto();
        var data = new it.yolo.client.customer.dto.DataCustomerResponseDto();
        
        data.setId(customer.id().intValue());
        data.setName(customer.firstName());
        data.setSurname(customer.lastName());
        data.setPrimaryMail(customer.email());
        
        legacyCustomer.setData(data);
        return legacyCustomer;
    }
}
