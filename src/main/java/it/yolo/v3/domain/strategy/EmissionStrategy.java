package it.yolo.v3.domain.strategy;

import it.yolo.v3.application.service.EmissionOrchestrator.EmissionData;
import it.yolo.v3.application.service.EmissionOrchestrator.EmissionResult;

/**
 * Strategy interface for emission processing
 */
public interface EmissionStrategy {
    
    /**
     * Get the strategy type identifier
     */
    String getStrategyType();
    
    /**
     * Execute the emission process
     */
    EmissionResult executeEmission(EmissionData data);
}
