package it.yolo.v3.domain.strategy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import it.yolo.v3.domain.model.*;
import it.yolo.v3.application.service.EmissionOrchestrator.*;
import it.yolo.client.pg.PGClient;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.emission.dto.request.EmissionRequest;
import it.yolo.service.ServiceOrder;
import it.yolo.service.client.V2.InvokePolicyV2;

import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

/**
 * External emission strategy - copia esatta da ExternalEmissionImpl
 */
@ApplicationScoped
@LegacyExternalEmission
public class LegacyExternalEmissionStrategy implements EmissionStrategy {

    private static final Logger LOGGER = Logger.getLogger(LegacyExternalEmissionStrategy.class);
    
    private static final String ASYNC_CERTIFICATE = "external_async";
    private static final String INTERNAL_CERTIFICATE = "internal";
    private static final String GEN_ASYNC_CERTIFICATE = "external_generation_async";
    private static final String GEN_SYNC_CERTIFICATE = "external_generation_sync";
    private static final String NONE_CERTIFICATE = "none";
    private static final int ITEM_INDEX = 0;

    @Inject
    @RestClient
    PGClient pgClient;

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    InvokePolicyV2 invokePolicy;

    @ConfigProperty(name = "emission.password")
    String emissionPassword;

    @ConfigProperty(name = "emission.username")
    String emissionUsername;

    @ConfigProperty(name = "tenant.name")
    String tenant;

    @Override
    public String getStrategyType() {
        return "external";
    }

    @Override
    public EmissionResult executeEmission(EmissionData data) {
        LOGGER.infof("Executing external emission for order: %s", data.order().orderCode());
        
        String token = "Bearer " + jsonWebToken.getRawToken();
        
        try {
            // Step 1: Prepare emission request exactly like legacy
            EmissionRequest request = prepareEmissionRequest(data);
            
            // Step 2: Call PG client exactly like legacy
            Response responsePg = pgClient.emission(createPgRequest(request, data));
            JsonNode pgResponseData = responsePg.readEntity(JsonNode.class);
            
            // Step 3: Update order with emission details exactly like legacy
            it.yolo.client.order.dto.response.OrderResponseDto orderResponse = 
                serviceOrder.updateEmissionDetail(token, 
                    data.order().orderCode(),
                    data.order().orderItems().get(0).id(), 
                    pgResponseData);
            
            // Step 4: Create policy request exactly like legacy
            PolicyRequestDto policyRequest = generateRequestPolicy(data, pgResponseData);
            
            // Step 5: Handle policy creation (normal or warranties update) exactly like legacy
            PolicyResponseDto policyResponse = createOrUpdatePolicy(data, policyRequest, token);
            
            // Step 6: Create emission result
            Policy policy = Policy.fromLegacyPolicy(policyResponse);
            Order updatedOrder = Order.fromLegacyOrder(orderResponse);
            
            // Step 7: Handle certificate from PG response
            Certificate certificate = extractCertificateFromPgResponse(pgResponseData, policy);
            
            LOGGER.infof("External emission completed for order: %s, policy: %s", 
                data.order().orderCode(), policy.policyCode());
            
            return new EmissionResult(
                updatedOrder,
                data.customer(),
                data.product(),
                policy,
                certificate,
                pgResponseData, // PG response for external emissions
                data.token()
            );
            
        } catch (Exception e) {
            LOGGER.errorf(e, "External emission failed for order: %s", data.order().orderCode());
            throw new RuntimeException("External emission failed", e);
        }
    }

    /**
     * Prepare emission request - based on legacy logic
     */
    private EmissionRequest prepareEmissionRequest(EmissionData data) {
        // Create legacy emission request DTO from V3 data
        var legacyEmissionDto = convertToLegacyEmissionRequestDto(data);
        legacyEmissionDto.setEmissionPassword(emissionPassword);
        legacyEmissionDto.setEmissionUsername(emissionUsername);
        
        EmissionRequest request = new EmissionRequest();
        request.setData(legacyEmissionDto);
        request.setTenant(tenant);
        
        return request;
    }

    /**
     * Create PG request JSON - exactly like legacy
     */
    private JsonNode createPgRequest(EmissionRequest request, EmissionData data) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        
        // Handle warranties update logic exactly like legacy
        boolean warrantiesUpdated = data.order().parentOrder() != null;
        request.getData().getPolicy().setPolicySubstitution(warrantiesUpdated);
        
        if (warrantiesUpdated) {
            try {
                PolicyResponseDto oldPolicy = policyClient.getOrderId(
                    "Bearer " + jsonWebToken.getRawToken(),
                    getParentOrderId(data.order()).intValue()).readEntity(PolicyResponseDto.class);
                request.getData().getPolicy().setOriginalPolicyNumber(oldPolicy.getData().getPolicyCode());
            } catch (Exception e) {
                LOGGER.warn("Failed to get old policy for warranties update", e);
            }
        }
        
        // Convert to JSON and clean up exactly like legacy
        JsonNode reqForPg = mapper.valueToTree(request);
        
        // Set dates exactly like legacy
        String startDate = ensureDateFormat(data.order().orderItems().get(0).startDate());
        String expirationDate = ensureDateFormat(data.order().orderItems().get(0).expirationDate());
        
        ObjectNode orderData = (ObjectNode) reqForPg.get("data").get("order").get("data");
        orderData.put("start_date", startDate);
        orderData.put("expiration_date", expirationDate);
        orderData.put("utmSource", data.order().utmSource());
        orderData.put("agenziaDiRiferimento", data.order().agenziaDiRiferimento());
        
        // Clean up exactly like legacy
        orderData.remove("packets");
        orderData.remove("product");
        
        ObjectNode productData = (ObjectNode) reqForPg.get("data").get("product").get("data");
        productData.remove("images");
        
        // Clean up packets exactly like legacy
        for (JsonNode packet : productData.get("packets")) {
            for (JsonNode warranty : packet.withArray("warranties")) {
                ((ObjectNode) warranty.get("anagWarranty")).remove("images");
            }
        }
        
        // Clean up order items exactly like legacy
        ObjectNode orderItemData = (ObjectNode) reqForPg.get("data").get("order").get("data")
            .get("orderItem").get(0);
        
        if (orderItemData.get("instance").has("ceilings")) {
            for (JsonNode ceiling : orderItemData.get("instance").withArray("ceilings")) {
                ((ObjectNode) ceiling.get("anagWarranty")).remove("images");
            }
        }
        
        orderItemData.remove("start_date");
        orderItemData.remove("expiration_date");
        orderItemData.put("start_date", startDate);
        orderItemData.put("expiration_date", expirationDate);
        
        return reqForPg;
    }

    /**
     * Generate policy request - exactly like legacy generateRequestPolicy()
     */
    private PolicyRequestDto generateRequestPolicy(EmissionData data, JsonNode pgResponse) {
        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest = new DataPolicyRequestDto();
        
        var order = data.order();
        var orderItem = order.orderItems().get(0);
        var customer = data.customer();
        var product = data.product();
        
        // Set dates exactly like legacy
        dataPolicyRequest.setStartDate(orderItem.startDate());
        dataPolicyRequest.setType(order.productType());
        dataPolicyRequest.setEndDate(orderItem.expirationDate());
        
        // Set policy code from PG response exactly like legacy
        dataPolicyRequest.setPolicyCode(pgResponse.get("policyNumber").asText());
        
        // Set IDs exactly like legacy
        dataPolicyRequest.setOrderIdCode(String.valueOf(order.id()));
        dataPolicyRequest.getCustomer().setId(customer.id());
        dataPolicyRequest.getProduct().setId(product.id());
        
        // Set payment info exactly like legacy
        if (order.paymentToken() != null) {
            dataPolicyRequest.getPayment().setPaymentToken(order.paymentToken());
        }
        if (order.paymentType() != null) {
            dataPolicyRequest.getPayment().setPaymentType(order.paymentType());
        }
        if (order.paymentTransactionId() != null) {
            dataPolicyRequest.getPayment().setPaymentTransactionId(
                Integer.valueOf(order.paymentTransactionId()));
            dataPolicyRequest.getPayment().setId(
                Integer.valueOf(order.paymentTransactionId()));
        }
        
        // Set additional fields exactly like legacy
        dataPolicyRequest.setName(product.code().toUpperCase());
        dataPolicyRequest.setInsurancePremium(
            product.price() != null ? java.math.BigDecimal.valueOf(product.price()) : null);
        dataPolicyRequest.setQuantity(orderItem.quantity());
        dataPolicyRequest.setOrderItemId(orderItem.id());
        
        policyRequestDto.setDto(dataPolicyRequest);
        return policyRequestDto;
    }

    /**
     * Create or update policy - exactly like legacy logic
     */
    private PolicyResponseDto createOrUpdatePolicy(EmissionData data, PolicyRequestDto policyRequest, String token) {
        boolean warrantiesUpdated = data.order().parentOrder() != null;
        
        if (warrantiesUpdated) {
            // Handle warranties update exactly like legacy
            Long parentOrderId = getParentOrderId(data.order());
            Long lastParentOrderId = getLastParentOrderId(data.order());
            
            if (lastParentOrderId == null) {
                return invokePolicy.duplicatePolicy(token, 
                    data.order().id().intValue(), 
                    parentOrderId.intValue(),
                    data.order().orderItems().get(0).id(), 
                    policyRequest.getDto().getPolicyCode());
            } else {
                return invokePolicy.duplicatePolicy(token, 
                    data.order().id().intValue(), 
                    lastParentOrderId.intValue(),
                    data.order().orderItems().get(0).id(), 
                    policyRequest.getDto().getPolicyCode());
            }
        } else {
            // Normal policy creation exactly like legacy
            return policyClient.create(token, policyRequest).readEntity(PolicyResponseDto.class);
        }
    }

    /**
     * Extract certificate from PG response - based on legacy logic
     */
    private Certificate extractCertificateFromPgResponse(JsonNode pgResponse, Policy policy) {
        if (pgResponse.get("certificate") != null && 
            !pgResponse.get("certificate").asText().equals("null")) {
            
            String certificateContent = pgResponse.get("certificate").asText();
            return new Certificate(
                certificateContent,
                null, // Link not provided in PG response
                policy.policyCode() + ".pdf",
                "pdf"
            );
        }
        
        // Return none certificate if not available
        return new Certificate(
            NONE_CERTIFICATE,
            NONE_CERTIFICATE,
            NONE_CERTIFICATE,
            NONE_CERTIFICATE
        );
    }

    // Helper methods exactly like legacy
    
    private String ensureDateFormat(String date) {
        return date.contains(".") ? date : date + ".000";
    }
    
    private Long getParentOrderId(Order order) {
        if (order.parentOrder() == null) return null;
        
        String parentCode = order.parentOrder();
        if (parentCode.contains(";")) {
            String[] codes = parentCode.split(";");
            return Long.valueOf(codes[0]); // First parent
        }
        return Long.valueOf(parentCode);
    }
    
    private Long getLastParentOrderId(Order order) {
        if (order.parentOrder() == null) return null;
        
        String parentCode = order.parentOrder();
        if (parentCode.contains(";")) {
            String[] codes = parentCode.split(";");
            String lastParent = codes[codes.length - 1];
            String firstParent = codes[0];
            
            if (!lastParent.equalsIgnoreCase(firstParent)) {
                return Long.valueOf(lastParent);
            }
        }
        return null;
    }
    
    /**
     * Convert V3 emission data to legacy DTO for PG call
     */
    private it.yolo.emission.dto.request.EmissionRequestDto convertToLegacyEmissionRequestDto(EmissionData data) {
        // This would create a legacy EmissionRequestDto from V3 data
        // For now, create a minimal implementation
        // In production, this would need full mapping
        var legacyDto = new it.yolo.emission.dto.request.EmissionRequestDto();
        
        // Convert V3 models back to legacy DTOs
        legacyDto.setCustomer(convertCustomerToLegacyDto(data.customer()));
        legacyDto.setOrder(convertOrderToLegacyDto(data.order()));
        legacyDto.setProduct(convertProductToJsonNode(data.product()));
        
        // Set policy code generation info
        var policyData = new it.yolo.client.policy.dto.response.DataPolicyResponse();
        policyData.setPolicyCode("TEMP_EXTERNAL_POLICY"); // Will be overridden by PG
        legacyDto.setPolicy(policyData);
        
        return legacyDto;
    }
    
    // Helper conversion methods (same as internal strategy)
    private it.yolo.client.customer.dto.CustomerResponseDto convertCustomerToLegacyDto(Customer customer) {
        var legacyCustomer = new it.yolo.client.customer.dto.CustomerResponseDto();
        var data = new it.yolo.client.customer.dto.DataCustomerResponseDto();
        
        data.setId(customer.id().intValue());
        data.setName(customer.firstName());
        data.setSurname(customer.lastName());
        data.setPrimaryMail(customer.email());
        
        legacyCustomer.setData(data);
        return legacyCustomer;
    }

    private it.yolo.client.order.dto.response.OrderResponseDto convertOrderToLegacyDto(Order order) {
        var legacyOrder = new it.yolo.client.order.dto.response.OrderResponseDto();
        var data = new it.yolo.client.order.dto.response.DataOrderResponseDto();
        
        data.setId(order.id().intValue());
        data.setOrderCode(order.orderCode());
        data.setCustomerId(order.customerId());
        data.setProductId(order.productId());
        data.setProductType(order.productType());
        data.setPaymentToken(order.paymentToken());
        data.setPaymentType(order.paymentType());
        data.setPaymentTransactionId(order.paymentTransactionId());
        
        legacyOrder.setResponse(data);
        return legacyOrder;
    }

    private JsonNode convertProductToJsonNode(Product product) {
        ObjectMapper mapper = new ObjectMapper();
        var productNode = mapper.createObjectNode();
        var dataNode = mapper.createObjectNode();
        
        dataNode.put("id", product.id());
        dataNode.put("code", product.code());
        dataNode.put("name", product.name());
        dataNode.set("configuration", product.configuration());
        
        productNode.set("data", dataNode);
        return productNode;
    }
}
