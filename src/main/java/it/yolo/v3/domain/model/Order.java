package it.yolo.v3.domain.model;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;

public record Order(
    String orderCode,
    Long id,
    Integer customerId,
    String productId,
    String productType,
    String language,
    String anagState,
    List<OrderItem> orderItems,
    String paymentToken,
    String paymentType,
    String paymentTransactionId,
    String parentOrder,
    Boolean intermediaryOrder,
    String utmSource,
    String agenziaDiRiferimento,
    JsonNode resPg
) {
    
    public Order {
        if (orderCode == null || orderCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Order code cannot be null or empty");
        }
        if (id == null) {
            throw new IllegalArgumentException("Order ID cannot be null");
        }
        if (customerId == null) {
            throw new IllegalArgumentException("Customer ID cannot be null");
        }
    }
    
    public static Order fromLegacyOrder(it.yolo.client.order.dto.response.OrderResponseDto legacyOrder) {
        List<OrderItem> items = legacyOrder.getResponse().getOrderItem().stream()
            .map(item -> new OrderItem(
                item.getId(),
                item.getStartDate(),
                item.getExpirationDate(),
                item.getQuantity(),
                item.getState(),
                item.getInstance(),
                item.getEmission()
            ))
            .toList();
            
        return new Order(
            legacyOrder.getResponse().getOrderCode(),
            legacyOrder.getResponse().getId().longValue(),
            legacyOrder.getResponse().getCustomerId(),
            legacyOrder.getResponse().getProductId(),
            legacyOrder.getResponse().getProductType(),
            legacyOrder.getResponse().getLanguage(),
            legacyOrder.getResponse().getAnagStates() != null ? 
                "PAYMENT_STATE" : null, // Default state mapping
            items,
            legacyOrder.getResponse().getPaymentToken(),
            legacyOrder.getResponse().getPaymentType(),
            legacyOrder.getResponse().getPaymentTransactionId(),
            legacyOrder.getResponse().getParentOrder(),
            legacyOrder.getResponse().getIntermediaryOrder(),
            legacyOrder.getResponse().getUtmSource(),
            legacyOrder.getResponse().getAgenziaDiRiferimento(),
            null // resPg non disponibile direttamente nel DTO
        );
    }
    
    public record OrderItem(
        Integer id,
        String startDate,
        String expirationDate,
        Integer quantity,
        String state,
        JsonNode instance,
        JsonNode emission
    ) {}
}
