package it.yolo.v3.domain.model;

import com.fasterxml.jackson.databind.JsonNode;

public record Policy(
    Long id,
    String policyCode,
    String startDate,
    String endDate,
    String status,
    Double premium,
    String paymentFrequency,
    Long customerId,
    Long productId,
    Long orderId,
    String certificateLink,
    JsonNode details
) {
    
    public Policy {
        if (policyCode == null || policyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Policy code cannot be null or empty");
        }
    }
    
    public static Policy fromLegacyPolicy(it.yolo.client.policy.dto.response.PolicyResponseDto legacyPolicy) {
        var data = legacyPolicy.getData();
        return new Policy(
            data.getId(),
            data.getPolicyCode(),
            data.getStartDate(),
            data.getEndDate(),
            null, // status non disponibile
            null, // premium non disponibile
            null, // paymentFrequency non disponibile
            data.getCustomer() != null ? data.getCustomer().getId() : null,
            data.getProduct() != null ? data.getProduct().getId() : null,
            data.getOrderId(),
            data.getCertificateLink(),
            null // Additional details can be stored here
        );
    }
    
    public static Policy fromLegacyPolicyData(it.yolo.client.policy.dto.response.DataPolicyResponse legacyPolicyData) {
        return new Policy(
            legacyPolicyData.getId(),
            legacyPolicyData.getPolicyCode(),
            legacyPolicyData.getStartDate(),
            legacyPolicyData.getEndDate(),
            null, // status non disponibile
            null, // premium non disponibile
            null, // paymentFrequency non disponibile
            legacyPolicyData.getCustomer() != null ? legacyPolicyData.getCustomer().getId() : null,
            legacyPolicyData.getProduct() != null ? legacyPolicyData.getProduct().getId() : null,
            legacyPolicyData.getOrderId(),
            legacyPolicyData.getCertificateLink(),
            null
        );
    }
}
