package it.yolo.v3.domain.model;

public record Certificate(
    String fileContent,
    String link,
    String filename,
    String type
) {
    
    public Certificate {
        if (filename == null || filename.trim().isEmpty()) {
            throw new IllegalArgumentException("Certificate filename cannot be null or empty");
        }
    }
    
    public static Certificate fromLegacyCertificate(it.yolo.emission.dto.response.CertificateResponseDto legacy) {
        return new Certificate(
            legacy.getFile(),
            legacy.getLink(),
            legacy.getNomeFile(),
            legacy.getType()
        );
    }
    
    public boolean isNone() {
        return "none".equals(fileContent) && "none".equals(link);
    }
}
