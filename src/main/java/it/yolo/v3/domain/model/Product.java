package it.yolo.v3.domain.model;

import com.fasterxml.jackson.databind.JsonNode;

public record Product(
    Long id,
    String code,
    String name,
    String version,
    String description,
    String insuranceCompany,
    String category,
    Double price,
    JsonNode configuration,
    String emission,
    String certificate,
    JsonNode properties
) {
    
    public Product {
        if (id == null) {
            throw new IllegalArgumentException("Product ID cannot be null");
        }
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Product code cannot be null or empty");
        }
    }
    
    public static Product fromLegacyProduct(JsonNode productJson) {
        var data = productJson.get("data");
        var config = data.get("configuration");
        
        return new Product(
            data.get("id").asLong(),
            data.get("code").asText(),
            data.get("name").asText(),
            data.has("version") ? data.get("version").asText() : null,
            data.has("description") ? data.get("description").asText() : null,
            data.has("insuranceCompany") ? data.get("insuranceCompany").asText() : null,
            data.has("category") ? data.get("category").asText() : null,
            data.has("price") ? data.get("price").asDouble() : null,
            config,
            config.has("emission") ? config.get("emission").asText() : "internal",
            config.has("certificate") ? config.get("certificate").asText() : "internal",
            config.has("properties") ? config.get("properties") : null
        );
    }
    
    public boolean isInternalEmission() {
        return "internal".equals(emission);
    }
    
    public boolean isExternalEmission() {
        return "external".equals(emission);
    }
    
    public boolean isEmailOnlyEmission() {
        return "email".equals(emission);
    }
    
    public boolean hasEmissionSteps() {
        return properties != null && properties.has("emissionSteps");
    }
    
    public JsonNode getEmissionSteps() {
        return hasEmissionSteps() ? properties.get("emissionSteps") : null;
    }
}
