package it.yolo.v3.domain.model;

public record Customer(
    Long id,
    String fiscalCode,
    String firstName,
    String lastName,
    String email,
    String phone,
    String birthDate,
    String birthPlace,
    String gender,
    String address,
    String city,
    String postalCode,
    String province,
    String ndgCode
) {
    
    public Customer {
        if (id == null) {
            throw new IllegalArgumentException("Customer ID cannot be null");
        }
        if (fiscalCode == null || fiscalCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Fiscal code cannot be null or empty");
        }
    }
    
    public static Customer fromLegacyCustomer(it.yolo.client.customer.dto.CustomerResponseDto legacyCustomer) {
        var data = legacyCustomer.getData();
        return new Customer(
            data.getId() != null ? data.getId().longValue() : null,
            data.getTaxCode(),
            data.getName(),
            data.getSurname(),
            data.getPrimaryMail(),
            data.getPrimaryPhone(),
            data.getDateOfBirth(),
            data.getBirthCity(),
            data.getGender(),
            data.getStreet() + " " + (data.getStreetNumber() != null ? data.getStreetNumber() : ""),
            data.getCity(),
            data.getZipCode(),
            data.getProvince(),
            data.getNdg()
        );
    }
}
