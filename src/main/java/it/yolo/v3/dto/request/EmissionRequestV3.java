package it.yolo.v3.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Richiesta emissione V3 (semplificata). Usa record per compatibilità Java 17.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public record EmissionRequestV3(
        @JsonProperty("paymentType") String paymentType,
        @JsonProperty("paymentToken") String paymentToken,
        @JsonProperty("discount") String discount,
        @JsonProperty("subscriptionId") String subscriptionId,
        @JsonProperty("paymentTransactionId") Integer paymentTransactionId
) {
    
    public EmissionRequestV3 {
        // Validazione costruttore compatto
        if (paymentToken == null || paymentToken.trim().isEmpty()) {
            throw new IllegalArgumentException("Payment token cannot be null or empty");
        }
    }
}
