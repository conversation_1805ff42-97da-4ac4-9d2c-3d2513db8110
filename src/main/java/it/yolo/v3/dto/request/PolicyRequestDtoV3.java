package it.yolo.v3.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO V3 per la richiesta delle polizze - versione record
 */
public record PolicyRequestDtoV3(
        @JsonProperty("data") DataPolicyRequestDtoV3 data
) {}

/**
 * Dati della richiesta polizza V3
 */
public record DataPolicyRequestDtoV3(
        @JsonProperty("id") Long id,
        @JsonProperty("paymentMethod") String paymentMethod,
        @JsonProperty("orderCode") String orderCode,
        @JsonProperty("orderId") Long orderId,
        @JsonProperty("policyCode") String policyCode,
        @JsonProperty("orderIdCode") String orderIdCode,
        @JsonProperty("subscriptionId") String subscriptionId,
        @JsonProperty("product") ProductRequestPolicyV3 product,
        @JsonProperty("packet") PacketRequestPolicyV3 packet,
        @JsonProperty("customer") CustomerRequestPolicyV3 customer,
        @JsonProperty("choosenProperties") JsonNode choosenProperties,
        @JsonProperty("insurancePremium") BigDecimal insurancePremium,
        @JsonProperty("startDate") LocalDateTime startDate,
        @JsonProperty("endDate") LocalDateTime endDate,
        @JsonProperty("quantity") Long quantity,
        @JsonProperty("insuredIsContractor") Boolean insuredIsContractor,
        @JsonProperty("type") String type,
        @JsonProperty("markedAsRenewable") Boolean markedAsRenewable,
        @JsonProperty("policySubstitution") Boolean policySubstitution,
        @JsonProperty("masterPolicyNumber") String masterPolicyNumber,
        @JsonProperty("nextBillingDate") String nextBillingDate,
        @JsonProperty("name") String name,
        @JsonProperty("username") String username,
        @JsonProperty("password") String password,
        @JsonProperty("isWithdrawable") Boolean isWithdrawable,
        @JsonProperty("isDeactivable") Boolean isDeactivable
) {}

/**
 * Prodotto per richiesta polizza V3
 */
public record ProductRequestPolicyV3(
        @JsonProperty("id") Long id,
        @JsonProperty("code") String code,
        @JsonProperty("name") String name
) {}

/**
 * Pacchetto per richiesta polizza V3
 */
public record PacketRequestPolicyV3(
        @JsonProperty("id") Long id,
        @JsonProperty("name") String name
) {}

/**
 * Cliente per richiesta polizza V3
 */
public record CustomerRequestPolicyV3(
        @JsonProperty("id") Long id,
        @JsonProperty("customerCode") String customerCode,
        @JsonProperty("name") String name,
        @JsonProperty("surname") String surname,
        @JsonProperty("taxCode") String taxCode,
        @JsonProperty("email") String email,
        @JsonProperty("phone") String phone
) {}
