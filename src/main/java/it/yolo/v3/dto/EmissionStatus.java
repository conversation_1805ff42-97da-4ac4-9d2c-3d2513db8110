package it.yolo.v3.dto;

import java.time.Instant;

/**
 * Stato dettagliato del processo di emissione.
 * Utilizzato per tracciare il progresso delle operazioni asincrone.
 * 
 * @param orderCode Codice dell'ordine
 * @param policyNumber Numero della polizza (può essere null se l'emissione non è ancora avvenuta)
 * @param state Stato attuale del processo
 * @param details Dettagli aggiuntivi (es. messaggio di errore in caso di fallimento)
 * @param updatedAt Timestamp dell'ultimo aggiornamento
 */
public record EmissionStatus(
    String orderCode,
    String policyNumber,
    EmissionState state,
    String details,
    Instant updatedAt
) {
    
    public EmissionStatus {
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
        if (state == null) {
            throw new IllegalArgumentException("State cannot be null");
        }
        if (updatedAt == null) {
            throw new IllegalArgumentException("Updated at cannot be null");
        }
    }
    
    /**
     * Crea un nuovo status con stato aggiornato mantenendo gli altri campi
     */
    public EmissionStatus withState(EmissionState newState) {
        return new EmissionStatus(orderCode, policyNumber, newState, details, Instant.now());
    }
    
    /**
     * Crea un nuovo status con stato e dettagli aggiornati
     */
    public EmissionStatus withStateAndDetails(EmissionState newState, String newDetails) {
        return new EmissionStatus(orderCode, policyNumber, newState, newDetails, Instant.now());
    }
    
    /**
     * Crea un nuovo status con policy number aggiornato (dopo l'emissione)
     */
    public EmissionStatus withPolicyNumber(String newPolicyNumber) {
        return new EmissionStatus(orderCode, newPolicyNumber, state, details, Instant.now());
    }
}
