package it.yolo.v3.dto;

/**
 * Risposta immediata dell'API di emissione V3.
 * Contiene solo le informazioni essenziali che il client riceve subito dopo l'emissione.
 * 
 * @param orderCode Codice dell'ordine
 * @param policyNumber Numero della polizza generata
 */
public record EmissionResponse(
    String orderCode,
    String policyNumber
) {
    
    public EmissionResponse {
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
        if (policyNumber == null || policyNumber.isBlank()) {
            throw new IllegalArgumentException("Policy number cannot be null or blank");
        }
    }
}
