package it.yolo.v3.dto;

/**
 * Stati del processo di emissione asincrono.
 * Rappresenta le fasi del workflow post-emissione.
 */
public enum EmissionState {
    
    /**
     * Processo appena avviato, in attesa di elaborazione
     */
    PENDING,
    
    /**
     * Polizza emessa con successo, task in background avviati
     */
    EMITTED,
    
    /**
     * Generazione del certificato in corso
     */
    CERTIFICATE_IN_PROGRESS,
    
    /**
     * Invio email e upload documenti in corso
     */
    COMMUNICATION_IN_PROGRESS,
    
    /**
     * Processo completato con successo
     */
    COMPLETE,
    
    /**
     * Processo fallito durante l'elaborazione in background
     */
    FAILED
}
