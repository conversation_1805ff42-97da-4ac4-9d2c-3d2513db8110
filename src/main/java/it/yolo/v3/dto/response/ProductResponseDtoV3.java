package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;

/**
 * DTO V3 per la risposta dei prodotti - versione record
 */
public record ProductResponseDtoV3(
        @JsonProperty("data") DataProductResponseV3 data
) {}

/**
 * Dati del prodotto V3
 */
public record DataProductResponseV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("code") String code,
        @JsonProperty("description") String description,
        @JsonProperty("startDate") String startDate,
        @JsonProperty("recurring") Boolean recurring,
        @JsonProperty("splits") Object splits,
        @JsonProperty("questions") Object questions,
        @JsonProperty("categories") Object categories,
        @JsonProperty("insuranceCompany") InsuranceCompanyV3 insuranceCompany,
        @JsonProperty("price") Double price,
        @JsonProperty("short_description") String shortDescription,
        @JsonProperty("images") Object images,
        @JsonProperty("holder_maximum_age") Integer holderMaximumAge,
        @JsonProperty("business") Boolean business,
        @JsonProperty("display_price") Boolean displayPrice,
        @JsonProperty("show_in_dashboard") Boolean showInDashboard,
        @JsonProperty("maximum_insurable") Double maximumInsurable,
        @JsonProperty("only_contractor") Boolean onlyContractor,
        @JsonProperty("variants") Object variants,
        @JsonProperty("conditions_package") String conditionsPackage,
        @JsonProperty("packets") List<PacketV3> packets,
        @JsonProperty("insuranceCompanyLogo") String insuranceCompanyLogo,
        @JsonProperty("catalogId") Integer catalogId,
        @JsonProperty("holder_minimum_age") Integer holderMinimumAge,
        @JsonProperty("information_package") String informationPackage,
        @JsonProperty("can_open_claim") Boolean canOpenClaim,
        @JsonProperty("conditions") String conditions,
        @JsonProperty("productDescription") String productDescription,
        @JsonProperty("titleProd") String titleProd,
        @JsonProperty("properties") JsonNode properties,
        @JsonProperty("quotatorType") String quotatorType,
        @JsonProperty("configuration") ProductConfigurationV3 configuration
) {}

/**
 * Compagnia assicurativa V3
 */
public record InsuranceCompanyV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("name") String name,
        @JsonProperty("code") String code,
        @JsonProperty("logo") String logo
) {}

/**
 * Configurazione del prodotto V3
 */
public record ProductConfigurationV3(
        @JsonProperty("emission") String emission,
        @JsonProperty("emissionType") String emissionType,
        @JsonProperty("policyPrefix") String policyPrefix,
        @JsonProperty("requiresCertificate") Boolean requiresCertificate
) {}
