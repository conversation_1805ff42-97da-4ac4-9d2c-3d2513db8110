package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;

/**
 * DTO V3 per la risposta dei clienti - versione record
 */
public record CustomerResponseDtoV3(
        @JsonProperty("data") DataCustomerResponseDtoV3 data,
        @JsonProperty("additionalProperties") Map<String, Object> additionalProperties
) {}

/**
 * Dati del cliente V3
 */
public record DataCustomerResponseDtoV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("customer_code") String customerCode,
        @JsonProperty("external_code") JsonNode externalCode,
        @JsonProperty("username") Object username,
        @JsonProperty("name") String name,
        @JsonProperty("surname") String surname,
        @JsonProperty("date_of_birth") String dateOfBirth,
        @JsonProperty("birth_city") String birthCity,
        @JsonProperty("birth_country") String birthCountry,
        @JsonProperty("birth_state") String birthState,
        @JsonProperty("education") String education,
        @JsonProperty("country_id") Integer countryId,
        @JsonProperty("state_id") Integer stateId,
        @JsonProperty("ndg") String ndg,
        @JsonProperty("state") String state,
        @JsonProperty("city_id") Integer cityId,
        @JsonProperty("birth_state_id") Integer birthStateId,
        @JsonProperty("birth_city_id") Integer birthCityId,
        @JsonProperty("birth_country_id") Integer birthCountryId,
        @JsonProperty("birth_state_abbr") String birthStateAbbr,
        @JsonProperty("birth_province") String birthProvince,
        @JsonProperty("tax_code") String taxCode,
        @JsonProperty("gender") String gender,
        @JsonProperty("street") String street,
        @JsonProperty("street_number") String streetNumber,
        @JsonProperty("city") String city,
        @JsonProperty("type") String type,
        @JsonProperty("country") String country,
        @JsonProperty("zip_code") String zipCode,
        @JsonProperty("province") String province,
        @JsonProperty("primary_mail") String primaryMail,
        @JsonProperty("secondary_mail") String secondaryMail,
        @JsonProperty("primary_phone") String primaryPhone,
        @JsonProperty("secondary_phone") Object secondaryPhone,
        @JsonProperty("language") String language,
        @JsonProperty("legal_form") String legalForm,
        @JsonProperty("createdAt") String createdAt,
        @JsonProperty("updatedAt") String updatedAt,
        @JsonProperty("state_abbr") String stateAbbr,
        @JsonProperty("password") String password,
        @JsonProperty("company") String company
) {}
