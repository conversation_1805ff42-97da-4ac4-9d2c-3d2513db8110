package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Risposta immediata dell'API di emissione V3.
 * Contiene solo le informazioni essenziali che il client riceve subito dopo l'emissione.
 */
public record EmissionResponseV3(
        @JsonProperty("orderCode") String orderCode,
        @JsonProperty("policyNumber") String policyNumber
) {
    
    public EmissionResponseV3 {
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
        if (policyNumber == null || policyNumber.isBlank()) {
            throw new IllegalArgumentException("Policy number cannot be null or blank");
        }
    }
    
    public static EmissionResponseV3 pending(String orderCode) {
        return new EmissionResponseV3(orderCode, null);
    }
}
