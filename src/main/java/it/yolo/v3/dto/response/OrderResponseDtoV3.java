package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO V3 per la risposta degli ordini - versione record
 */
public record OrderResponseDtoV3(
        @JsonProperty("data") DataOrderResponseDtoV3 data,
        @JsonProperty("additionalInfo") Object additionalInfo,
        @JsonProperty("discount") String discount,
        @JsonProperty("version") String version,
        @JsonProperty("fieldToRecover") JsonNode fieldToRecover,
        @JsonProperty("additionalProperties") Map<String, Object> additionalProperties
) {}

/**
 * Dati dell'ordine V3
 */
public record DataOrderResponseDtoV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("orderCode") String orderCode,
        @JsonProperty("productId") String productId,
        @JsonProperty("policyCode") Object policyCode,
        @JsonProperty("anagStates") AnagStatesV3 anagStates,
        @JsonProperty("packetId") Integer packetId,
        @JsonProperty("product") ProductOrderResponseV3 product,
        @JsonProperty("brokerId") Integer brokerId,
        @JsonProperty("companyId") Integer companyId,
        @JsonProperty("customerId") Integer customerId,
        @JsonProperty("choosenProperties") Object choosenProperties,
        @JsonProperty("insurancePremium") Double insurancePremium,
        @JsonProperty("createdBy") String createdBy,
        @JsonProperty("updatedBy") String updatedBy,
        @JsonProperty("fieldToRecover") JsonNode fieldToRecover,
        @JsonProperty("start_date") LocalDateTime startDate,
        @JsonProperty("expiration_date") LocalDateTime expirationDate,
        @JsonProperty("survey") JsonNode survey,
        @JsonProperty("createdAt") String createdAt,
        @JsonProperty("updatedAt") String updatedAt,
        @JsonProperty("packet") PacketV3 packet,
        @JsonProperty("orderHistory") OrderHistoryV3 orderHistory,
        @JsonProperty("stepState") List<String> stepState,
        @JsonProperty("orderItem") List<OrderItemV3> orderItem,
        @JsonProperty("customer") DataCustomerResponseDtoV3 customer,
        @JsonProperty("type") String type,
        @JsonProperty("plan_id") String planId
) {}

/**
 * Stati anagrafici V3
 */
public record AnagStatesV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description
) {}

/**
 * Prodotto dell'ordine V3
 */
public record ProductOrderResponseV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("code") String code,
        @JsonProperty("description") String description,
        @JsonProperty("startDate") String startDate,
        @JsonProperty("recurring") Boolean recurring,
        @JsonProperty("splits") Object splits,
        @JsonProperty("questions") Object questions,
        @JsonProperty("categories") Object categories,
        @JsonProperty("asset") Object asset,
        @JsonProperty("insuranceCompany") Object insuranceCompany
) {}

/**
 * Pacchetto V3
 */
public record PacketV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description
) {}

/**
 * Storico ordine V3
 */
public record OrderHistoryV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("action") String action,
        @JsonProperty("timestamp") LocalDateTime timestamp
) {}

/**
 * Item dell'ordine V3
 */
public record OrderItemV3(
        @JsonProperty("id") Integer id,
        @JsonProperty("quantity") Integer quantity,
        @JsonProperty("price") Double price
) {}
