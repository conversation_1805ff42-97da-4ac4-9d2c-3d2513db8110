package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum EmissionStatusV3 {
    PENDING,
    CERTIFICATE_IN_PROGRESS,
    POST_PROCESSING,
    COMPLETE,
    FAILED;

    @Json<PERSON>reator
    public static EmissionStatusV3 from(String val){
        return EmissionStatusV3.valueOf(val);
    }

    @JsonValue
    public String toValue(){
        return name();
    }
}

