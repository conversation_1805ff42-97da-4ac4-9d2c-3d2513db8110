package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.Instant;

/**
 * Stati del processo di emissione asincrono.
 * Rappresenta le fasi del workflow post-emissione.
 */
public enum EmissionStatusV3 {
    
    /**
     * Processo appena avviato, in attesa di elaborazione
     */
    PENDING("Processo appena avviato"),
    
    /**
     * Polizza emessa con successo, task in background avviati
     */
    EMITTED("Polizza emessa, task background avviati"),
    
    /**
     * Generazione del certificato in corso
     */
    CERTIFICATE_IN_PROGRESS("Generazione certificato in corso"),
    
    /**
     * Invio email e upload documenti in corso
     */
    POST_PROCESSING("Invio email e upload documenti"),
    
    /**
     * Processo completato con successo
     */
    COMPLETE("Processo completato con successo"),
    
    /**
     * Processo fallito durante l'elaborazione in background
     */
    FAILED("Errore durante elaborazione background"),
    
    /**
     * Order not found
     */
    NOT_FOUND("Order not found");

    private final String description;

    EmissionStatusV3(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static EmissionStatusV3 from(String val) {
        return EmissionStatusV3.valueOf(val);
    }

    @JsonValue
    public String toValue() {
        return name();
    }
}

/**
 * Stato dettagliato del processo di emissione.
 * Utilizzato per tracciare il progresso delle operazioni asincrone.
 */
record EmissionStatusDetailV3(
    @JsonProperty("orderCode") String orderCode,
    @JsonProperty("policyNumber") String policyNumber,
    @JsonProperty("status") EmissionStatusV3 status,
    @JsonProperty("details") String details,
    @JsonProperty("updatedAt") Instant updatedAt
) {}
