package it.yolo.v3.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.util.List;

/**
 * DTO V3 per la risposta delle polizze - versione record
 */
public record PolicyResponseDtoV3(
        @JsonProperty("data") DataPolicyResponseV3 data
) {}

/**
 * <PERSON><PERSON> della polizza V3
 */
public record DataPolicyResponseV3(
        @JsonProperty("id") Long id,
        @JsonProperty("policyCode") String policyCode,
        @JsonProperty("startDate") String startDate,
        @JsonProperty("endDate") String endDate,
        @JsonProperty("customer") CustomerResponseV3 customer,
        @JsonProperty("product") ProductResponseV3 product,
        @JsonProperty("orderId") Long orderId,
        @JsonProperty("externalCode") String externalCode,
        @JsonProperty("originalPolicyNumber") String originalPolicyNumber,
        @JsonProperty("state") AnagStateResponseV3 state,
        @JsonProperty("payment") PaymentReferenceResponseV3 payment,
        @JsonProperty("choosenProperties") JsonNode choosenProperties,
        @JsonProperty("insurancePremium") BigDecimal insurancePremium,
        @JsonProperty("warranties") List<PolicyWarrantiesResponseV3> warranties,
        @JsonProperty("additionalPolicyInfo") JsonNode additionalPolicyInfo,
        @JsonProperty("createdAt") String createdAt,
        @JsonProperty("updatedAt") String updatedAt,
        @JsonProperty("quantity") Long quantity,
        @JsonProperty("insuredIsContractor") Boolean insuredIsContractor,
        @JsonProperty("certificateFileName") String certificateFileName,
        @JsonProperty("certificateLink") String certificateLink,
        @JsonProperty("certificateContentType") String certificateContentType,
        @JsonProperty("certificateFileSize") String certificateFileSize,
        @JsonProperty("certificateUpdatedAt") String certificateUpdatedAt,
        @JsonProperty("certificate") String certificate,
        @JsonProperty("type") String type,
        @JsonProperty("withdrawalRequestDate") String withdrawalRequestDate,
        @JsonProperty("renewedAt") String renewedAt,
        @JsonProperty("markedAsRenewable") Boolean markedAsRenewable,
        @JsonProperty("policySubstitution") Boolean policySubstitution,
        @JsonProperty("masterPolicyNumber") String masterPolicyNumber,
        @JsonProperty("nextBillingDate") String nextBillingDate,
        @JsonProperty("name") String name,
        @JsonProperty("username") String username,
        @JsonProperty("password") String password,
        @JsonProperty("isWithdrawable") Boolean isWithdrawable,
        @JsonProperty("isDeactivable") Boolean isDeactivable,
        @JsonProperty("subscriptionId") String subscriptionId
) {}

/**
 * Cliente della polizza V3
 */
public record CustomerResponseV3(
        @JsonProperty("id") Long id
) {}

/**
 * Prodotto della polizza V3
 */
public record ProductResponseV3(
        @JsonProperty("id") Long id,
        @JsonProperty("code") String code,
        @JsonProperty("name") String name
) {}

/**
 * Stato anagrafico della polizza V3
 */
public record AnagStateResponseV3(
        @JsonProperty("id") Long id,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description
) {}

/**
 * Riferimento pagamento V3
 */
public record PaymentReferenceResponseV3(
        @JsonProperty("id") Long id,
        @JsonProperty("type") String type,
        @JsonProperty("reference") String reference
) {}

/**
 * Garanzie della polizza V3
 */
public record PolicyWarrantiesResponseV3(
        @JsonProperty("id") Long id,
        @JsonProperty("name") String name,
        @JsonProperty("description") String description,
        @JsonProperty("amount") BigDecimal amount
) {}
