package it.yolo.emission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.pg.PGClient;
import it.yolo.client.pg.request.CertificateRequest;
import it.yolo.client.pg.response.PgCertificateResponse;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.request.UploadDtoRequest;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.DocumentException;
import it.yolo.exception.GenerateMacException;
import it.yolo.exception.PGException;
import it.yolo.exception.PolicyException;
import it.yolo.service.ServiceOrder;
import it.yolo.service.client.V2.InvokePolicyV2;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
@SendEmail
public class EmailEmissionImpl implements Emission{

    @Inject
    @RestClient
    PolicyClient policyClient;


    @Inject
    JsonWebToken jsonWebToken;

    @Override
    public EmissionResponseDto emission(EmissionRequestDto emissionDto) throws PolicyException, PGException, GenerateMacException {
        PolicyRequestDto policyRequest = generateRequestPolicy(emissionDto);
        String token="Bearer "+jsonWebToken.getRawToken();
        PolicyResponseDto policyResponse;
        try {
            policyResponse = policyClient.create(token, policyRequest).readEntity(PolicyResponseDto.class);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        EmissionResponseDto emissionResponseDto=policyToEmission(policyResponse);
        return emissionResponseDto;
    }

    @Override
    public CertificateResponseDto generateCertificate(EmissionResponseDto emissionResponseDto, CertificateRequestDto certificateRequestDto) {
        return null;
    }

    private PolicyRequestDto generateRequestPolicy(EmissionRequestDto emissionRequestDto){
        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest= new DataPolicyRequestDto();
        dataPolicyRequest.setStartDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(0).getStartDate());
        dataPolicyRequest.setType(emissionRequestDto.getOrder().getResponse().getProductType());
        dataPolicyRequest.setEndDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(0).getExpirationDate());
        dataPolicyRequest.setSubscriptionId(emissionRequestDto.getSubscriptionId());
        dataPolicyRequest.setOrderIdCode(String.valueOf(emissionRequestDto.getOrder().getResponse().getId()));
        dataPolicyRequest.getCustomer().setId(Long.valueOf(emissionRequestDto.getCustomer().getData().getId()));
        dataPolicyRequest.getProduct().setId(Long.valueOf(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getId()));
        if(emissionRequestDto.getOrder().getResponse().getPaymentToken()!=null){
            dataPolicyRequest.getPayment().setPaymentToken(emissionRequestDto.getOrder().getResponse().getPaymentToken());
        }
        if(emissionRequestDto.getOrder().getResponse().getPaymentType()!=null){
            dataPolicyRequest.getPayment().setPaymentType(emissionRequestDto.getOrder().getResponse().getPaymentType());
        }
        if(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()!=null){
            dataPolicyRequest.getPayment().setPaymentTransactionId(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()));
            dataPolicyRequest.getPayment().setId(Integer.valueOf(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId())));
        }
        dataPolicyRequest.setName(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode().toUpperCase());
        dataPolicyRequest.setInsurancePremium(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsurancePremium());
        dataPolicyRequest.setQuantity(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getQuantity());
        dataPolicyRequest.setOrderItemId(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getId());
        policyRequestDto.setDto(dataPolicyRequest);
        return policyRequestDto;
    }

    private EmissionResponseDto policyToEmission(PolicyResponseDto policyResponseDto){
        EmissionResponseDto emissionResponseDto= new EmissionResponseDto();
        emissionResponseDto.setPolicyResponseDto(policyResponseDto);
        return emissionResponseDto;
    }

}
