package it.yolo.emission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.pg.PGClient;
import it.yolo.client.pg.request.CertificateRequest;
import it.yolo.client.pg.request.CertificateRequestV2;
import it.yolo.client.pg.response.PgCertificateResponse;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequest;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.request.UploadDtoRequest;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.DocumentException;
import it.yolo.exception.GenerateMacException;
import it.yolo.exception.PGException;
import it.yolo.exception.PolicyException;
import it.yolo.service.ServiceEmissionManager;
import it.yolo.service.ServiceOrder;
import it.yolo.service.client.V2.InvokePolicyV2;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

import org.jboss.logging.Logger;

@RequestScoped
@External
public class ExternalEmissionImpl implements Emission{


    private static String ASYNC_CERTIFICATE ="external_async";

    private static String INTERNAL_CERTIFICATE ="internal";

    private static String GEN_ASYNC_CERTIFICATE = "external_generation_async";

    private static String GEN_SYNC_CERTIFICATE = "external_generation_sync";

    private static String NONE_CERTIFICATE = "none";
//
//    @Inject
//    @RestClient
//    AdpPgGateway adpPgClient;

    @Inject
    @RestClient
    PGClient pgClient;

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    ServiceEmissionManager serviceEmissionManager;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @ConfigProperty(name = "emission.password")
    String emissionPassword;

    @ConfigProperty(name = "emission.username")
    String emissionUsername;

    @ConfigProperty(name = "tenant.name")
    String tenant;

    @ConfigProperty(name = "key.mac")
    String key;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    InvokePolicyV2 invokePolicy;

    @Inject
    Logger log;

    private static int ITEM_INDEX=0;

    @Override
    public EmissionResponseDto emission(EmissionRequestDto emissionDto) throws PolicyException, PGException, GenerateMacException {

        String token = "Bearer "+jsonWebToken.getRawToken();
        emissionDto.setEmissionPassword(emissionPassword);
        emissionDto.setEmissionUsername(emissionUsername);

        //logica gestione verso il pg
        String startDate= emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getStartDate().contains(".") ?
                emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getStartDate() :
                emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getStartDate()+".000";
        String exspireOrder=emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getExpirationDate().contains(".") ?
                emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getExpirationDate() :
                emissionDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getExpirationDate()+".000";
        String utmSource = emissionDto.getOrder().getResponse().getUtmSource();
        String agenziaDiRiferimento = emissionDto.getOrder().getResponse().getAgenziaDiRiferimento();

        Response responsePg;
        EmissionRequest request=new EmissionRequest();
        request.setData(emissionDto);
        request.setTenant(tenant);
        ObjectMapper mapper=new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        boolean warrantiesUpdated=emissionDto.getOrder().getResponse().getParentOrder()!=null;
        request.getData().getPolicy().setPolicySubstitution(warrantiesUpdated);
        if(warrantiesUpdated){
            PolicyResponseDto oldPolicy=policyClient.getOrderId(token,
                    emissionDto.getParentOrderId()).readEntity(PolicyResponseDto.class);
            request.getData().getPolicy().setOriginalPolicyNumber(oldPolicy.getData().getPolicyCode());
        }
        JsonNode reqForPg=mapper.valueToTree(request);
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).put("start_date", startDate);
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).put("expiration_date", exspireOrder);
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).put("utmSource", utmSource);
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).put("agenziaDiRiferimento", agenziaDiRiferimento);
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).remove("packets");
        ((ObjectNode)reqForPg.get("data").get("order").get("data")).remove("product");
        ((ObjectNode)reqForPg.get("data").get("product").get("data")).remove("images");
        for(JsonNode packet : reqForPg.get("data").get("product").get("data").get("packets")) {
            for (JsonNode warranty : packet.withArray("warranties")) {
                ((ObjectNode) warranty.get("anagWarranty")).remove("images");
            }
        }
        ((ObjectNode)reqForPg.get("data").get("order").get("data").get("packet").get("data").get("product")).remove("images");
        if (reqForPg.get("data").get("order").get("data").get("orderItem").get(0).get("instance").has("ceilings")) {
            for (JsonNode ceiling : reqForPg.get("data").get("order").get("data").get("orderItem").get(0).get("instance").withArray("ceilings")) {
                ((ObjectNode) ceiling.get("anagWarranty")).remove("images");
            }
        }
        ((ObjectNode)reqForPg.get("data").get("order").get("data").get("orderItem").get(0)).remove("start_date");
        ((ObjectNode)reqForPg.get("data").get("order").get("data").get("orderItem").get(0)).remove("expiration_date");
        ((ObjectNode)reqForPg.get("data").get("order").get("data").get("orderItem").get(0)).put("start_date", startDate);
        ((ObjectNode)reqForPg.get("data").get("order").get("data").get("orderItem").get(0)).put("expiration_date", exspireOrder);
        responsePg = pgClient.emission(reqForPg);
        JsonNode pgResponseDt=responsePg.readEntity(JsonNode.class);
        OrderResponseDto orderResponse = serviceOrder.updateEmissionDetail(token, emissionDto.getOrder().getResponse().getOrderCode(),emissionDto.getOrder().getResponse().getOrderItem().get(0).getId(),pgResponseDt);
        PolicyRequestDto policyRequest = generateRequestPolicy(emissionDto,pgResponseDt);
        PolicyResponseDto policyResponse;
        if(warrantiesUpdated){
            if(emissionDto.getLastParentOrderId()==null) {
                policyResponse = invokePolicy.duplicatePolicy(token, emissionDto.getOrder().getResponse().getId(), emissionDto.getParentOrderId(),
                        emissionDto.getOrder().getResponse().getOrderItem().get(0).getId(), policyRequest.getDto().getPolicyCode());
            } else {
                policyResponse = invokePolicy.duplicatePolicy(token, emissionDto.getOrder().getResponse().getId(), emissionDto.getLastParentOrderId(),
                        emissionDto.getOrder().getResponse().getOrderItem().get(0).getId(), policyRequest.getDto().getPolicyCode());
            }
        }else {
            policyResponse = policyClient.create(token, policyRequest).readEntity(PolicyResponseDto.class);
        }
        EmissionResponseDto emissionResponseDto=policyToEmission(policyResponse);
        if(pgResponseDt.get("certificate")!=null && pgResponseDt.get("certificate").asText()!="null"){
            String certificate=pgResponseDt.get("certificate").asText();
            emissionResponseDto.setCertificate(certificate);
        }
        emissionResponseDto.setPgResponse(pgResponseDt);
        emissionResponseDto.setEmission(orderResponse.getResponse().getOrderItem().get(ITEM_INDEX).getEmission());
        return emissionResponseDto;
    }

    @Override
    public CertificateResponseDto generateCertificate(EmissionResponseDto emissionResponseDto, CertificateRequestDto certificateRequestDto) {
        ((ObjectNode)certificateRequestDto.getProduct().get("data")).remove("images");
        for(JsonNode packet : certificateRequestDto.getProduct().get("data").get("packets")) {
            for (JsonNode warranty : packet.withArray("warranties")) {
                ((ObjectNode) warranty.get("anagWarranty")).remove("images");
            }
        }
        CertificateResponseDto certificateResponseDto=null;
        String externalCertificate= certificateRequestDto.getProduct().get("data").get("configuration").get("certificate").asText();
        // serve a capire se deve essere recuperato il certificato in quanto le compagnie non restituisco sempre il certificato alla prima chiamata
        if(externalCertificate.equalsIgnoreCase(ASYNC_CERTIFICATE)){
            log.infov("External async certificate");
            certificateResponseDto = getCertificateAsync(certificateRequestDto, emissionResponseDto,
                    certificateRequestDto.getPolicy().getData().getPolicyCode()+".pdf");
            return certificateResponseDto;
        }
        if (externalCertificate.equalsIgnoreCase(INTERNAL_CERTIFICATE)){
            certificateResponseDto=getCertificateInternal(certificateRequestDto);
            return certificateResponseDto;
        }
        if (externalCertificate.equalsIgnoreCase(GEN_ASYNC_CERTIFICATE)){

            CertificateRequestV2 certificateRequestV2 = new CertificateRequestV2();
            certificateRequestV2.setData(certificateRequestDto);

            certificateResponseDto = certificateAsync(certificateRequestV2, emissionResponseDto,
                certificateRequestDto.getPolicy().getData().getPolicyCode() + ".pdf");
            return certificateResponseDto;

        }
        if (externalCertificate.equalsIgnoreCase(GEN_SYNC_CERTIFICATE)){
            log.infov("External sync certificate");
            ObjectNode productData = (ObjectNode) certificateRequestDto.getProduct().get("data");
            JsonNode packets = productData.get("packets");
            Integer targetPacketId = certificateRequestDto.getOrder().getResponse().getPacketId();

            // Crea un nuovo array contenente solo il pacchetto corrispondente
            ArrayNode newPackets = JsonNodeFactory.instance.arrayNode();
            for (JsonNode p : packets) {
                if (p.has("id") && p.get("id").asInt() == targetPacketId) {
                    newPackets.add(p);
                }
            }
            // Sostituisce l'array originale con quello nuovo
            productData.set("packets", newPackets);
            CertificateRequestV2 certificateRequestV2 = new CertificateRequestV2();
            certificateRequestV2.setData(certificateRequestDto);
            certificateResponseDto = certificateSync(certificateRequestV2, emissionResponseDto,
            certificateRequestDto.getPolicy().getData().getPolicyCode()+".pdf");
            return certificateResponseDto;
        }
        if (externalCertificate.equalsIgnoreCase(NONE_CERTIFICATE)){
            certificateResponseDto = getNoneCertificate(certificateRequestDto);
            return certificateResponseDto;
        }
        certificateResponseDto=getCertificateSync(certificateRequestDto);
        return certificateResponseDto;
    }


    private PolicyRequestDto generateRequestPolicy(EmissionRequestDto emissionRequestDto, JsonNode response){



        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest= new DataPolicyRequestDto();
        //dataPolicyRequest.setStateId(STATE_ID_POLICY);
        dataPolicyRequest.setStartDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(0).getStartDate());
        dataPolicyRequest.setType(emissionRequestDto.getOrder().getResponse().getProductType());
        dataPolicyRequest.setEndDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(0).getExpirationDate());
        dataPolicyRequest.setSubscriptionId(emissionRequestDto.getSubscriptionId());
        dataPolicyRequest.setPolicyCode(response.get("policyNumber").asText());


        dataPolicyRequest.setOrderIdCode(String.valueOf(emissionRequestDto.getOrder().getResponse().getId()));
        dataPolicyRequest.getCustomer().setId(Long.valueOf(emissionRequestDto.getCustomer().getData().getId()));
        dataPolicyRequest.getProduct().setId(Long.valueOf(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getId()));
        if(emissionRequestDto.getOrder().getResponse().getPaymentToken()!=null){
            dataPolicyRequest.getPayment().setPaymentToken(emissionRequestDto.getOrder().getResponse().getPaymentToken());
        }

        if(emissionRequestDto.getOrder().getResponse().getPaymentType()!=null){
            dataPolicyRequest.getPayment().setPaymentType(emissionRequestDto.getOrder().getResponse().getPaymentType());
        }

        if(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()!=null){
            dataPolicyRequest.getPayment().setPaymentTransactionId(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()));
            dataPolicyRequest.getPayment().setId(Integer.valueOf(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId())));
        }
        if(emissionRequestDto.getPayment_frequency()!=null){
            dataPolicyRequest.setPaymentFrequency(emissionRequestDto.getPayment_frequency());
        }
        dataPolicyRequest.setName(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode().toUpperCase());
        dataPolicyRequest.setInsurancePremium(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsurancePremium());
        dataPolicyRequest.setQuantity(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getQuantity());
        dataPolicyRequest.setOrderItemId(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getId());
        policyRequestDto.setDto(dataPolicyRequest);

        return policyRequestDto;
    }

    private EmissionResponseDto policyToEmission(PolicyResponseDto policyResponseDto){
        EmissionResponseDto emissionResponseDto= new EmissionResponseDto();
        emissionResponseDto.setPolicyResponseDto(policyResponseDto);
        return emissionResponseDto;
    }

    private CertificateResponseDto certificateAsync(CertificateRequestV2 certificateRequest, EmissionResponseDto emissionResponseDto,
                                                String fileName){
         Response responsePg = null;
        try {
           responsePg = pgClient.certificate(certificateRequest);
        } catch (Exception e) {
            log.infov("Non è stato possibile recuperare il certificato"); 
            CertificateResponseDto emptyResponse = new CertificateResponseDto();
            return emptyResponse;
        }
        
        PgCertificateResponse pgResponseDto= responsePg.readEntity(PgCertificateResponse.class);
        String certificate=pgResponseDto.getData().getCertificate();
        fileName= pgResponseDto.getData().getPolicyNumber()+".pdf";
        
        Response response = null;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificate, fileName));
        } catch (Exception e){
            log.infov("Non è stato possibile recuperare il certificato"); 
        }
    
        CertificateResponseDto responseDto = response.readEntity(CertificateResponseDto.class);
        if(responseDto.getFile()==null){
            responseDto.setFile(certificate);
        }
        return responseDto;
    }

    private CertificateResponseDto certificateSync(CertificateRequestV2 certificateRequest, EmissionResponseDto emissionResponseDto,
                                                String fileName){
        Response responsePg = null;
        responsePg = pgClient.certificate(certificateRequest);
        
        PgCertificateResponse pgResponseDto= responsePg.readEntity(PgCertificateResponse.class);
        String certificate=pgResponseDto.getData().getCertificate();
        fileName= pgResponseDto.getData().getPolicyNumber()+".pdf";

        Response response = null;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificate, fileName));
        } catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
    
        CertificateResponseDto responseDto = response.readEntity(CertificateResponseDto.class);
        if(responseDto.getFile()==null){
            responseDto.setFile(certificate);
        }
        return responseDto;
    }

    private CertificateResponseDto getCertificateAsync(CertificateRequestDto certificateRequest, EmissionResponseDto emissionResponseDto,
                                                       String fileName){
        CertificateRequest request=new CertificateRequest();
        request.setTenant(tenant);
        request.setProductCode(certificateRequest.getProduct().get("data").get("code").asText());
//        request.getProductData().setPartnerToken(emissionResponseDto.getPgResponse().getParameters().getHeaders().getxPartner());
//        request.getProductData().setUserToken(StringUtils.remove(emissionResponseDto.getPgResponse().getParameters().getHeaders().getAuthorization(), "AUTH_TOKEN "));
        request.getProductData().setDocumentId(emissionResponseDto.getPgResponse().get("policyNumber").asText());
        Response responsePg= pgClient.getCertificate(request);
        PgCertificateResponse pgResponseDto= responsePg.readEntity(PgCertificateResponse.class);
        String certificate=pgResponseDto.getData().getCertificate();
        fileName= pgResponseDto.getData().getPolicyNumber()+".pdf";
        Response response;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificate, fileName));
        } catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        /* PULIZIA VERTICALIZZAZIONE EMISSIONE ESTERNA PER
        if(request.getProductCode().equalsIgnoreCase("ehealth-quixa-standard")){
            emissionResponseDto.getPolicyResponseDto().getData().setPolicyCode(pgResponseDto.getData().getPolicyNumber());
        }*/
        CertificateResponseDto responseDto=response.readEntity(CertificateResponseDto.class);
        if(responseDto.getFile()==null){
            responseDto.setFile(certificate);
        }
        return responseDto;
    }

    private CertificateResponseDto getCertificateInternal(CertificateRequestDto certificateRequestDto){
        Response response;
        try {
            response=documentClient.generateCertficate(certificateRequestDto);
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto certificateResponseDto=response.readEntity(CertificateResponseDto.class);
        String certficate=certificateResponseDto.getFile();
        certificateRequestDto.setCertificate(certficate);
        return certificateResponseDto;
    }

    private CertificateResponseDto getNoneCertificate(CertificateRequestDto certificateRequestDto){
        CertificateResponseDto noneCertificateResponseDTO = new CertificateResponseDto();
        String certificate= "none";
        noneCertificateResponseDTO.setFile(certificate);
        noneCertificateResponseDTO.setLink(certificate);
        noneCertificateResponseDTO.setNomeFile(certificate);
        noneCertificateResponseDTO.setType(certificate);
        return noneCertificateResponseDTO;
    }

    private CertificateResponseDto getCertificateSync(CertificateRequestDto certificateRequestDto){
        Response response;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificateRequestDto.getCertificate(),certificateRequestDto.getPolicy().getData().getPolicyCode()+".pdf"));
        } catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto responseDto=response.readEntity(CertificateResponseDto.class);
        if(responseDto.getFile()==null){
            responseDto.setFile(certificateRequestDto.getCertificate());
        }
        return responseDto;
    }

    private PgResponseDto stringToPgResponseDto(String string) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.readValue(string, PgResponseDto.class);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


}