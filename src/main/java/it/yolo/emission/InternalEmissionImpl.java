package it.yolo.emission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.DocumentException;
import it.yolo.exception.PolicyException;
import it.yolo.service.ServiceOrder;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RequestScoped
@Internal
public class InternalEmissionImpl implements Emission {


    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceOrder serviceOrder;

    private static final String STATE_ID_POLICY = "9";
    private static final int ITEM_INDEX=0;
    private static final String NONE_CERTIFICATE = "none";

    @Override
    public EmissionResponseDto emission(EmissionRequestDto emissionDto) {
        String token = "Bearer "+ jsonWebToken.getRawToken();
        PolicyRequestDto policyResquest = generateRequestPolicy(emissionDto);
        Response response;
        try {
            response = policyClient.create(token, policyResquest);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        String emission = response.readEntity(String.class);
        OrderResponseDto orderResponse = serviceOrder.updateEmissionDetail(token, emissionDto.getOrder().getResponse().getOrderCode(),emissionDto.getOrder().getResponse().getOrderItem().get(0).getId(), emission);
        PolicyResponseDto policyResponseDto=this.stringToPolicyResponseDto(emission);
        EmissionResponseDto emissionResponseDto=policyToEmission(policyResponseDto);
        emissionResponseDto.setEmission(orderResponse.getResponse().getOrderItem().get(ITEM_INDEX).getEmission());
        return emissionResponseDto;
    }

    @Override
    public CertificateResponseDto generateCertificate(EmissionResponseDto emissionResponseDto, CertificateRequestDto certificateRequestDto) {
        Response response;
        CertificateResponseDto certificateResponseDto;

        String internalCertificate = certificateRequestDto.getProduct().get("data").get("configuration").get("certificate").asText();
        if (internalCertificate.equalsIgnoreCase(NONE_CERTIFICATE)){
            certificateResponseDto = getNoneCertificate();
            return certificateResponseDto;
        }

        try {
            response= documentClient.generateCertficate(certificateRequestDto);
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }

        certificateResponseDto = response.readEntity(CertificateResponseDto.class);
        certificateRequestDto.setCertificate(certificateResponseDto.getFile());
        return certificateResponseDto;
    }

    //MAP REQUEST FOR IAD
    private PolicyRequestDto generateRequestPolicy(EmissionRequestDto emissionRequestDto){

        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest= new DataPolicyRequestDto();
        //dataPolicyRequest.setStateId(STATE_ID_POLICY);
        dataPolicyRequest.setStartDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(0).getStartDate());
        dataPolicyRequest.setType(emissionRequestDto.getOrder().getResponse().getProductType());
        dataPolicyRequest.setEndDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                                    .get(0).getExpirationDate());
        JsonNode properties=emissionRequestDto.getProduct().get("data").get("configuration").get("properties");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        if(properties.has("minusDay") && properties.get("minusDay").asBoolean()){
            LocalDateTime endDate=LocalDateTime.parse(dataPolicyRequest.getEndDate().replace("T", " "), dateTimeFormatter);
            dataPolicyRequest.setEndDate(endDate.minusDays(1).toString());
        }
        int lastDotIndex=dataPolicyRequest.getStartDate().lastIndexOf(".");
        if(lastDotIndex!=-1 && dataPolicyRequest.getStartDate().length()-lastDotIndex-4>=0){
            dataPolicyRequest.setStartDate(dataPolicyRequest.getStartDate().substring(0, lastDotIndex+4));
        }
        String date=dataPolicyRequest.getStartDate().replace("T", " ");
        LocalDateTime startDate= LocalDate.parse(date.split(" ")[0]).atStartOfDay();
        if(startDate.isBefore(LocalDateTime.now())) {
            startDate=LocalDateTime.now();
        }
        dataPolicyRequest.setStartDate(startDate.toString());
        dataPolicyRequest.setPolicyCode(emissionRequestDto.getPolicy().getPolicyCode());
        dataPolicyRequest.setOrderIdCode(String.valueOf(emissionRequestDto.getOrder().getResponse().getId()));
        dataPolicyRequest.getCustomer().setId(Long.valueOf(emissionRequestDto.getCustomer().getData().getId()));
        dataPolicyRequest.getProduct().setId(Long.valueOf(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getId()));
        if(emissionRequestDto.getOrder().getResponse().getPaymentToken()!=null){
            dataPolicyRequest.getPayment().setPaymentToken(emissionRequestDto.getOrder().getResponse().getPaymentToken());
        }

        if(emissionRequestDto.getOrder().getResponse().getPaymentType()!=null){
            dataPolicyRequest.getPayment().setPaymentType(emissionRequestDto.getOrder().getResponse().getPaymentType());
        }

        if(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()!=null){
            dataPolicyRequest.getPayment().setPaymentTransactionId(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()));
            dataPolicyRequest.getPayment().setId(Integer.valueOf(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId())));
        }
        dataPolicyRequest.setInsurancePremium(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsurancePremium());
        dataPolicyRequest.setQuantity(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getQuantity());
        dataPolicyRequest.setName(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode().toUpperCase());
        dataPolicyRequest.setOrderItemId(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getId());
        policyRequestDto.setDto(dataPolicyRequest);
        return policyRequestDto;
    }


    private EmissionResponseDto policyToEmission(PolicyResponseDto policyResponseDto){
        EmissionResponseDto emissionResponseDto= new EmissionResponseDto();
        emissionResponseDto.setPolicyResponseDto(policyResponseDto);
        return emissionResponseDto;
    }

    private PolicyResponseDto stringToPolicyResponseDto(String string) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.readValue(string, PolicyResponseDto.class);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private CertificateResponseDto getNoneCertificate(){
        CertificateResponseDto noneCertificateResponseDTO = new CertificateResponseDto();
        noneCertificateResponseDTO.setFile(NONE_CERTIFICATE);
        noneCertificateResponseDTO.setLink(NONE_CERTIFICATE);
        noneCertificateResponseDTO.setNomeFile(NONE_CERTIFICATE);
        noneCertificateResponseDTO.setType(NONE_CERTIFICATE);
        return noneCertificateResponseDTO;
    }

}
