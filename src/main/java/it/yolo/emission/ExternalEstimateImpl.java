package it.yolo.emission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.adp.pg.AdpPgGateway;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.pg.PGClient;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.request.UploadDtoRequest;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.DocumentException;
import it.yolo.exception.PGException;
import it.yolo.exception.PolicyException;
import it.yolo.service.ServiceOrder;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.time.LocalDateTime;
import java.util.Base64;

@RequestScoped
@ExternalEstimate
public class ExternalEstimateImpl implements Emission {

    private static final String ASYNC_CERTIFICATE = "external_async";

    private static final String INTERNAL_CERTIFICATE = "internal";

    private static final String CERTIFICATE_STUB = "internal";

    private static final String STATE_ID_POLICY = "9";

    private static final int ITEM_INDEX = 0;

    private static final String ACTION_PREVENTIVAZIONE_ADAPTER = "preventivazione";

    private static final String ACTION_EMISSIONE_ADAPTER = "emissione";

    private static final String INSURANCE_ID_KEY = "insuranceId";


    @Inject
    @RestClient
    AdpPgGateway adpPgClient;

    @Inject
    @RestClient
    PGClient pgClient;

    @Inject
    @RestClient
    PolicyClient policyClient;


    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @ConfigProperty(name = "emission.password")
    String emissionPassword;

    @ConfigProperty(name = "emission.username")
    String emissionUsername;


    @Inject
    ServiceOrder serviceOrder;


    @Override
    public EmissionResponseDto emission(EmissionRequestDto emissionDto) throws PGException, PolicyException {
        Response response = null;
        String token = "Bearer " + jsonWebToken.getRawToken();
        emissionDto.setEmissionPassword(emissionPassword);
        emissionDto.setEmissionUsername(emissionUsername);

        //logica gestione data verso il pg
        LocalDateTime startDate = mapStartDateOrder(emissionDto);
        LocalDateTime exspireOrder = mapExspireDateOrder(emissionDto);

        PgResponseDto pgResponseDto = externalEstimate(emissionDto);
        PolicyRequestDto policyResquest = generateRequestPolicy(emissionDto, pgResponseDto);
        try {
            response = policyClient.create(token, policyResquest);
            PolicyResponseDto policyResponseDto = response.readEntity(PolicyResponseDto.class);
            EmissionResponseDto emissionResponseDto = policyToEmission(policyResponseDto);
            String certificate = pgResponseDto.getCertificate();
            emissionResponseDto.setCertificate(certificate);
            return emissionResponseDto;
        } catch (Exception e) {
            throw new PolicyException(e.getMessage());
        }
    }

    @Override
    public CertificateResponseDto generateCertificate(EmissionResponseDto emissionResponseDto, CertificateRequestDto certificateRequestDto) {

        CertificateResponseDto certificateResponseDto = null;
        String externalCerifcate = certificateRequestDto.getProduct().get("data").get("configuration").get("certificate").asText();
        // serve a capire se deve essere recuperato il certificato , in quanto le compagnie non restituisco sempre il certificato alla prima chiamata
        if (externalCerifcate.equalsIgnoreCase(ASYNC_CERTIFICATE)) {
            certificateResponseDto = getCertificateAsync(emissionResponseDto, certificateRequestDto.getPolicy().getData().getPolicyCode()+".pdf");
            return certificateResponseDto;
        }
        if (externalCerifcate.equalsIgnoreCase(INTERNAL_CERTIFICATE)) {
            certificateResponseDto = getCertificateInternal(certificateRequestDto);
            return certificateResponseDto;
        }
        certificateResponseDto = getCertificateSync(certificateRequestDto);
        return certificateResponseDto;
    }


    private PolicyRequestDto generateRequestPolicy(EmissionRequestDto emissionRequestDto, PgResponseDto response) {

        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        DataPolicyRequestDto dataPolicyRequest = new DataPolicyRequestDto();
        //dataPolicyRequest.setStateId(STATE_ID_POLICY);
        dataPolicyRequest.setStartDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(ITEM_INDEX).getStartDate());
        dataPolicyRequest.setType(emissionRequestDto.getOrder().getResponse().getProductType());
        dataPolicyRequest.setEndDate(emissionRequestDto.getOrder().getResponse().getOrderItem()
                .get(ITEM_INDEX).getExpirationDate());


        dataPolicyRequest.setPolicyCode(response.getInsuranceId());
        dataPolicyRequest.setOrderIdCode(String.valueOf(emissionRequestDto.getOrder().getResponse().getId()));
        dataPolicyRequest.getCustomer().setId(Long.valueOf(emissionRequestDto.getCustomer().getData().getId()));
        dataPolicyRequest.getProduct().setId(Long.valueOf(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getId()));
        dataPolicyRequest.getPayment().setPaymentToken(emissionRequestDto.getOrder().getResponse().getPaymentToken());
        dataPolicyRequest.getPayment().setPaymentType(emissionRequestDto.getOrder().getResponse().getPaymentType());
        dataPolicyRequest.getPayment().setPaymentTransactionId(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()));
        dataPolicyRequest.getPayment().setId(Integer.valueOf(emissionRequestDto.getOrder().getResponse().getPaymentTransactionId()));
        dataPolicyRequest.setInsurancePremium(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsurancePremium());
        dataPolicyRequest.setQuantity(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getQuantity());
        dataPolicyRequest.setName(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode().toUpperCase());
        dataPolicyRequest.setOrderItemId(emissionRequestDto.getOrder().getResponse().getOrderItem().get(0).getId());
        policyRequestDto.setDto(dataPolicyRequest);

        return policyRequestDto;
    }

    private EmissionResponseDto policyToEmission(PolicyResponseDto policyResponseDto) {
        EmissionResponseDto emissionResponseDto = new EmissionResponseDto();
        emissionResponseDto.setPolicyResponseDto(policyResponseDto);
        return emissionResponseDto;
    }


    private CertificateResponseDto getCertificateAsync(EmissionResponseDto emissionResponseDto, String nomeFile) {
//        Response response= pgClient.getCertificate();
//        PgResponseDto pgResponseDto= response.readEntity(PgResponseDto.class);
        //certficate=pgResponseDto.getCertificate();
        PgResponseDto pgResponseDto = new PgResponseDto();
//        pgResponseDto.setCertificate(Base64.getDecoder().decode(CERTIFICATE_STU0B));
//                pgResponseDto.setCertificate();
        String certificate = Base64.getDecoder().decode(pgResponseDto.getCertificate()).toString();
        Response response;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificate, nomeFile));
        } catch (Exception e) {
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto responseDto = response.readEntity(CertificateResponseDto.class);
        return responseDto;
    }

    private CertificateResponseDto getCertificateInternal(CertificateRequestDto certificateRequestDto) {
        Response response;
        try {
            response = documentClient.generateCertficate(certificateRequestDto);
        } catch (Exception e) {
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto certificateResponseDto = response.readEntity(CertificateResponseDto.class);
        String certficate = certificateResponseDto.getFile();
        certificateRequestDto.setCertificate(certficate);
        return certificateResponseDto;
    }

    private CertificateResponseDto getCertificateSync(CertificateRequestDto certificateRequestDto) {
        Response response;
        try {
            response = documentClient.uploadDocument(new UploadDtoRequest(certificateRequestDto.getCertificate(),certificateRequestDto.getPolicy().getData().getPolicyCode()+".pdf"));
        } catch (Exception e) {
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto responseDto = response.readEntity(CertificateResponseDto.class);
        if(responseDto.getFile()==null){
            responseDto.setFile(certificateRequestDto.getCertificate());
        }
        return responseDto;
    }


    private LocalDateTime mapStartDateOrder(EmissionRequestDto emissionRequestDto) {
        LocalDateTime start_Date = emissionRequestDto.getOrder().getResponse()
                .startDate(LocalDateTime.parse(emissionRequestDto.getOrder().getResponse().getOrderItem().get(ITEM_INDEX).getStartDate()));
        return start_Date;
    }

    private LocalDateTime mapExspireDateOrder(EmissionRequestDto emissionRequestDto) {
        LocalDateTime exspireOrder = emissionRequestDto.getOrder().getResponse().expirationDate(emissionRequestDto.getOrder().getResponse().getStart_date());
        return exspireOrder;
    }

    private PgResponseDto externalEstimate(EmissionRequestDto request) throws PGException {
        try {

//            request.setAction(ACTION_PREVENTIVAZIONE_ADAPTER);
//            Response responsePrev = adpPgClient.trasfomrRequest(request);
//            JsonNode jsonNodePrev = responsePrev.readEntity(JsonNode.class);
            ObjectMapper mapper=new ObjectMapper();
            JsonNode reqForPgEstimate=mapper.valueToTree(request);
            ((ObjectNode)reqForPgEstimate).put("action", ACTION_PREVENTIVAZIONE_ADAPTER);
            Response responsePgPrev = pgClient.emission(reqForPgEstimate);
            JsonNode jsonNodePgPrev = responsePgPrev.readEntity(JsonNode.class);

//            request.setAction(ACTION_EMISSIONE_ADAPTER);
            String insuranceId = jsonNodePgPrev.get(INSURANCE_ID_KEY).textValue();
            request.setInsuranceId(insuranceId);
//
//            Response responseEmission = adpPgClient.trasfomrRequest(request);
//            JsonNode responseEmissionPg = responseEmission.readEntity(JsonNode.class);
            JsonNode reqForPg=mapper.valueToTree(request);
            ((ObjectNode)reqForPg).put("action", ACTION_EMISSIONE_ADAPTER);
            Response emissionPolicy = pgClient.emission(reqForPg);
            String emission = emissionPolicy.readEntity(String.class);

            return this.stringToPgResponseDto(emission);
        } catch (Exception e) {
            throw new PGException(e.getMessage());
        }


    }

    private PgResponseDto stringToPgResponseDto(String string) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.readValue(string, PgResponseDto.class);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}
