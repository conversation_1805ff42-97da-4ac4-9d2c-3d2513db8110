package it.yolo.emission;

import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.GenerateMacException;
import it.yolo.exception.PGException;
import it.yolo.exception.PolicyException;

public interface Emission {


     EmissionResponseDto emission(EmissionRequestDto emissionDto) throws PGException, PolicyException, GenerateMacException;
     CertificateResponseDto generateCertificate(EmissionResponseDto emissionResponseDto, CertificateRequestDto certificateRequestDto);
}
