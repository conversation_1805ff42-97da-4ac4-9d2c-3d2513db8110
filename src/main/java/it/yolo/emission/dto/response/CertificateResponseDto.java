package it.yolo.emission.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificateResponseDto {

    @JsonProperty("nome_file")
    private String nomeFile;

    @JsonProperty("link")
    private String link;

    @JsonProperty("type")
    private String type;

    @JsonProperty("file")
    private String file;

    @JsonProperty("nome_file")
    public String getNomeFile() {
        return nomeFile;
    }

    @JsonProperty("nome_file")
    public void setNomeFile(String nomeFile) {
        this.nomeFile = nomeFile;
    }

    @JsonProperty("link")
    public String getLink() {
        return link;
    }

    @JsonProperty("link")
    public void setLink(String link) {
        this.link = link;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("file")
    public String getFile() {
        return file;
    }

    @JsonProperty("file")
    public void setFile(String file) {
        this.file = file;
    }
}
