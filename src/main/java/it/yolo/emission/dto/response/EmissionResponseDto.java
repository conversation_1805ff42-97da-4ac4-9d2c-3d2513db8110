package it.yolo.emission.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmissionResponseDto {

    @JsonProperty("policy")
    private PolicyResponseDto policyResponseDto;

    @JsonProperty("certificate")
    private String certificate;

    @JsonProperty("emission")
    private JsonNode emission;

    @JsonIgnore
    private JsonNode pgResponse;

    @JsonProperty("policy")
    public PolicyResponseDto getPolicyResponseDto() {
        return policyResponseDto;
    }

    @JsonProperty("policy")
    public void setPolicyResponseDto(PolicyResponseDto policyResponseDto) {
        this.policyResponseDto = policyResponseDto;
    }

    @JsonProperty("certificate")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificate")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    @JsonProperty("emission")
    public JsonNode getEmission() {
        return emission;
    }

    @JsonProperty("emission")
    public void setEmission(JsonNode emission) {
        this.emission = emission;
    }

    public JsonNode getPgResponse() {
        return pgResponse;
    }

    public void setPgResponse(JsonNode pgResponse) {
        this.pgResponse = pgResponse;
    }
}
