package it.yolo.emission.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UploadDtoRequest {

    @JsonProperty("file")
    private String file;

    @JsonProperty("nome_file")
    private String nomeFile;

    @JsonProperty("file")
    public String getFile() {
        return file;
    }

    @JsonProperty("file")
    public void setFile(String file) {
        this.file = file;
    }

    public UploadDtoRequest(String file, String nomeFile) {
        this.file = file;
        this.nomeFile = nomeFile;
    }

    public UploadDtoRequest() {
    }
}
