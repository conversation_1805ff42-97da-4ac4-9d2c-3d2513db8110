package it.yolo.emission.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificateRequestDto {

    @JsonProperty("product")
    private JsonNode product;

    @JsonProperty("order")
    private OrderResponseDto order;

    @JsonProperty("policy")
    private PolicyResponseDto policy;

    @JsonProperty("customer")
    private CustomerResponseDto customer;

    @JsonProperty("certificate")
    private String  certificate;


    @JsonProperty("product")
    public JsonNode getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(JsonNode product) {
        this.product = product;
    }

    @JsonProperty("order")
    public OrderResponseDto getOrder() {
        return order;
    }
    @JsonProperty("order")
    public void setOrder(OrderResponseDto order) {
        this.order = order;
    }

    @JsonProperty("policy")
    public PolicyResponseDto getPolicy() {
        return policy;
    }

    @JsonProperty("policy")
    public void setPolicy(PolicyResponseDto policy) {
        this.policy = policy;
    }

    @JsonProperty("customer")
    public CustomerResponseDto getCustomer() {
        return customer;
    }

    @JsonProperty("customer")
    public void setCustomer(CustomerResponseDto customer) {
        this.customer = customer;
    }

    @JsonProperty("certificate")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificate")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }
}
