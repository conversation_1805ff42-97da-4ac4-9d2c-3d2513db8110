package it.yolo.emission.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tenant",
        "data"
})
public class EmissionRequest {

    @JsonProperty("data")
    private EmissionRequestDto data;

    @JsonProperty("tenant")
    private String tenant;

    @JsonProperty("data")
    public EmissionRequestDto getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(EmissionRequestDto data) {
        this.data = data;
    }

    @JsonProperty("tenant")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("tenant")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
