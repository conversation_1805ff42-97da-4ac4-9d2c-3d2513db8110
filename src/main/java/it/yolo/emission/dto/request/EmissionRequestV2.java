package it.yolo.emission.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tenant",
        "data"
})
public class EmissionRequestV2 {

    @JsonProperty("data")
    private EmissionRequestDtoV2 data;

    @JsonProperty("tenant")
    private String tenant;

    @JsonProperty("data")
    public EmissionRequestDtoV2 getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(EmissionRequestDtoV2 data) {
        this.data = data;
    }

    @JsonProperty("tenant")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("tenant")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
