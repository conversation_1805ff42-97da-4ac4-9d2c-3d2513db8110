package it.yolo.emission.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.policy.dto.response.DataPolicyResponse;

public class EmissionRequestDtoV2 {

    @JsonProperty("order")
    private JsonNode order;

    @JsonProperty("customer")
    private CustomerResponseDto customer;

    @JsonProperty("product")
    private JsonNode product;

    @JsonProperty("policy")
    private DataPolicyResponse policy=new DataPolicyResponse();

    @JsonProperty("emissionUsername")
    private String emissionUsername;

    @JsonProperty("emissionPassword")
    private String emissionPassword;

    @JsonProperty("payment_frequency")
    private String payment_frequency;


    @JsonProperty("action")
    private String action;


    @JsonProperty("insuranceId")
    private String insuranceId;

    @JsonProperty("subscriptionId")
    private String subscriptionId;

    @JsonProperty("idTrans")
    private String idTrans;

    @JsonProperty("mac")
    private String mac;

    @JsonProperty("parentOrderId")
    private Integer parentOrderId;

    @JsonProperty("lastParentOrderId")
    private Integer lastParentOrderId;

    @JsonProperty("addons")
    private JsonNode addons;

    @JsonProperty("action")
    public String getAction() {
        return action;
    }

    @JsonProperty("action")
    public void setAction(String action) {
        this.action = action;
    }

    @JsonProperty("payment_frequency")
    public String getPayment_frequency() {
        return payment_frequency;
    }
    @JsonProperty("payment_frequency")
    public void setPayment_frequency(String payment_frequency) {
        this.payment_frequency = payment_frequency;
    }

    @JsonProperty("order")
    public JsonNode getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(JsonNode order) {
        this.order = order;
    }

    @JsonProperty("customer")
    public CustomerResponseDto getCustomer() {
        return customer;
    }

    @JsonProperty("customer")
    public void setCustomer(CustomerResponseDto customer) {
        this.customer = customer;
    }

    @JsonProperty("policy")
    public DataPolicyResponse getPolicy() {
        return policy;
    }

    @JsonProperty("policy")
    public void setPolicy(DataPolicyResponse policy) {
        this.policy = policy;
    }

    @JsonProperty("emissionUsername")
    public String getEmissionUsername() {
        return emissionUsername;
    }

    @JsonProperty("emissionUsername")
    public void setEmissionUsername(String emissionUsername) {
        this.emissionUsername = emissionUsername;
    }

    @JsonProperty("emissionPassword")
    public String getEmissionPassword() {
        return emissionPassword;
    }

    @JsonProperty("emissionPassword")
    public void setEmissionPassword(String emissionPassword) {
        this.emissionPassword = emissionPassword;
    }

    public String getInsuranceId() {
        return insuranceId;
    }

    public void setInsuranceId(String insuranceId) {
        this.insuranceId = insuranceId;
    }

    public JsonNode getAddons() {
        return addons;
    }

    public void setAddons(JsonNode addons) {
        this.addons = addons;
    }

    @JsonProperty("idTrans")
    public String getIdTrans() {
        return idTrans;
    }

    @JsonProperty("idTrans")
    public void setIdTrans(String idTrans) {
        this.idTrans = idTrans;
    }

    @JsonProperty("mac")
    public String getMac() {
        return mac;
    }

    @JsonProperty("mac")
    public void setMac(String mac) {
        this.mac = mac;
    }

    @JsonProperty("product")
    public JsonNode getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(JsonNode product) {
        this.product = product;
    }

    @JsonProperty("subscriptionId")
    public String getSubscriptionId() {
        return subscriptionId;
    }

    @JsonProperty("subscriptionId")
    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty("parentOrderId")
    public Integer getParentOrderId() {
        return parentOrderId;
    }

    @JsonProperty("parentOrderId")
    public void setParentOrderId(Integer parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    @JsonProperty("lastParentOrderId")
    public Integer getLastParentOrderId() {
        return lastParentOrderId;
    }

    @JsonProperty("lastParentOrderId")
    public void setLastParentOrderId(Integer lastParentOrderId) {
        this.lastParentOrderId = lastParentOrderId;
    }
}
