package it.yolo.client.yin;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

//orders/core/:coreApiKey/align/:orderNumber
@Path("api/orders/orders/core")
@RegisterRestClient(configKey = "yin-api")
public interface YinClient {

    @GET
    @Path("/{coreApiKey}/align/{orderNumber}/{version}")
    Response alignOder(@PathParam("coreApiKey") String coreApiKey, @PathParam("orderNumber") String orderNumber,@PathParam("version") String version);

}
