package it.yolo.client.yin;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

@Path("align")
@RegisterRestClient(configKey = "align-order")
public interface YinClientV2 {

    @GET
    @Path("{order_code}")
    Response alignOder(@PathParam("order_code") String orderCode);

}
