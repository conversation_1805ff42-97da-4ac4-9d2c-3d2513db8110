package it.yolo.client.iam;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/iam")
@RegisterRestClient(configKey = "iad-token")
public interface IamClient {

    @GET
    @Path("userInfo")
    Response findByToken(@RestHeader("Authorization") String token);

}
