package it.yolo.client.adp.pg.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.policy.dto.response.LoginResponseDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PgAdpDtoRequest implements Serializable {

    @JsonProperty("order")
    private OrderResponseDto orderRequestDto;

    @JsonProperty("customer")
    private CustomerResponseDto customerResponseDto;

    @JsonProperty("login")
    private LoginResponseDto login;

    @JsonProperty("policy")
    private PolicyResponseDto policyResponseDto;


    @JsonProperty("order")
    public OrderResponseDto getOrderRequestDto() {
        return orderRequestDto;
    }

    @JsonProperty("order")
    public void setOrderRequestDto(OrderResponseDto orderRequestDto) {
        this.orderRequestDto = orderRequestDto;
    }

    @JsonProperty("customer")
    public CustomerResponseDto getCustomerResponseDto() {
        return customerResponseDto;
    }

    @JsonProperty("customer")
    public void setCustomerResponseDto(CustomerResponseDto customerResponseDto) {
        this.customerResponseDto = customerResponseDto;
    }


    @JsonProperty("policy")
    public PolicyResponseDto getPolicyResponseDto() {
        return policyResponseDto;
    }

    @JsonProperty("policy")
    public void setPolicyResponseDto(PolicyResponseDto policyResponseDto) {
        this.policyResponseDto = policyResponseDto;
    }

    @JsonProperty("login")
    public LoginResponseDto getLogin() {
        return login;
    }

    @JsonProperty("login")
    public void setLogin(LoginResponseDto login) {
        this.login = login;
    }
}
