package it.yolo.client.adp.pg;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.adp.pg.dto.PgAdpDtoRequest;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Deprecated
@Path("/pg/estimation")
@RegisterRestClient(configKey = "adp-pg")
public interface AdpPgGateway {


    @POST
    @Path("/toPg")
    Response trasfomrRequest(EmissionRequestDto req);

    @POST
    @Path("/fromIad")
    Response trasfomrResponse(JsonNode req);


}
