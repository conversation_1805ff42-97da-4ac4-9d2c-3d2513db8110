package it.yolo.client.adp.customer;

import it.yolo.model.request.OrderManagerRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Deprecated
@Path("/customer")
@RegisterRestClient(configKey = "adp-customer")
public interface AdpCustomerClient {

    @POST
    @Path("/address")
    Response trasfomrRequest(OrderManagerRequest req);


}
