package it.yolo.client.adp.survey;

import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.model.request.OrderManagerRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Deprecated
@Path("/survey")
@RegisterRestClient(configKey = "adp-survey")
public interface AdpSurveyClient {


    @POST
    @Path("/fromIad")
    Response trasfomrRequest(OrderManagerRequest req);

    @POST
    @Path("/toIad")
    Response trasformResponse(OrderResponseDto orderResponseDto);

    @POST
    @Path("/fromIad")
    Response trasfomrRequestCustomer(OrderManagerRequest req);


}
