package it.yolo.client.adp.payment;

import it.yolo.client.order.dto.response.OrderResponseDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Deprecated
@Path("/payment")
@RegisterRestClient(configKey = "adp-payment")
public interface AdpPaymentClient {


    @POST
    @Path("/single")
    Response trasfomrRequest(@HeaderParam("payment-token") String payment_token,
                             OrderResponseDto req);


}
