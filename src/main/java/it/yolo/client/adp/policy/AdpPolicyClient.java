package it.yolo.client.adp.policy;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import com.fasterxml.jackson.databind.JsonNode;

@Deprecated
@Path("/policy")
@RegisterRestClient(configKey = "adp-policy")
public interface AdpPolicyClient {

    @POST
    @Path("/fromIad")
    Response trasfomrResponse(JsonNode req);
}
