package it.yolo.client.policy.v2;

import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.model.request.DuplicatePolicyRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-policy")
@Path("v2")
public interface PolicyClient {

    @POST
    @Path("policies")
    Response create(@RestHeader("Authorization") String token, PolicyRequestDto request);

    @GET
    @Path("policies/{id}/customer")
    Response get(@RestHeader("Authorization") String token, Integer id);

    @PUT
    @Path("policies/emission")
    Response updateAfterEmission(@RestHeader("Authorization") String token,PolicyRequestDto req);

    @GET
    @Path("code/generation/{product_code}")
    Response getNextPolicy(@RestHeader("Authorization") String token, @PathParam("product_code") String productCode);

    @GET
    @Path("states/state/{name}")
    Response getStateByName(@RestHeader("Authorization") String token, @PathParam("name") String name);

    @POST
    @Path("policies/fail-recurring/{subscriptionId}")
    Response updateNumberFailedPayment(@RestHeader("Authorization") String token, PolicyRequestDto request, String subscriptionId);

    @POST
    @Path("policies/success-recurring/{subscriptionId}")
    Response updateSuccessfulPayment(@RestHeader("Authorization") String token, PolicyRequestDto request, String subscriptionId);

    @GET
    @Path("policies/byCode/{code}/active")
    Response readPolicyByCode(@RestHeader("Authorization") String token, String code);
    @GET
    @Path("policies/byOrderId/{orderId}/active")
    Response readPolicyByOrderIdActive(@RestHeader("Authorization") String token, Long orderId);

    @GET
    @Path("policies/order/{orderId}")
    Response readPolicyByOrderId(@RestHeader("Authorization") String token, Integer orderId);

    @POST
    @Path("policies/saveByUpdatedWarranties")
    Response duplicatePolicy(@RestHeader("Authorization") String token, DuplicatePolicyRequest request);
}
