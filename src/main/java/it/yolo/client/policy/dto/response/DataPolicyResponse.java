package it.yolo.client.policy.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.math.BigDecimal;
import java.util.List;

public class DataPolicyResponse {
    @JsonProperty("id")
    private Long id;
    @JsonProperty("policyCode")
    private String policyCode;
    @JsonProperty("startDate")
    private String startDate;
    @JsonProperty("endDate")
    private String endDate;
    @JsonProperty("customer")
    private CustomerResponse customer;
    @JsonProperty("product")
    private ProductResponse product;
    @JsonProperty("orderId")
    private Long orderId;
    @JsonProperty("externalCode")
    private String externalCode;
    @JsonProperty("originalPolicyNumber")
    private String originalPolicyNumber;
    @JsonProperty("state")
    private AnagStateResponse state;
    @JsonProperty("payment")
    private PaymentReferenceResponse payment;
    @JsonProperty("choosenProperties")
    private JsonNode choosenProperties;
    @JsonProperty("insurancePremium")
    private BigDecimal insurancePremium;
    @JsonProperty("warranties")
    private List<PolicyWarrantiesResponse> warranties;
    @JsonProperty("additionalPolicyInfo")
    private JsonNode AdditionalPolicyInfo;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("quantity")
    private Long quantity;
    @JsonProperty("insuredIsContractor")
    private Boolean insuredIsContractor;
    @JsonProperty("certificateFileName")
    private String certificateFileName;
    @JsonProperty("certificateLink")
    private String certificateLink;
    @JsonProperty("certificateContentType")
    private String certificateContentType;
    @JsonProperty("certificateFileSize")
    private String certificateFileSize;
    @JsonProperty("certificateUpdatedAt")
    private String certificateUpdatedAt;
    @JsonProperty("certificate")
    private String certificate;
    @JsonProperty("type")
    private String type;
    @JsonProperty("withdrawalRequestDate")
    private String withdrawalRequestDate;
    @JsonProperty("renewedAt")
    private String renewedAt;
    @JsonProperty("markedAsRenewable")
    private Boolean markedAsRenewable;

    @JsonProperty("policySubstitution")
    private Boolean policySubstitution;
    @JsonProperty("masterPolicyNumber")
    private String masterPolicyNumber;

    @JsonProperty("nextBillingDate")
    private String nextBillingDate;
    @JsonProperty("name")
    private String name;
    @JsonProperty("username")
    private String username;
    @JsonProperty("password")
    private String password;
    @JsonProperty("isWithdrawable")
    private Boolean isWithdrawable;
    @JsonProperty("isDeactivable")
    private Boolean isDeactivable;
    @JsonProperty("subscriptionId")
    private String subscriptionId;
    @JsonProperty("id")
    public Long getId() {
        return id;
    }
    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }
    @JsonProperty("policyCode")
    public String getPolicyCode() {
        return policyCode;
    }
    @JsonProperty("policyCode")
    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }
    @JsonProperty("startDate")
    public String getStartDate() {
        return startDate;
    }
    @JsonProperty("startDate")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    @JsonProperty("endDate")
    public String getEndDate() {
        return endDate;
    }
    @JsonProperty("endDate")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    @JsonProperty("customer")
    public CustomerResponse getCustomer() {
        return customer;
    }
    @JsonProperty("customer")
    public void setCustomer(CustomerResponse customer) {
        this.customer = customer;
    }
    @JsonProperty("product")
    public ProductResponse getProduct() {
        return product;
    }
    @JsonProperty("product")
    public void setProduct(ProductResponse product) {
        this.product = product;
    }
    @JsonProperty("orderId")
    public Long getOrderId() {
        return orderId;
    }
    @JsonProperty("orderId")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public AnagStateResponse getState() {
        return state;
    }

    public void setState(AnagStateResponse state) {
        this.state = state;
    }

    public PaymentReferenceResponse getPayment() {
        return payment;
    }

    public void setPayment(PaymentReferenceResponse payment) {
        this.payment = payment;
    }

    public JsonNode getChoosenProperties() {
        return choosenProperties;
    }

    public void setChoosenProperties(JsonNode choosenProperties) {
        this.choosenProperties = choosenProperties;
    }

    public BigDecimal getInsurancePremium() {
        return insurancePremium;
    }

    public void setInsurancePremium(BigDecimal insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    public List<PolicyWarrantiesResponse> getWarranties() {
        return warranties;
    }

    public void setWarranties(List<PolicyWarrantiesResponse> warranties) {
        this.warranties = warranties;
    }

    public JsonNode getAdditionalPolicyInfo() {
        return AdditionalPolicyInfo;
    }

    public void setAdditionalPolicyInfo(JsonNode additionalPolicyInfo) {
        AdditionalPolicyInfo = additionalPolicyInfo;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Boolean getInsuredIsContractor() {
        return insuredIsContractor;
    }

    public void setInsuredIsContractor(Boolean insuredIsContractor) {
        this.insuredIsContractor = insuredIsContractor;
    }

    public String getCertificateFileName() {
        return certificateFileName;
    }

    public void setCertificateFileName(String certificateFileName) {
        this.certificateFileName = certificateFileName;
    }

    public String getCertificateLink() {
        return certificateLink;
    }

    public void setCertificateLink(String certificateLink) {
        this.certificateLink = certificateLink;
    }

    public String getCertificateContentType() {
        return certificateContentType;
    }

    public void setCertificateContentType(String certificateContentType) {
        this.certificateContentType = certificateContentType;
    }

    public String getCertificateFileSize() {
        return certificateFileSize;
    }

    public void setCertificateFileSize(String certificateFileSize) {
        this.certificateFileSize = certificateFileSize;
    }

    public String getCertificateUpdatedAt() {
        return certificateUpdatedAt;
    }

    public void setCertificateUpdatedAt(String certificateUpdatedAt) {
        this.certificateUpdatedAt = certificateUpdatedAt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getWithdrawalRequestDate() {
        return withdrawalRequestDate;
    }

    public void setWithdrawalRequestDate(String withdrawalRequestDate) {
        this.withdrawalRequestDate = withdrawalRequestDate;
    }

    public String getRenewedAt() {
        return renewedAt;
    }

    public void setRenewedAt(String renewedAt) {
        this.renewedAt = renewedAt;
    }

    public Boolean getMarkedAsRenewable() {
        return markedAsRenewable;
    }

    public void setMarkedAsRenewable(Boolean markedAsRenewable) {
        this.markedAsRenewable = markedAsRenewable;
    }

    public String getMasterPolicyNumber() {
        return masterPolicyNumber;
    }

    public void setMasterPolicyNumber(String masterPolicyNumber) {
        this.masterPolicyNumber = masterPolicyNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getWithdrawable() {
        return isWithdrawable;
    }

    public void setWithdrawable(Boolean withdrawable) {
        isWithdrawable = withdrawable;
    }

    public Boolean getDeactivable() {
        return isDeactivable;
    }

    public void setDeactivable(Boolean deactivable) {
        isDeactivable = deactivable;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    @JsonProperty("policySubstitution")
    public Boolean getPolicySubstitution() {
        return policySubstitution;
    }

    @JsonProperty("policySubstitution")
    public void setPolicySubstitution(Boolean policySubstitution) {
        this.policySubstitution = policySubstitution;
    }

    @JsonProperty("nextBillingDate")
    public String getNextBillingDate() {
        return nextBillingDate;
    }

    @JsonProperty("nextBillingDate")
    public void setNextBillingDate(String nextBillingDate) {
        this.nextBillingDate = nextBillingDate;
    }

    @JsonProperty("originalPolicyNumber")
    public String getOriginalPolicyNumber() {
        return originalPolicyNumber;
    }

    @JsonProperty("originalPolicyNumber")
    public void setOriginalPolicyNumber(String originalPolicyNumber) {
        this.originalPolicyNumber = originalPolicyNumber;
    }
}
