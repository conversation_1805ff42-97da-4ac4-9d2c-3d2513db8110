package it.yolo.client.policy.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AnagStateResponse {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("state")
    private String state;
    @JsonProperty("id")
    public Long getId() {
        return id;
    }
    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }
    @JsonProperty("state")
    public String getState() {
        return state;
    }
    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }
}
