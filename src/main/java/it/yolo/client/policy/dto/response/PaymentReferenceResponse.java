package it.yolo.client.policy.dto.response;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

public class PaymentReferenceResponse {


    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    private String paymentFrequency;

    @Getter
    @Setter
    private String paymentTrx;

    @Getter
    @Setter
    private String paymentToken;

    @Getter
    @Setter
    private List<?> paymentSources;


}
