package it.yolo.client.policy.dto.response;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

public class PolicyWarrantiesResponse {
    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Warranty")
    private WarrantyReferenceResponse warranty;

    @Getter
    @Setter
    @Schema(description = "Insurance premium")
    private BigDecimal insurancePremium;

    @Getter
    @Setter
    @Schema(description = "Whether this warranty is mandatory")
    private boolean mandatory;

    @Getter
    @Setter
    @Schema(description = "Ceiling")
    private BigDecimal ceiling;
}
