package it.yolo.client.policy.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PolicyRequestDto {
    @JsonProperty("data")
    private DataPolicyRequestDto dto;

    @JsonProperty("data")
    public DataPolicyRequestDto getDto() {
        return dto;
    }

    @JsonProperty("data")
    public void setDto(DataPolicyRequestDto dto) {
        this.dto = dto;
    }

    public PolicyRequestDto(DataPolicyRequestDto dto) {
        this.dto = dto;
    }

    public PolicyRequestDto() {
    }
}
