package it.yolo.client.policy.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.iam.dto.CustomerRequestPolicy;
import it.yolo.client.iam.dto.PacketRequestPolicy;
import it.yolo.client.iam.dto.ProductRequestPolicy;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataPolicyRequestDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("paymentMethod")
    private String paymentMethod;

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("orderId")
    private Long orderId;

    @JsonProperty("policyCode")
    private String policyCode;

    @JsonProperty("orderIdCode")
    private String orderIdCode;

    @JsonProperty("subscriptionId")
    private String subscriptionId;

    @JsonProperty("product")
    private ProductRequestPolicy product=new ProductRequestPolicy();

    @JsonProperty("packet")
    private PacketRequestPolicy packet=new PacketRequestPolicy();

    @JsonProperty("customer")
    private CustomerRequestPolicy customer=new CustomerRequestPolicy();

    @JsonProperty("startDate")
    private String startDate;

    @JsonProperty("endDate")
    private String endDate;

    @JsonProperty("stateId")
    private Long stateId;

    @JsonProperty("payment")
    private PolicyPaymentRequestDto payment=new PolicyPaymentRequestDto();

    @JsonProperty("payment_frequency")
    private String paymentFrequency;

    @JsonProperty("certificateLink")
    private String certificateLink;

    @JsonProperty("certificateFileName")
    private String certificateFileName;

    @JsonProperty("certificateContentType")
    private String certificateContentType;

    @JsonProperty("type")
    private String type;

    @JsonProperty("insurancePremium")
    private BigDecimal insurancePremium;

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("name")
    private String name;

    @JsonProperty("orderItemId")
    private Integer orderItemId;

    @JsonProperty("number_failed_payment")
    private Long numberFailedPayment;

    @JsonProperty("next_billing_date")
    private LocalDateTime nextBillingDate;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("policyCode")
    public String getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @JsonProperty("orderIdCode")
    public String getOrderIdCode() {
        return orderIdCode;
    }

    @JsonProperty("orderIdCode")
    public void setOrderIdCode(String orderIdCode) {
        this.orderIdCode = orderIdCode;
    }

    @JsonProperty("startDate")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("startDate")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public String getEndDate() {
        return endDate;
    }

    @JsonProperty("endDate")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("stateId")
    public Long getStateId() {
        return stateId;
    }

    @JsonProperty("stateId")
    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public ProductRequestPolicy getProduct() {
        return product;
    }

    public void setProduct(ProductRequestPolicy product) {
        this.product = product;
    }

    public PacketRequestPolicy getPacket() {
        return packet;
    }

    public void setPacket(PacketRequestPolicy packet) {
        this.packet = packet;
    }

    public CustomerRequestPolicy getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerRequestPolicy customer) {
        this.customer = customer;
    }

    @JsonProperty("payment")
    public PolicyPaymentRequestDto getPayment() {
        return payment;
    }

    @JsonProperty("payment_frequency")
    public String getPaymentFrequency() {
        return paymentFrequency;
    }

    @JsonProperty("payment_frequency")
    public void setPaymentFrequency(String paymentFrequency) {
        this.paymentFrequency = paymentFrequency;
    }

    @JsonProperty("payment")
    public void setPayment(PolicyPaymentRequestDto payment) {
        this.payment = payment;
    }

    @JsonProperty("certificateLink")
    public String getCertificateLink() {
        return certificateLink;
    }

    @JsonProperty("certificateLink")
    public void setCertificateLink(String certificateLink) {
        this.certificateLink = certificateLink;
    }

    @JsonProperty("certificateFileName")
    public String getCertificateFileName() {
        return certificateFileName;
    }

    @JsonProperty("certificateFileName")
    public void setCertificateFileName(String certificateFileName) {
        this.certificateFileName = certificateFileName;
    }


    @JsonProperty("certificateContentType")
    public String getCertificateContentType() {
        return certificateContentType;
    }

    @JsonProperty("certificateContentType")
    public void setCertificateContentType(String certificateContentType) {
        this.certificateContentType = certificateContentType;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("insurancePremium")
    public BigDecimal getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(BigDecimal insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    @JsonProperty("quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("subscriptionId")
    public String getSubscriptionId() {
        return subscriptionId;
    }

    @JsonProperty("subscriptionId")
    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty("orderItemId")
    public Integer getOrderItemId() {
        return this.orderItemId;
    }
    @JsonProperty("orderItemId")
    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @JsonProperty("number_failed_payment")
    public Long getNumberFailedPayment() {
        return numberFailedPayment;
    }

    @JsonProperty("number_failed_payment")
    public void setNumberFailedPayment(Long numberFailedPayment) {
        this.numberFailedPayment = numberFailedPayment;
    }

    @JsonProperty("next_billing_date")
    public LocalDateTime getNextBillingDate() {
        return nextBillingDate;
    }

    @JsonProperty("next_billing_date")
    public void setNextBillingDate(LocalDateTime nextBillingDate) {
        this.nextBillingDate = nextBillingDate;
    }

    @JsonProperty("paymentMethod")
    public String getPaymentMethod() {
        return paymentMethod;
    }

    @JsonProperty("paymentMethod")
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }


    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
}
