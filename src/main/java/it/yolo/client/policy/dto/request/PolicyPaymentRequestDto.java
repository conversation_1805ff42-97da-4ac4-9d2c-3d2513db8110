package it.yolo.client.policy.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PolicyPaymentRequestDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("paymentToken")
    private String paymentToken;


    @JsonProperty("paymentTrx")
    private Integer paymentTransactionId;

    @JsonProperty("paymentType")
    private String paymentType;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("paymentToken")
    public String getPaymentToken() {
        return paymentToken;
    }

    @JsonProperty("paymentToken")
    public void setPaymentToken(String paymentToken) {
        this.paymentToken = paymentToken;
    }

    @JsonProperty("paymentTrx")
    public Integer getPaymentTransactionId() {
        return paymentTransactionId;
    }

    @JsonProperty("paymentTrx")
    public void setPaymentTransactionId(Integer paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }

    @JsonProperty("paymentType")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("paymentType")
    public void setPaymentType(String paymentFrequency) {
        this.paymentType = paymentFrequency;
    }
}
