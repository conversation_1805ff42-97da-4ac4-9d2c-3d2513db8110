package it.yolo.client.policy;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

@RegisterRestClient(configKey = "iad-policy")
@Path("v1")
public interface PolicyClient {

    @POST
    @Path("policies")
    Response create(@RestHeader("Authorization") String token, PolicyRequestDto request);

    @POST
    @Path("actions")
    Response action(ObjectNode request);

    @GET
    @Path("policies/{id}/customer")
    Response get(@RestHeader("Authorization") String token, Integer id);

    @PUT
    @Path("policies/emission")
    Response updateAfterEmission(@RestHeader("Authorization") String token,PolicyRequestDto req);

    @GET
    @Path("code/generation/{product_code}")
    Response getNextPolicy(@RestHeader("Authorization") String token, @PathParam("product_code") String productCode);

    @GET
    @Path("states/state/{name}")
    Response getStateByName(@RestHeader("Authorization") String token, @PathParam("name") String name);

    @GET
    @Path("policies/order/{orderId}")
    Response getOrderId(@RestHeader("Authorization") String token, @PathParam("orderId") Integer id);
}
