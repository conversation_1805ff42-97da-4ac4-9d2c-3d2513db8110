package it.yolo.client.packet;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/packets")
@RegisterRestClient(configKey = "iad-product")
public interface PacketClient {

    @GET
    @Path("{id}")
    Response findById(@RestHeader("Authorization") String token, Integer id);
}
