package it.yolo.client.packet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.dto.iad.response.DataPacket;

public class PacketDtoResponse {


    @JsonProperty("data")
    private DataPacket dataPacket;

    @JsonProperty("data")
    public DataPacket getDataPacket() {
        return dataPacket;
    }

    @JsonProperty("data")
    public void setDataPacket(DataPacket dataPacket) {
        this.dataPacket = dataPacket;
    }
}
