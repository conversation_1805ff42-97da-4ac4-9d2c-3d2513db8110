package it.yolo.client.payment;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

@Path("/v1")
@RegisterRestClient(configKey = "payment-stripe")
public interface StripeClient {

    @GET
    @Path("get-subscription/subscription/{orderCode}")
    Response findSubcriptionByOrder(@RestHeader("Authorization") String token, @PathParam("orderCode") String orderCode);
}
