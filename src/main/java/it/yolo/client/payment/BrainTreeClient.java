package it.yolo.client.payment;

import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/braintree")
@RegisterRestClient(configKey = "brain-tree")
public interface BrainTreeClient {

    @POST
    @Path("/payment/single")
    Response paymentSingle(@RestHeader("Authorization") String token, JsonNode request);

    @POST
    @Path("/payment/recurring")
    Response paymentRecurring(@RestHeader("Authorization") String token, JsonNode request);
}
