package it.yolo.client.payment;

import it.yolo.client.order.dto.response.OrderCodesListResponse;
import it.yolo.client.payment.dto.PaymentManagerListRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/payment")
@RegisterRestClient(configKey = "payment-manager")
public interface PaymentManagerClient {

    @POST
    @Path("get-transactions/payment-manager")
    Response getPayments(@RestHeader("Authorization") String token, PaymentManagerListRequest request);
}
