package it.yolo.client.payment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class PaymentManagerListRequest {

    @JsonProperty("data")
    private PaymentManagerListDto data;

    @JsonProperty("data")
    public PaymentManagerListDto getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(PaymentManagerListDto data) {
        this.data = data;
    }

    public PaymentManagerListRequest(List<String> orderCodes) {
        data=new PaymentManagerListDto(orderCodes);
    }

    public PaymentManagerListRequest() {
    }
}
