package it.yolo.client.payment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

public class ProviderPaymentRequestDto {

    @JsonProperty("request")
    private JsonNode request;

    @JsonProperty("bearerToken")
    private String bearerToken;

    @JsonProperty("orcerCode")
    private String orcerCode;

    @JsonProperty("amount")
    private Integer amount;

    @JsonProperty("payment_token")
    private String payment_token;

    @JsonProperty("token")
    private String token;


    @JsonProperty("device_data")
    private String device_data;

    @JsonProperty("order_id")
    private Integer order_id;

    @JsonProperty("type")
    private String type;

    @JsonProperty("request")
    public JsonNode getRequest() {
        return request;
    }

    @JsonProperty("request")
    public void setRequest(JsonNode request) {
        this.request = request;
    }

    @JsonProperty("bearerToken")
    public String getBearerToken() {
        return bearerToken;
    }

    @JsonProperty("bearerToken")
    public void setBearerToken(String bearerToken) {
        this.bearerToken = bearerToken;
    }

    @JsonProperty("amount")
    public Integer getAmount() {
        return amount;
    }

    @JsonProperty("amount")
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    @JsonProperty("payment_token")
    public String getPayment_token() {
        return payment_token;
    }

    @JsonProperty("payment_token")
    public void setPayment_token(String payment_token) {
        this.payment_token = payment_token;
    }

    @JsonProperty("device_data")
    public String getDevice_data() {
        return device_data;
    }

    @JsonProperty("device_data")
    public void setDevice_data(String device_data) {
        this.device_data = device_data;
    }

    @JsonProperty("order_id")
    public Integer getOrder_id() {
        return order_id;
    }

    @JsonProperty("order_id")
    public void setOrder_id(Integer order_id) {
        this.order_id = order_id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @JsonProperty("frequency")
    public String getType() {
        return type;
    }

    @JsonProperty("frequency")
    public void setType(String frequecy) {
        this.type = frequecy;
    }

    @JsonProperty("orcerCode")
    public String getOrcerCode() {
        return orcerCode;
    }

    @JsonProperty("orcerCode")
    public void setOrcerCode(String orcerCode) {
        this.orcerCode = orcerCode;
    }
}
