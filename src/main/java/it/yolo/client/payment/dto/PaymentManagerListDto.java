package it.yolo.client.payment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class PaymentManagerListDto {

    @JsonProperty("orderCodes")
    private List<String> orderCodes;

    @JsonProperty("orderCodes")
    public List<String> getOrderCodes() {
        return orderCodes;
    }

    @JsonProperty("orderCodes")
    public void setOrderCodes(List<String> orderCodes) {
        this.orderCodes = orderCodes;
    }

    public PaymentManagerListDto(List<String> orderCodes) {
        this.orderCodes = orderCodes;
    }

    public PaymentManagerListDto() {
    }
}
