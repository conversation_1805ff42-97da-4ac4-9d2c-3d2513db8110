package it.yolo.client.payment;

import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/no-payment")
@RegisterRestClient(configKey = "no-pay")
public interface NoPayClient {

    @PUT
    @Path("/checkouts/{id}")
    Response payment(@RestHeader("Authorization") String token, JsonNode request, String id);

}
