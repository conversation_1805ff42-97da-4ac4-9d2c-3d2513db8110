package it.yolo.client.dto.legacy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Addons {


    /*
    messo come stringa , perchè nel campo legacy e un Long , invece nella risp del pg id corrisponde ad una stringa.
    per non creare due dto diversi per gli addons ho unificato il campo come stringa
     */
    @JsonProperty("id")
    private String id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("commission")
    private Double commission;

    @JsonProperty("maximal")
    private Double maximal;

    @JsonProperty("net")
    private Double net;

    @JsonProperty("price")
    private Double price;

    @JsonProperty("selected")
    private Boolean selected;

    @JsonProperty("tax")
    private Double tax;

    @JsonProperty("taxable")
    private Double taxable;

    @JsonProperty("description")
    private String description;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getCommission() {
        return commission;
    }

    public void setCommission(Double commission) {
        this.commission = commission;
    }

    public Double getMaximal() {
        return maximal;
    }

    public void setMaximal(Double maximal) {
        this.maximal = maximal;
    }

    public Double getNet() {
        return net;
    }

    public void setNet(Double net) {
        this.net = net;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getTaxable() {
        return taxable;
    }

    public void setTaxable(Double taxable) {
        this.taxable = taxable;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
