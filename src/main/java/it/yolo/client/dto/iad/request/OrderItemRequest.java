package it.yolo.client.dto.iad.request;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "price",
        "packet_id",
        "policy_number",
        "master_policy_number",
        "external_id",
        "state",
        "start_date",
        "expiration_date",
        "insured_item"
})
public class OrderItemRequest {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("price")
    private String price;
    @JsonProperty("packet_id")
    private Integer packetId;
    @JsonProperty("product_id")
    private Integer productId;
    @JsonProperty("policy_number")
    private Object policyNumber;
    @JsonProperty("master_policy_number")
    private Integer masterPolicyNumber;
    @JsonProperty("external_id")
    private Integer externalId;
    @JsonProperty("state")
    private String state;
    @JsonProperty("start_date")
    private LocalDateTime startDate;
    @JsonProperty("expiration_date")
    private LocalDateTime expirationDate;
    @JsonProperty("insured_item")
    private Object insuredItem;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("price")
    public String getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(String price) {
        this.price = price;
    }

    @JsonProperty("packet_id")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packet_id")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("product_id")
    public Integer getProductId() {
        return productId;
    }

    @JsonProperty("product_id")
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @JsonProperty("policy_number")
    public Object getPolicyNumber() {
        return policyNumber;
    }

    @JsonProperty("policy_number")
    public void setPolicyNumber(Object policyNumber) {
        this.policyNumber = policyNumber;
    }

    @JsonProperty("master_policy_number")
    public Integer getMasterPolicyNumber() {
        return masterPolicyNumber;
    }

    @JsonProperty("master_policy_number")
    public void setMasterPolicyNumber(Integer masterPolicyNumber) {
        this.masterPolicyNumber = masterPolicyNumber;
    }

    @JsonProperty("external_id")
    public Integer getExternalId() {
        return externalId;
    }

    @JsonProperty("external_id")
    public void setExternalId(Integer externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    @JsonProperty("start_date")
    public LocalDateTime getStartDate() {
        return startDate;
    }

    @JsonProperty("start_date")
    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("expiration_date")
    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    @JsonProperty("expiration_date")
    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    @JsonProperty("insured_item")
    public Object getInsuredItem() {
        return insuredItem;
    }

    @JsonProperty("insured_item")
    public void setInsuredItem(Object insuredItem) {
        this.insuredItem = insuredItem;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
