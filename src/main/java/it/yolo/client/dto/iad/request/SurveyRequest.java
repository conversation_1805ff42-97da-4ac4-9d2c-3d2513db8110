package it.yolo.client.dto.iad.request;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyRequest {


    @JsonProperty("questionId")
    private Integer questionId;

    @JsonProperty("answerId")
    private Integer answerId;

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("orderItemId")
    private String orderItemId;


    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("orderItemId")
    public String getOrderItemId() {
        return orderItemId;
    }

    @JsonProperty("orderItemId")
    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    @JsonProperty("questionId")
    public Integer getQuestionId() {
        return questionId;
    }

    @JsonProperty("questionId")
    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    @JsonProperty("answerId")
    public Integer getAnswerId() {
        return answerId;
    }

    @JsonProperty("answerId")
    public void setAnswerId(Integer answerId) {
        this.answerId = answerId;
    }
}
