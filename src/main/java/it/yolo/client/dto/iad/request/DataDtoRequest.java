package it.yolo.client.dto.iad.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;

public class DataDtoRequest {


    @JsonProperty("customerId")
    private Integer customerId;
    @JsonProperty("orderCode")
    private String orderCode;
    @JsonProperty("policyCode")
    private String policyCode;

    @JsonProperty("country")
    private String country;

    @JsonProperty("birth_country")
    private String birth_country;

    @JsonProperty("birth_state")
    private String birth_state;

    @JsonProperty("birth_state_abbr")
    private String birth_state_abbr;

    @JsonProperty("state")
    private String state;

    @JsonProperty("state_abbr")
    private String state_abbr;



    @JsonProperty("city")
    private String city;

    @JsonProperty("anagStatesId")
    private Integer anagStatesId;
    @JsonProperty("packetId")
    private Integer packetId;
    @JsonProperty("productId")
    private Integer productId;
    @JsonProperty("brokerId")
    private Integer brokerId;
    @JsonProperty("asset")
    private JsonNode asset;
    @JsonProperty("companyId")
    private Integer companyId;
    @JsonProperty("insuredItem")
    private JsonNode insuredItem;
    @JsonProperty("insurancePremium")
    private Integer insurancePremium;
    @JsonProperty("createdBy")
    private String createdBy;
    @JsonProperty("updatedBy")
    private String updatedBy;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("customer_code")
    private String customerCode;
    @JsonProperty("username")
    private String username;
    @JsonProperty("name")
    private String name;
    @JsonProperty("surname")
    private String surname;

    @JsonProperty("state_id")
    private String state_id;

    @JsonProperty("birth_city_id")
    private String birth_city_id;

    @JsonProperty("birth_state_id")
    private String birth_state_id;

    @JsonProperty("birth_country_id")
    private String birth_country_id;

    @JsonProperty("date_of_birth")
    private String date_of_birth;


    @JsonProperty("country_id")
    private String country_id;

    @JsonProperty("birth_city")
    private String birthCity;
    @JsonProperty("birth_province")
    private String birthProvince;
    @JsonProperty("tax_code")
    private String taxCode;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("street")
    private String street;
    @JsonProperty("street_number")
    private String streetNumber;

    @JsonProperty("zip_code")
    private String zipCode;
    @JsonProperty("province")
    private String province;
    @JsonProperty("primary_mail")
    private String primaryMail;
    @JsonProperty("secondary_mail")
    private String secondaryMail;
    @JsonProperty("primary_phone")
    private String primaryPhone;
    @JsonProperty("secondary_phone")
    private String secondaryPhone;
    @JsonProperty("language")
    private String language;
    @JsonProperty("legal_form")
    private String legalForm;

    @JsonProperty("orderItem")
    private List<OrderItemRequest> orderItem;


    @JsonProperty("questionId")
    private Integer questionId;

    @JsonProperty("answerId")
    private Integer answerId;


    @JsonProperty("orderItemId")
    private String orderItemId;

    @JsonProperty("company")
    private String company;

    @JsonProperty("orderItem")
    public List<OrderItemRequest> getOrderItem() {
        return orderItem;
    }

    @JsonProperty("orderItem")
    public void setOrderItem(List<OrderItemRequest> orderItem) {
        this.orderItem = orderItem;
    }

    @JsonProperty("customerId")
    public Integer getCustomerId() {
        return customerId;
    }

    @JsonProperty("customerId")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }


    @JsonProperty("asset")
    public JsonNode getAsset() {
        return asset;
    }

    @JsonProperty("asset")
    public void setAsset(JsonNode asset) {
        this.asset = asset;
    }


    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("policyCode")
    public String getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @JsonProperty("anagStatesId")
    public Integer getAnagStatesId() {
        return anagStatesId;
    }

    @JsonProperty("anagStatesId")
    public void setAnagStatesId(Integer anagStatesId) {
        this.anagStatesId = anagStatesId;
    }

    @JsonProperty("packetId")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("productId")
    public Integer getProductId() {
        return productId;
    }

    @JsonProperty("productId")
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @JsonProperty("brokerId")
    public Integer getBrokerId() {
        return brokerId;
    }

    @JsonProperty("brokerId")
    public void setBrokerId(Integer brokerId) {
        this.brokerId = brokerId;
    }

    @JsonProperty("companyId")
    public Integer getCompanyId() {
        return companyId;
    }

    @JsonProperty("companyId")
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @JsonProperty("insuredItem")
    public JsonNode getInsuredItem() {
        return insuredItem;
    }

    @JsonProperty("insuredItem")
    public void setInsuredItem(JsonNode insuredItem) {
        this.insuredItem = insuredItem;
    }

    @JsonProperty("insurancePremium")
    public Integer getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(Integer insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    @JsonProperty("createdBy")
    public String getCreatedBy() {
        return createdBy;
    }

    @JsonProperty("createdBy")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @JsonProperty("updatedBy")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @JsonProperty("updatedBy")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @JsonProperty("createdAt")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("createdAt")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updatedAt")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updatedAt")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }


    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("customer_code")
    public String getCustomerCode() {
        return customerCode;
    }

    @JsonProperty("customer_code")
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    @JsonProperty("username")
    public String getUsername() {
        return username;
    }

    @JsonProperty("username")
    public void setUsername(String username) {
        this.username = username;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("surname")
    public String getSurname() {
        return surname;
    }

    @JsonProperty("surname")
    public void setSurname(String surname) {
        this.surname = surname;
    }


    @JsonProperty("birth_city")
    public String getBirthCity() {
        return birthCity;
    }

    @JsonProperty("birth_city")
    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    @JsonProperty("birth_province")
    public String getBirthProvince() {
        return birthProvince;
    }

    @JsonProperty("birth_province")
    public void setBirthProvince(String birthProvince) {
        this.birthProvince = birthProvince;
    }

    @JsonProperty("tax_code")
    public String getTaxCode() {
        return taxCode;
    }

    @JsonProperty("tax_code")
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    @JsonProperty("gender")
    public String getGender() {
        return gender;
    }

    @JsonProperty("gender")
    public void setGender(String gender) {
        this.gender = gender;
    }

    @JsonProperty("street")
    public String getStreet() {
        return street;
    }

    @JsonProperty("street")
    public void setStreet(String street) {
        this.street = street;
    }

    @JsonProperty("street_number")
    public String getStreetNumber() {
        return streetNumber;
    }

    @JsonProperty("street_number")
    public void setStreetNumber(String streetNumber) {
        this.streetNumber = streetNumber;
    }

    @JsonProperty("city")
    public String getCity() {
        return city;
    }

    @JsonProperty("city")
    public void setCity(String city) {
        this.city = city;
    }

    @JsonProperty("country")
    public String getCountry() {
        return country;
    }

    @JsonProperty("country")
    public void setCountry(String country) {
        this.country = country;
    }

    @JsonProperty("zip_code")
    public String getZipCode() {
        return zipCode;
    }

    @JsonProperty("zip_code")
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    @JsonProperty("province")
    public String getProvince() {
        return province;
    }

    @JsonProperty("province")
    public void setProvince(String province) {
        this.province = province;
    }

    @JsonProperty("primary_mail")
    public String getPrimaryMail() {
        return primaryMail;
    }

    @JsonProperty("primary_mail")
    public void setPrimaryMail(String primaryMail) {
        this.primaryMail = primaryMail;
    }

    @JsonProperty("secondary_mail")
    public String getSecondaryMail() {
        return secondaryMail;
    }

    @JsonProperty("secondary_mail")
    public void setSecondaryMail(String secondaryMail) {
        this.secondaryMail = secondaryMail;
    }

    @JsonProperty("primary_phone")
    public String getPrimaryPhone() {
        return primaryPhone;
    }

    @JsonProperty("primary_phone")
    public void setPrimaryPhone(String primaryPhone) {
        this.primaryPhone = primaryPhone;
    }

    @JsonProperty("secondary_phone")
    public String getSecondaryPhone() {
        return secondaryPhone;
    }

    @JsonProperty("secondary_phone")
    public void setSecondaryPhone(String secondaryPhone) {
        this.secondaryPhone = secondaryPhone;
    }

    @JsonProperty("language")
    public String getLanguage() {
        return language;
    }

    @JsonProperty("language")
    public void setLanguage(String language) {
        this.language = language;
    }

    @JsonProperty("legal_form")
    public String getLegalForm() {
        return legalForm;
    }

    @JsonProperty("legal_form")
    public void setLegalForm(String legalForm) {
        this.legalForm = legalForm;
    }


    @JsonProperty("orderItemId")
    public String getOrderItemId() {
        return orderItemId;
    }

    @JsonProperty("orderItemId")
    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    @JsonProperty("questionId")
    public Integer getQuestionId() {
        return questionId;
    }

    @JsonProperty("questionId")
    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    @JsonProperty("answerId")
    public Integer getAnswerId() {
        return answerId;
    }

    @JsonProperty("answerId")
    public void setAnswerId(Integer answerId) {
        this.answerId = answerId;
    }

    @JsonProperty("state_id")
    public String getState_id() {
        return state_id;
    }

    @JsonProperty("state_id")
    public void setState_id(String state_id) {
        this.state_id = state_id;
    }

    @JsonProperty("birth_state_id")
    public String getBirth_state_id() {
        return birth_state_id;
    }
    @JsonProperty("birth_state_id")
    public void setBirth_state_id(String birth_state_id) {
        this.birth_state_id = birth_state_id;
    }

    @JsonProperty("birth_country_id")
    public String getBirth_country_id() {
        return birth_country_id;
    }

    @JsonProperty("birth_country_id")
    public void setBirth_country_id(String birth_country_id) {
        this.birth_country_id = birth_country_id;
    }

    @JsonProperty("birth_city_id")
    public String getBirth_city_id() {
        return birth_city_id;
    }

    @JsonProperty("birth_city_id")
    public void setBirth_city_id(String birth_city_id) {
        this.birth_city_id = birth_city_id;
    }

    @JsonProperty("country_id")
    public String getCountry_id() {
        return country_id;
    }

    @JsonProperty("country_id")
    public void setCountry_id(String country_id) {
        this.country_id = country_id;
    }

    public String getBirth_country() {
        return birth_country;
    }

    public void setBirth_country(String birth_country) {
        this.birth_country = birth_country;
    }

    public String getBirth_state() {
        return birth_state;
    }

    public void setBirth_state(String birth_state) {
        this.birth_state = birth_state;
    }

    public String getBirth_state_abbr() {
        return birth_state_abbr;
    }

    public void setBirth_state_abbr(String birth_state_abbr) {
        this.birth_state_abbr = birth_state_abbr;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState_abbr() {
        return state_abbr;
    }

    public void setState_abbr(String state_abbr) {
        this.state_abbr = state_abbr;
    }

    public String getDate_of_birth() {
        return date_of_birth;
    }

    public void setDate_of_birth(String date_of_birth) {
        this.date_of_birth = date_of_birth;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }
}
