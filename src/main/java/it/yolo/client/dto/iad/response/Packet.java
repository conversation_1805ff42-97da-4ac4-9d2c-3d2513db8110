package it.yolo.client.dto.iad.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "dataPacket"
})
@Generated("jsonschema2pojo")
public class Packet {

    @JsonProperty("data")
    private DataPacket dataPacket;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("data")
    public DataPacket getDataPacket() {
        return dataPacket;
    }

    @JsonProperty("data")
    public void setDataPacket(DataPacket dataPacket) {
        this.dataPacket = dataPacket;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
