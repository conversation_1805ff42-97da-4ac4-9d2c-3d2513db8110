package it.yolo.client.product;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-product")
@Path("/v3/products")
public interface ProductClient {

    @GET
    @Path("{id}")
    Response findById(@RestHeader("Authorization") String token, @RestHeader("x-tenant-language") String xtl, Long id);

}
