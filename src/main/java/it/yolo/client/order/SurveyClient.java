package it.yolo.client.order;

import it.yolo.client.order.dto.request.AnswerDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1/answers")
@RegisterRestClient(configKey = "iad-survey")
public interface SurveyClient {

    @POST
    Response create(@RestHeader("Authorization") String token, AnswerDto clientRequestDto);
}
