package it.yolo.client.order.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnswerDto {

    @JsonProperty("data")
    private List<DataDtoRequest> dataDtoRequests;

    @JsonProperty("data")
    public List<DataDtoRequest> getDataDtoRequests() {
        return dataDtoRequests;
    }

    @JsonProperty("data")
    public void setDataDtoRequests(List<DataDtoRequest> dataDtoRequests) {
        this.dataDtoRequests = dataDtoRequests;
    }
}
