package it.yolo.client.order.dto.response;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "name",
        "broker",
        "product",
        "warranties",
        "sku",
        "business",
        "imagesPacket",
        "short_description",
        "conditions",
        "information_package",
        "conditions_package",
        "display_price",
        "price",
        "only_contractor",
        "maximum_insurable",
        "can_open_claim",
        "holder_maximum_age",
        "holder_minimum_age",
        "show_in_dashboard"
})
public class DataPacketOrderResponse {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("broker")
    private Broker broker;

    @JsonProperty("duration")
    private String duration;
    @JsonProperty("fixedEndDate")
    private LocalDateTime fixedEndDate;

    @JsonProperty("product")
    private DataProductOrderResponse product;
    @JsonProperty("warranties")
    private List<Object> warranties = null;
    @JsonProperty("sku")
    private String sku;
    @JsonProperty("business")
    private Boolean business;
    @JsonProperty("images")
    private ImagesPacket imagesPacket;
    @JsonProperty("short_description")
    private String shortDescription;

    @JsonProperty("description")
    private String description;
    @JsonProperty("conditions")
    private String conditions;
    @JsonProperty("information_package")
    private String informationPackage;
    @JsonProperty("conditions_package")
    private String conditionsPackage;
    @JsonProperty("display_price")
    private String displayPrice;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("packetPremium")
    private Double packetPremium;

    @JsonProperty("only_contractor")
    private Boolean onlyContractor;
    @JsonProperty("maximum_insurable")
    private Integer maximumInsurable;
    @JsonProperty("can_open_claim")
    private Boolean canOpenClaim;
    @JsonProperty("holder_maximum_age")
    private Integer holderMaximumAge;
    @JsonProperty("holder_minimum_age")
    private Integer holderMinimumAge;
    @JsonProperty("show_in_dashboard")
    private Boolean showInDashboard;
    @JsonProperty("configuration")
    private JsonNode configuration;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("broker")
    public Broker getBroker() {
        return broker;
    }

    @JsonProperty("broker")
    public void setBroker(Broker broker) {
        this.broker = broker;
    }

    @JsonProperty("product")
    public DataProductOrderResponse getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(DataProductOrderResponse product) {
        this.product = product;
    }

    @JsonProperty("warranties")
    public List<Object> getWarranties() {
        return warranties;
    }

    @JsonProperty("warranties")
    public void setWarranties(List<Object> warranties) {
        this.warranties = warranties;
    }

    @JsonProperty("sku")
    public String getSku() {
        return sku;
    }

    @JsonProperty("sku")
    public void setSku(String sku) {
        this.sku = sku;
    }

    @JsonProperty("business")
    public Boolean getBusiness() {
        return business;
    }

    @JsonProperty("business")
    public void setBusiness(Boolean business) {
        this.business = business;
    }

    @JsonProperty("imagesPacket")
    public ImagesPacket getImagesPacket() {
        return imagesPacket;
    }

    @JsonProperty("imagesPacket")
    public void setImagesPacket(ImagesPacket imagesPacket) {
        this.imagesPacket = imagesPacket;
    }

    @JsonProperty("short_description")
    public String getShortDescription() {
        return shortDescription;
    }

    @JsonProperty("short_description")
    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    @JsonProperty("conditions")
    public String getConditions() {
        return conditions;
    }

    @JsonProperty("conditions")
    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    @JsonProperty("information_package")
    public String getInformationPackage() {
        return informationPackage;
    }

    @JsonProperty("information_package")
    public void setInformationPackage(String informationPackage) {
        this.informationPackage = informationPackage;
    }

    @JsonProperty("conditions_package")
    public String getConditionsPackage() {
        return conditionsPackage;
    }

    @JsonProperty("conditions_package")
    public void setConditionsPackage(String conditionsPackage) {
        this.conditionsPackage = conditionsPackage;
    }

    @JsonProperty("display_price")
    public String getDisplayPrice() {
        return displayPrice;
    }

    @JsonProperty("display_price")
    public void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("packetPremium")
    public Double getPacketPremium() {
        return packetPremium;
    }

    @JsonProperty("packetPremium")
    public void setPacketPremium(Double packetPremium) {
        this.packetPremium = packetPremium;
    }

    @JsonProperty("only_contractor")
    public Boolean getOnlyContractor() {
        return onlyContractor;
    }

    @JsonProperty("only_contractor")
    public void setOnlyContractor(Boolean onlyContractor) {
        this.onlyContractor = onlyContractor;
    }

    @JsonProperty("maximum_insurable")
    public Integer getMaximumInsurable() {
        return maximumInsurable;
    }

    @JsonProperty("maximum_insurable")
    public void setMaximumInsurable(Integer maximumInsurable) {
        this.maximumInsurable = maximumInsurable;
    }

    @JsonProperty("can_open_claim")
    public Boolean getCanOpenClaim() {
        return canOpenClaim;
    }

    @JsonProperty("can_open_claim")
    public void setCanOpenClaim(Boolean canOpenClaim) {
        this.canOpenClaim = canOpenClaim;
    }

    @JsonProperty("holder_maximum_age")
    public Integer getHolderMaximumAge() {
        return holderMaximumAge;
    }

    @JsonProperty("holder_maximum_age")
    public void setHolderMaximumAge(Integer holderMaximumAge) {
        this.holderMaximumAge = holderMaximumAge;
    }

    @JsonProperty("holder_minimum_age")
    public Integer getHolderMinimumAge() {
        return holderMinimumAge;
    }

    @JsonProperty("holder_minimum_age")
    public void setHolderMinimumAge(Integer holderMinimumAge) {
        this.holderMinimumAge = holderMinimumAge;
    }

    @JsonProperty("show_in_dashboard")
    public Boolean getShowInDashboard() {
        return showInDashboard;
    }

    @JsonProperty("show_in_dashboard")
    public void setShowInDashboard(Boolean showInDashboard) {
        this.showInDashboard = showInDashboard;
    }

    @JsonProperty("fixedEndDate")
    public LocalDateTime getFixedEndDate() {
        return fixedEndDate;
    }

    @JsonProperty("fixedEndDate")
    public void setFixedEndDate(LocalDateTime fixedEndDate) {
        this.fixedEndDate = fixedEndDate;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("duration")
    public String getDuration() {
        return duration;
    }

    @JsonProperty("duration")
    public void setDuration(String duration) {
        this.duration = duration;
    }

    @JsonProperty("configuration")
    public JsonNode getConfiguration() {
        return configuration;
    }

    @JsonProperty("configuration")
    public void setConfiguration(JsonNode configuration) {
        this.configuration = configuration;
    }
}
