package it.yolo.client.order.dto.request;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "data"
})
public class OrderRequestDto {

    @JsonProperty("data")
    private DataDtoRequest dataDtoRequest=new DataDtoRequest();

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("data")
    public DataDtoRequest getDataDtoRequest() {
        return dataDtoRequest;
    }

    @JsonProperty("data")
    public void setDataDtoRequest(DataDtoRequest dataDtoRequest) {
        this.dataDtoRequest = dataDtoRequest;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }


}
