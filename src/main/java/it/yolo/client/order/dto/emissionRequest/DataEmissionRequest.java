package it.yolo.client.order.dto.emissionRequest;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataEmissionRequest {
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("orderCode")
    private String orderCode;
    @JsonProperty("emission")
    private JsonNode Emission;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("emission")
    public JsonNode getEmission() {
        return Emission;
    }

    @JsonProperty("emission")
    public void setEmission(JsonNode emission) {
        Emission = emission;
    }
}
