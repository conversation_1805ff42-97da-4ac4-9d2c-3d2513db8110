package it.yolo.client.order.dto.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonPropertyOrder({
        "dataPacket"
})
@Generated("jsonschema2pojo")
public class Packet {

    @JsonProperty("data")
    private DataPacketOrderResponse dataPacketOrderResponse =new DataPacketOrderResponse();
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("data")
    public DataPacketOrderResponse getDataPacket() {
        return dataPacketOrderResponse;
    }

    @JsonProperty("data")
    public void setDataPacket(DataPacketOrderResponse dataPacketOrderResponse) {
        this.dataPacketOrderResponse = dataPacketOrderResponse;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
