package it.yolo.client.order.dto.response;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.Map;

@JsonPropertyOrder({
        "id",
        "price",
        "packet_id",
        "policy_number",
        "master_policy_number",
        "external_id",
        "state",
        "start_date",
        "expiration_date",
        "insured_item",
        "emission"
})
public class OrderItem {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("price")
    private String price;
    @JsonProperty("packet_id")
    private Integer packetId;
    @JsonProperty("product_id")
    private Integer productId;
    @JsonProperty("policy_number")
    private Object policyNumber;
    @JsonProperty("master_policy_number")
    private Integer masterPolicyNumber;
    @JsonProperty("external_id")
    private Integer externalId;
    @JsonProperty("quantity")
    private Integer quantity;
    @JsonProperty("state")
    private String state;
    @JsonProperty("start_date")
    private String startDate;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("expiration_date")
    private String expirationDate;
    @JsonProperty("insured_item")
    private JsonNode insuredItem;
    @JsonProperty("instance")
    private JsonNode instance;
    @JsonProperty("quotation")
    public JsonNode quotation;
    @JsonProperty("emission")
    public JsonNode emission;



    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("price")
    public String getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(String price) {
        this.price = price;
    }

    @JsonProperty("packet_id")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packet_id")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("product_id")
    public Integer getProductId() {
        return productId;
    }

    @JsonProperty("product_id")
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @JsonProperty("policy_number")
    public Object getPolicyNumber() {
        return policyNumber;
    }

    @JsonProperty("policy_number")
    public void setPolicyNumber(Object policyNumber) {
        this.policyNumber = policyNumber;
    }

    @JsonProperty("master_policy_number")
    public Integer getMasterPolicyNumber() {
        return masterPolicyNumber;
    }

    @JsonProperty("master_policy_number")
    public void setMasterPolicyNumber(Integer masterPolicyNumber) {
        this.masterPolicyNumber = masterPolicyNumber;
    }

    @JsonProperty("external_id")
    public Integer getExternalId() {
        return externalId;
    }

    @JsonProperty("external_id")
    public void setExternalId(Integer externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    @JsonProperty("start_date")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("start_date")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("expiration_date")
    public String getExpirationDate() {
        return expirationDate;
    }

    @JsonProperty("expiration_date")
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("insured_item")
    public JsonNode getInsuredItem() {
        return insuredItem;
    }

    @JsonProperty("instance")
    public JsonNode getInstance() {
        return instance;
    }

    @JsonProperty("instance")
    public void setInstance(JsonNode instance) {
        this.instance = instance;
    }

    @JsonProperty("insured_item")
    public void setInsuredItem(JsonNode insuredItem) {
        this.insuredItem = insuredItem;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("quotation")
    public JsonNode getQuotation() {
        return quotation;
    }

    @JsonProperty("quotation")
    public void setQuotation(JsonNode quotation) {
        this.quotation = quotation;
    }

    @JsonProperty("emission")
    public JsonNode getEmission() {
        return emission;
    }

    @JsonProperty("emission")
    public void setEmission(JsonNode emission) {
        this.emission = emission;
    }
}
