package it.yolo.client.order.dto.emissionRequest;

import com.fasterxml.jackson.annotation.*;
import it.yolo.client.order.dto.request.DataDtoRequest;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "data"
})
public class EmissionRequest {
    @JsonProperty("data")
    private DataEmissionRequest dataEmissionRequest;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("data")
    public DataEmissionRequest getDataEmissionRequest() {
        return dataEmissionRequest;
    }

    @JsonProperty("data")
    public void setDataEmissionRequest(DataEmissionRequest dataEmissionRequest) {
        this.dataEmissionRequest = dataEmissionRequest;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }
}
