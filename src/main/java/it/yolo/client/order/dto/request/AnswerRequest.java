package it.yolo.client.order.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnswerRequest {

    @JsonProperty("id")
    private int idSurvey;

    @JsonProperty("questionId")
    private int questionId;


    @JsonProperty("id")
    public int getIdSurvey() {
        return idSurvey;
    }

    @JsonProperty("id")
    public void setIdSurvey(int idSurvey) {
        this.idSurvey = idSurvey;
    }

    @JsonProperty("questionId")
    public int getQuestionId() {
        return questionId;
    }

    @JsonProperty("questionId")
    public void setQuestionId(int questionId) {
        this.questionId = questionId;
    }


}
