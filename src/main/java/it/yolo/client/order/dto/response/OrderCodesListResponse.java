package it.yolo.client.order.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class OrderCodesListResponse {

    @JsonProperty("data")
    private List<String> codes;

    @JsonProperty("data")
    public List<String> getCodes() {
        return codes;
    }

    @JsonProperty("data")
    public void setCodes(List<String> codes) {
        this.codes = codes;
    }
}
