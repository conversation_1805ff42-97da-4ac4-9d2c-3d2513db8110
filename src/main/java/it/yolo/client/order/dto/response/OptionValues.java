package it.yolo.client.order.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OptionValues {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("presentation")
    private String presentation;

    @JsonProperty("duration")
    private int duration;

    @JsonProperty("option_type_id")
    private int option_type_id;

    @JsonProperty("option_type_name")
    private String option_type_name;

    @JsonProperty("option_type_name")
    public String getOption_type_name() {
        return option_type_name;
    }

    @JsonProperty("option_type_name")
    public void setOption_type_name(String option_type_name) {
        this.option_type_name = option_type_name;
    }

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("presentation")
    public String getPresentation() {
        return presentation;
    }

    @JsonProperty("presentation")
    public void setPresentation(String presentation) {
        this.presentation = presentation;
    }

    @JsonProperty("duration")
    public int getDuration() {
        return duration;
    }

    @JsonProperty("duration")
    public void setDuration(int duration) {
        this.duration = duration;
    }

    @JsonProperty("option_type_id")
    public int getOption_type_id() {
        return option_type_id;
    }

    @JsonProperty("option_type_id")
    public void setOption_type_id(int option_type_id) {
        this.option_type_id = option_type_id;
    }
}
