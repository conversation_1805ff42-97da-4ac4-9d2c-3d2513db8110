package it.yolo.client.order.v2;

import it.yolo.client.order.dto.request.OrderRequestDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v2/order")
@RegisterRestClient(configKey = "iad-order")
public interface OrderClientV2 {

    @POST
    Response create(@RestHeader("Authorization") String token, OrderRequestDto orderRequest);

    @PUT
    @Path("{id}")
    Response update(@RestHeader("Authorization") String token, String id, OrderRequestDto orderRequest);

}
