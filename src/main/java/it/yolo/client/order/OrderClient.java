package it.yolo.client.order;

import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import it.yolo.client.order.dto.emissionRequest.EmissionRequest;
import it.yolo.client.order.dto.request.OrderRequestDto;
import it.yolo.exception.EmissionFlowException;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-order")
@Path("/v3")
public interface OrderClient {

    @PUT
    @Path("order/failed")
    Response failed(@RestHeader("Authorization") String token, @QueryParam("orderCode") String orderCode);

    @PUT
    @Path("order/confirmed")
    Response confirmed(@RestHeader("Authorization") String token, @QueryParam("order_code") String orderCode);

    @POST
    @Path("order")
    Response create(@RestHeader("Authorization") String token, OrderRequestDto orderRequest);

    @PUT
    @Path("order/{code}")
    Response update(@RestHeader("Authorization") String token, OrderRequestDto orderRequest, String code);

    @PUT
    @Path("order/unchecked/{code}")
    Response uncheckedUpdate(@RestHeader("Authorization") String token, OrderRequestDto orderRequest, String code);

    @GET
    @Path("/code/{order_code}")
    Response findByOrderCode(@RestHeader("Authorization") String token, String order_code);
    @GET
    @Path("order/unchecked/code/{orderCode}")
    Response findByOrderCodeUnchecked(@RestHeader("Authorization") String token, @PathParam("orderCode") String orderCode);

    @GET
    @Path("order/{id}")
    Response findById(@RestHeader("Authorization") String token, Long id);

    @GET
    @Path("order/unchecked/{id}")
    Response findByIdUnchecked(@RestHeader("Authorization") String token, Long id);

    @GET
    @Path("order/{customerId}/{orderCode}")
    Response findByOrderCodeAndIdCustomer(@RestHeader("Authorization") String token, int customerId, String orderCode);

    @GET
    @Path("order/{customerId}/customer")
    Response getByCustomerId(@RestHeader("Authorization") String token, @PathParam("customerId") Long customerId);

    @GET
    @Path("order/details/order/{orderCode}")
    Response getdetailsOrder(@RestHeader("Authorization") String token, String orderCode);

    @GET
    @Path("code/{orderCode}")
    Response getOrder(@RestHeader("Authorization") String token, String orderCode);

    @GET
    @Path("/order/unchecked/code/{orderCode}")
    Response getOrderUnchecked(@RestHeader("Authorization") String token, String orderCode);

    @POST
    @Path("order/emission")
    Response updateEmissionDetail(@RestHeader("Authorization") String token, EmissionRequest orderRequest);
    @POST
    @Path("order/duplicated-order")
    Response duplicatedOrder(@RestHeader("Authorization") String token, OrderRequestDto request);

    @GET
    @Path("order/start/{orderCode}")
    Response updateStartDate(@RestHeader("Authorization") String token, String orderCode);

    @GET
    @Path("/order/checkExistingPolicy/{orderCode}")
    Response checkExistingPolicy(String orderCode, @RestHeader("Authorization") String token);

    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 400 || response.getStatus() == 401 || response.getStatus() == 403 || response.getStatus() == 404
                ||response.getStatus() == 500 || response.getStatus() == 502 || response.getStatus() == 503) {
            return new EmissionFlowException("Errore chiamata order", "Status response order: "+response.getStatus());
        }
        return null;
    }
}
