package it.yolo.client.documentManager;

import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-document-manager")
@Path("v1/document-manager")
public interface DocumentManagerClient {
    @POST
    @Path("docs")
    Response addDocument(@RestHeader("Authorization") String token, @QueryParam("insuranceId") String insuranceId,
                         JsonNode request);

}
