package it.yolo.client.pg.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Parameters {

    @JsonProperty("insurance_id")
    private String insuranceId;

    @JsonProperty("headers")
    private Headers headers;

    @JsonProperty("insurance_id")
    public String getInsuranceId() {
        return insuranceId;
    }

    @JsonProperty("insurance_id")
    public void setInsuranceId(String insuranceId) {
        this.insuranceId = insuranceId;
    }

    @JsonProperty("headers")
    public Headers getHeaders() {
        return headers;
    }

    @JsonProperty("headers")
    public void setHeaders(Headers headers) {
        this.headers = headers;
    }
}
