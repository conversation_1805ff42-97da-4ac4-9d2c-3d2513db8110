package it.yolo.client.pg.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AdditionalData {

    @JsonProperty("policyNumber")
    private String policyNumber;

    @JsonProperty("certificateBase64")
    private String certificate;

    @JsonProperty("policyNumber")
    public String getPolicyNumber() {
        return policyNumber;
    }

    @JsonProperty("policyNumber")
    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    @JsonProperty("certificateBase64")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificateBase64")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }
}
