package it.yolo.client.pg.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PgResponseDto {

    @JsonProperty("insuranceId")
    private String insuranceId;

    @JsonProperty("parameters")
    private Parameters parameters;

    @JsonProperty("certificate")
    private String certificate;

    @JsonProperty("additional_data")
    private JsonNode additionalData;


    @JsonProperty("insuranceId")
    public String getInsuranceId() {
        return insuranceId;
    }

    @JsonProperty("insuranceId")
    public void setInsuranceId(String insuranceId) {
        this.insuranceId = insuranceId;
    }

    @JsonProperty("certificate")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificate")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    @JsonProperty("parameters")
    public Parameters getParameters() {
        return parameters;
    }

    @JsonProperty("parameters")
    public void setParameters(Parameters parameters) {
        this.parameters = parameters;
    }

    @JsonProperty("additional_data")
    public JsonNode getAdditionalData() {
        return additionalData;
    }

    @JsonProperty("additional_data")
    public void setAdditionalData(JsonNode additionalData) {
        this.additionalData = additionalData;
    }
}
