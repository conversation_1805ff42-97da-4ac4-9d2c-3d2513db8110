package it.yolo.client.pg.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Headers {

    @JsonProperty("x_partner")
    private String xPartner;

    @JsonProperty("authorization")
    private String authorization;

    @JsonProperty("x_partner")
    public String getxPartner() {
        return xPartner;
    }

    @JsonProperty("x_partner")
    public void setxPartner(String xPartner) {
        this.xPartner = xPartner;
    }

    @JsonProperty("authorization")
    public String getAuthorization() {
        return authorization;
    }

    @JsonProperty("authorization")
    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }
}
