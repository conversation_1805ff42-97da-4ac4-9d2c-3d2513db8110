package it.yolo.client.pg.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ProductData {

    @JsonProperty("partnerToken")
    private String partnerToken;

    @JsonProperty("userToken")
    private String userToken;

    @JsonProperty("documentId")
    private String documentId;

    @JsonProperty("partnerToken")
    public String getPartnerToken() {
        return partnerToken;
    }

    @JsonProperty("partnerToken")
    public void setPartnerToken(String partnerToken) {
        this.partnerToken = partnerToken;
    }

    @JsonProperty("userToken")
    public String getUserToken() {
        return userToken;
    }

    @JsonProperty("userToken")
    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    @JsonProperty("documentId")
    public String getDocumentId() {
        return documentId;
    }

    @JsonProperty("documentId")
    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }
}
