package it.yolo.client.pg.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.emission.dto.request.CertificateRequestDto;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificateRequestV2 {

    @JsonProperty("data")
    CertificateRequestDto data;

    @JsonProperty("data")
    public CertificateRequestDto getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(CertificateRequestDto data) {
        this.data = data;
    }
}
