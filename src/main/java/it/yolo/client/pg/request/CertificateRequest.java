package it.yolo.client.pg.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CertificateRequest {

    @JsonProperty("token")
    private String token;

    @JsonProperty("tenant")
    private String tenant;

    @JsonProperty("product_code")
    private String productCode;

    @JsonProperty("product_data")
    private ProductData productData=new ProductData();

    @JsonProperty("token")
    public String getToken() {
        return token;
    }

    @JsonProperty("token")
    public void setToken(String token) {
        this.token = token;
    }

    @JsonProperty("tenant")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("tenant")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    @JsonProperty("product_code")
    public String getProductCode() {
        return productCode;
    }

    @JsonProperty("product_code")
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    @JsonProperty("product_data")
    public ProductData getProductData() {
        return productData;
    }

    @JsonProperty("product_data")
    public void setProductData(ProductData productData) {
        this.productData = productData;
    }
}
