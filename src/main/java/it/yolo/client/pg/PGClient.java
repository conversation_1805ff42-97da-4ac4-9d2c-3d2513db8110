package it.yolo.client.pg;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.pg.request.CertificateRequest;
import it.yolo.client.pg.request.CertificateRequestV2;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestV2;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;
@Path("/")
@RegisterRestClient(configKey = "provider-gateway")
public interface PGClient {

    @POST
    @Path("v2/emission")
    Response emission(JsonNode request);

    @POST
    @Path("v2/info")
    Response getCertificate(CertificateRequest request);

    @POST
    @Path("v2/emission/callback")
    Response callback(EmissionRequestV2 request);

    @POST
    @Path("v2/receipt")
    Response receipt(EmissionRequestV2 request);

    @POST
    @Path("v2/certificate")
    Response certificateV2(CertificateRequestDto certificateRequest);

    // Usato per italiana-casa-famiglia
    @POST
    @Path("/v1/certificate")
    Response certificate(CertificateRequestV2 certificateRequestDto);
}
