package it.yolo.client.pricing;

import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v2/quote")
@RegisterRestClient(configKey = "iad-pricing")
public interface PricingClient {

//    @PUT
    @POST
    Response quote(@RestHeader("Authorization") String token, JsonNode request);

}
