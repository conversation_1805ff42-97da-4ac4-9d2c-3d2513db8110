package it.yolo.client.document.dto;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Schema(description = "State entity")
public class DownloadDocumentResponse {

    @Getter
    @Setter
    @Schema(description = "FILE")
    private String file;

    @Getter
    @Setter
    @Schema(description = "file name")
    private String fileName;

}
