package it.yolo.client.document;

import it.yolo.client.document.dto.DownloadDocumentRequest;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.UploadDtoRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-document")
public interface DocumentClient {

    @POST
    @Path("/upload")
    Response uploadDocument(UploadDtoRequest file);

    @POST
    @Path("/generate")
    Response generateCertficate(CertificateRequestDto certificateRequestDto);

    @POST
    @Path("/downloadLink")
    Response downloadLink(DownloadDocumentRequest certificateRequestDto);

    @POST
    @Path("/download")
    Response download(DownloadDocumentRequest certificateRequestDto);
}
