package it.yolo.client.orderManager;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import it.yolo.exception.EmissionFlowException;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-order-manager")
@Path("/v1/order-manager/")
public interface OrderManagerClient {

    @PUT
    @Path("{code}")
    Response confirm(@RestHeader("Authorization") String token, @PathParam("code") String orderCode, JsonNode request);


    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 400 || response.getStatus() == 401 || response.getStatus() == 403 || response.getStatus() == 404
                ||response.getStatus() == 500 || response.getStatus() == 502 || response.getStatus() == 503) {
            return new EmissionFlowException("Errore chiamata order manager", "Status response order manager: "+response.getStatus());
        }
        return null;
    }
}