package it.yolo.client.communicationManager.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunicationManagerDtoRequest {

    @JsonProperty("attachments")
    private List<Attachments> attachments= new ArrayList<>();

    @JsonProperty("message")
    private Message message=new Message();

    @JsonProperty("options")
    private Options options=new Options();

    @JsonProperty("attachments")
    public List<Attachments> getAttachments() {
        return attachments;
    }

    @JsonProperty("attachments")
    public void setAttachments(List<Attachments> attachments) {
        this.attachments = attachments;
    }

    @JsonProperty("message")
    public Message getMessage() {
        return message;
    }

    @JsonProperty("message")
    public void setMessage(Message message) {
        this.message = message;
    }

    @JsonProperty("options")
    public Options getOptions() {
        return options;
    }

    @JsonProperty("options")
    public void setOptions(Options options) {
        this.options = options;
    }
}
