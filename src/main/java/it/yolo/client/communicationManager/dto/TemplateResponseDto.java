package it.yolo.client.communicationManager.dto;

import java.util.ArrayList;
import java.util.List;

public class TemplateResponseDto {

    private List<Attachments> attachments = new ArrayList<>();

    private Message message=new Message();

    private Options options=new Options();

    public List<Attachments> getAttachment() {
        return attachments;
    }

    public void setAttachment(List<Attachments> attachments) {
        this.attachments = attachments;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public Options getOptions() {
        return options;
    }

    public void setOptions(Options options) {
        this.options = options;
    }
}
