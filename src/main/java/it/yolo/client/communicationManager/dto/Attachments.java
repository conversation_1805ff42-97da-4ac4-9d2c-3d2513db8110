package it.yolo.client.communicationManager.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Attachments {

    private String filename;
    private String contenttype;
    private String body;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getContenttype() {
        return contenttype;
    }

    public void setContenttype(String contenttype) {
        this.contenttype = contenttype;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Attachments(String filename, String contenttype, String body) {
        this.filename = filename;
        this.contenttype = contenttype;
        this.body = body;
    }

    public Attachments() {
    }
}
