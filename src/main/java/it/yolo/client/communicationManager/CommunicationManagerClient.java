package it.yolo.client.communicationManager;

import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "communication-manager")
@Path("v1/comunication")
public interface CommunicationManagerClient {

    @POST
    @Path("/email")
    Response sendEmail(@RestHeader("Authorization") String token, @RestHeader("x-tenant-language") String language, CommunicationManagerDtoRequest req);

}
