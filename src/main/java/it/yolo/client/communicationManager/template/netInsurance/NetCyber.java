package it.yolo.client.communicationManager.template.netInsurance;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class NetCyber implements Template {

    private static String CERTIFICATO_FILE_NAME = "CERTIFICATO";

    private static String CONTENT_TYPE_PDF = "application/pdf";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest){

        TemplateResponseDto responseDto = new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("it_IT");
        responseDto.getOptions().setMessaggetype("html");
        responseDto.getOptions().setName(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        responseDto.getOptions().setSurname(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());
        if (templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").has("feUrl")) {
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("frontEndUrl", templateRequest
                    .getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").get("feUrl").asText()));
        }
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("productName", templateRequest.getEmissionRequestDto().getProduct().get("data").get("description").asText()));
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("policyNumber", templateRequest.getEmissionRequestDto().getPolicy().getPolicyCode()));
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("startDate", templateRequest.getEmissionRequestDto().getPolicy().getStartDate()));
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("endDate", templateRequest.getEmissionRequestDto().getPolicy().getEndDate()));
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("orderNumber", templateRequest.getEmissionRequestDto().getOrder().getResponse().getOrderCode()));

        List<Attachments> attachmentsList = new ArrayList<>();
        attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME + "." + templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));

        responseDto.getMessage().setKey("EMISSIONE_POLIZZA");
        responseDto.setAttachment(attachmentsList);
        return responseDto;
    }


}
