package it.yolo.client.communicationManager.template.verti;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@RequestScoped
public class VertiMotor implements Template {

        private static String CONTENT_TYPE_PDF="application/pdf";

        private static String SET_INFORMATIVO_FILE_NAME ="SET_INFORMATIVO_NET.pdf";
    
        private static String CERTIFICATO_FILE_NAME ="CERTIFICATO";
    
        @Override
        public TemplateResponseDto generate(TemplateRequest templateRequest) {
    
    
            TemplateResponseDto responseDto=new TemplateResponseDto();
            responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
            responseDto.getOptions().setMessaggetype("html");
            responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
            responseDto.getOptions().setName(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
            responseDto.getOptions().setSurname(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());
            if(templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").has("feUrl")){
                responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("frontEndUrl", templateRequest
                        .getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").get("feUrl").asText()));
            }
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("productName", templateRequest
                    .getEmissionRequestDto().getProduct().get("data").get("description").asText()));
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("policyNumber", templateRequest
                    .getEmissionRequestDto().getPolicy().getPolicyCode()));
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("startDate", templateRequest
                    .getEmissionRequestDto().getPolicy().getStartDate()));
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("endDate", templateRequest
                    .getEmissionRequestDto().getPolicy().getEndDate()));
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder("orderNumber", templateRequest
                    .getEmissionRequestDto().getOrder().getResponse().getOrderCode()));
        //     List<Attachments> attachmentsList=new ArrayList<>();
        //     attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME+"."+templateRequest.getCertificateResponseDto().getType(),
        //             templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
        //                     templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));
        //     URL url= null;
        //     try {
        //         url = new URL(templateRequest.getEmissionRequestDto().getProduct().get("data")
        //                 .get("informativeSet").asText());
        //     } catch (MalformedURLException e) {
        //         e.printStackTrace();
        //     }
        //     ByteArrayOutputStream out = new ByteArrayOutputStream();
        //     try (InputStream in = new BufferedInputStream(url.openStream())) {
        //         in.transferTo(Base64.getEncoder().wrap(out));
        //     } catch (IOException e) {
        //         e.printStackTrace();
        //     }
        //     String base64 = out.toString();
        //     attachmentsList.add(new Attachments(SET_INFORMATIVO_FILE_NAME, CONTENT_TYPE_PDF, base64));
        //     responseDto.setAttachment(attachmentsList);
             responseDto.getMessage().setKey("PAGAMENTO_RIUSCITO");
            return responseDto;
        }
    }
    