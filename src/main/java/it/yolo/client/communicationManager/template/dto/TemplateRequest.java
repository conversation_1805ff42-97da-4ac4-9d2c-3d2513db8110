package it.yolo.client.communicationManager.template.dto;

import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;

public class TemplateRequest {
    private EmissionRequestDto emissionRequestDto;
    private EmissionResponseDto emissionResponseDto;
    private CertificateResponseDto certificateResponseDto;
    public EmissionRequestDto getEmissionRequestDto() {
        return emissionRequestDto;
    }

    public void setEmissionRequestDto(EmissionRequestDto emissionRequestDto) {
        this.emissionRequestDto = emissionRequestDto;
    }

    public EmissionResponseDto getEmissionResponseDto() {
        return emissionResponseDto;
    }

    public void setEmissionResponseDto(EmissionResponseDto emissionResponseDto) {
        this.emissionResponseDto = emissionResponseDto;
    }

    public CertificateResponseDto getCertificateResponseDto() {
        return certificateResponseDto;
    }

    public void setCertificateResponseDto(CertificateResponseDto certificateResponseDto) {
        this.certificateResponseDto = certificateResponseDto;
    }
}
