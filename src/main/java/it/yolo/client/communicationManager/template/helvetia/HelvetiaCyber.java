package it.yolo.client.communicationManager.template.helvetia;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.common.Base64Utils;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class HelvetiaCyber implements Template {

        @Inject
        Logger log;
        private static String CERTIFICATO_FILE_NAME = "Modulo di Polizza";
        private static String ALLEGATO_3_4_4TER = "Modulo unico precontrattuale.pdf";
        private static String SET_INFORMATIVO = "Set_Informativo.pdf";
        private static String CONTENT_TYPE_PDF = "application/pdf";
        private static String CONTENT_TYPE_PDF_UTF8="application/pdf;charset=UTF-8";
        @Override
        public TemplateResponseDto generate(TemplateRequest templateRequest) {
                TemplateResponseDto responseDto = new TemplateResponseDto();
                responseDto.getOptions()
                                .setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData()
                                                .getPrimaryMail());
                responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
                responseDto.getOptions().setMessaggetype("html");
                responseDto.getMessage().setKey("HELVETIA_ATTIVAZIONE_STANDARD");
                List<Attachments> attachmentsList = new ArrayList<>();

                attachmentsList.add(
                                new Attachments(CERTIFICATO_FILE_NAME + "."
                                                + templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getType().contains("pdf")
                                                                ? CONTENT_TYPE_PDF
                                                                : templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getFile()));

//                informativaToBase64(attachmentsList, templateRequest.getEmissionRequestDto().getOrder());
                JsonNode product=templateRequest.getEmissionRequestDto().getProduct();
                if(product.get("data").get("configuration").get("properties").hasNonNull("emission") &&
                        product.get("data").get("configuration").get("properties").get("emission").hasNonNull("cc") &&
                ! product.get("data").get("configuration").get("properties").get("emission").withArray("cc").isEmpty()){
                        List<String> cc=new ArrayList<>();
                        product.get("data").get("configuration").get("properties").get("emission").withArray("cc").forEach(m->{
                                cc.add(m.asText());
                        });
                        responseDto.getOptions().setCc(cc);
                }
                Base64Utils.addAttachment(attachmentsList, product.get("data").get("attachment3_4").asText(), ALLEGATO_3_4_4TER, CONTENT_TYPE_PDF_UTF8);
                Base64Utils.addAttachment(attachmentsList, product.get("data").get("informativeSet").asText(), SET_INFORMATIVO, CONTENT_TYPE_PDF);
                responseDto.setAttachment(attachmentsList);
                TemplatePlaceholder placeholderPolicy = new TemplatePlaceholder(), placeholderCustomer=new TemplatePlaceholder(),
                placeholderProduct=new TemplatePlaceholder();
                responseDto.getOptions().setTemplatePlaceholder(new ArrayList<>());
                placeholderCustomer.setKey("customer_name");
                placeholderCustomer.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
                responseDto.getOptions().getTemplatePlaceholder().add(placeholderCustomer);
                placeholderProduct.setKey("product_name");
                placeholderProduct.setValue(templateRequest.getEmissionRequestDto().getProduct().get("data").get("productDescription").asText());
                responseDto.getOptions().getTemplatePlaceholder().add(placeholderProduct);
                placeholderPolicy.setKey("policy_code");
                placeholderPolicy.setValue(templateRequest.getEmissionResponseDto().getEmission().get("policyNumber").asText());
                responseDto.getOptions().getTemplatePlaceholder().add(placeholderPolicy);
                return responseDto;
        }



}