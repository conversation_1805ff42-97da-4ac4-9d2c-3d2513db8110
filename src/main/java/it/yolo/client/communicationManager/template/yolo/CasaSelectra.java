package it.yolo.client.communicationManager.template.yolo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;

@RequestScoped
public class CasaSelectra implements Template {

    private static final String DATA = "data";
    private static final String ORDER_ITEM = "orderItem";
    private static final String INSURED_ITEM = "insured_item";

    @Override
    public TemplateResponseDto generate(TemplateRequest request) {
        TemplateResponseDto response = new TemplateResponseDto();
        JsonNode order = new ObjectMapper().convertValue(request.getEmissionRequestDto().getOrder(), JsonNode.class);

        response.getOptions().setToMail(request.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        response.getOptions().setMessaggetype("html");
        response.getOptions().setLanguage(request.getEmissionRequestDto().getOrder().getResponse().getLanguage());
        response.getMessage().setKey("YOLO_SELECTRA_EMISSIONE");
        response.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "contractNumber",
                request.getEmissionRequestDto().getPolicy().getPolicyCode()
        ));
        response.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "productType",
                request.getEmissionRequestDto().getProduct().get(DATA).get("productDescription").asText()
        ));
        response.getOptions().getTemplatePlaceholder().add(getHomeAddress(order));
        response.getOptions().getTemplatePlaceholder().add(getPrice(order));
        response.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "frontEndUrl",
                request.getEmissionRequestDto().getProduct().get(DATA).get("configuration").get("properties").get("feUrl").asText()
        ));
        response.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "questionnaireUrl",
                request.getEmissionRequestDto().getProduct().get(DATA).get("configuration").get("properties").get("questionnaireUrl").asText()
        ));
        return response;
    }

    private TemplatePlaceholder getHomeAddress(JsonNode order) {
        return new TemplatePlaceholder(
                "homeAddress",
                String.format("%s, %s %s, %s",
                        order.get(DATA).get(ORDER_ITEM).get(0).get(INSURED_ITEM).get("indirizzo").asText(),
                        order.get(DATA).get(ORDER_ITEM).get(0).get(INSURED_ITEM).get("comune").asText(),
                        order.get(DATA).get(ORDER_ITEM).get(0).get(INSURED_ITEM).get("provincia").asText(),
                        order.get(DATA).get(ORDER_ITEM).get(0).get(INSURED_ITEM).get("cap").asText()
                )
        );
    }

    private TemplatePlaceholder getPrice(JsonNode order) {
        return new TemplatePlaceholder(
                "price",
                order.get(DATA).get(ORDER_ITEM).get(0).get("price").asText()
        );
    }
}
