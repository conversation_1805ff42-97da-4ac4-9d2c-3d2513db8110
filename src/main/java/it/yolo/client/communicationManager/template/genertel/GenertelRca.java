package it.yolo.client.communicationManager.template.genertel;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class GenertelRca implements Template {
    private Map<String, String> mapProductToMessageKey=new HashMap<>();
    private static String CONTENT_TYPE_PDF="application/pdf";
    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        Attachments attachments =new Attachments();
        attachments.setFilename(templateRequest.getCertificateResponseDto().getNomeFile());

        if(templateRequest.getCertificateResponseDto().getType().contains("pdf")){
            attachments.setContenttype(CONTENT_TYPE_PDF);
        }else{
            attachments.setContenttype(templateRequest.getCertificateResponseDto().getType());
        }
        attachments.setBody(templateRequest.getCertificateResponseDto().getFile());
        responseDto.setAttachment(Collections.singletonList(attachments));
        responseDto.getMessage().setKey("RCAUTO_CLASSIC_ATTIVAZIONE");
        return responseDto;
    }
}
