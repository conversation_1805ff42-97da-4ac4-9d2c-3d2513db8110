package it.yolo.client.communicationManager.template.generali;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import javax.enterprise.context.RequestScoped;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RequestScoped
public class TimSport implements Template {
        private static String CERTIFICATO_FILE_NAME = "CERTIFICATO";

        private static String CONTENT_TYPE_PDF = "application/pdf";

        @Override
        public TemplateResponseDto generate(TemplateRequest templateRequest) {
                TemplateResponseDto responseDto = new TemplateResponseDto();
                responseDto.getOptions()
                                .setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData()
                                                .getPrimaryMail());
                responseDto.getOptions().setLanguage("ita");
                responseDto.getOptions().setMessaggetype("html");
                responseDto.getMessage().setKey("TIM_ATTIVAZIONE_STANDARD");
                List<Attachments> attachmentsList = new ArrayList<>();

                attachmentsList.add(
                                new Attachments(CERTIFICATO_FILE_NAME + "."
                                                + templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getType().contains("pdf")
                                                                ? CONTENT_TYPE_PDF
                                                                : templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getFile()));

//                informativaToBase64(attachmentsList, templateRequest.getEmissionRequestDto().getOrder());

                responseDto.setAttachment(attachmentsList);
                TemplatePlaceholder placeholder = new TemplatePlaceholder();
                placeholder.setKey("nome");
                placeholder.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
                responseDto.getOptions().setTemplatePlaceholder(Collections.singletonList(placeholder));
                return responseDto;
        }

//        private void informativaToBase64(List<Attachments> attachmentsList, OrderResponseDto order) {
//                try {
//                        String urlFromorder = order.getResponse().getOrderItem().get(0).getInstance()
//                                .get("chosenWarranties").get("data").get("informativa")
//                                .asText();
//
//                        URL url = new URL(urlFromorder);
//                        String[] informativaNames = url.toString().split("/");
//
//                        InputStream in = new BufferedInputStream(url.openStream());
//                        ByteArrayOutputStream out = new ByteArrayOutputStream();
//                        in.transferTo(Base64.getEncoder().wrap(out));
//
//                        attachmentsList.add(new Attachments(
//                                        informativaNames[informativaNames.length - 1],
//                                        CONTENT_TYPE_PDF,
//                                        out.toString()));
//                } catch (Exception e) {
//                        e.printStackTrace();
//                }
//        }

}