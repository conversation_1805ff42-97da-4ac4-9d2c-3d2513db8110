package it.yolo.client.communicationManager.template.arag;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.order.dto.response.DataOrderResponseDto;
import it.yolo.common.Utility;

import javax.enterprise.context.RequestScoped;
import java.time.LocalDateTime;

@RequestScoped
public class AragTutelaLegaleKoPayment implements Template {


    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto = new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setMessaggetype("html");
        responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
        responseDto.getOptions().setName(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        responseDto.getOptions().setSurname(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());

        // Name placeholder
        TemplatePlaceholder name = new TemplatePlaceholder();
        name.setKey("name");
        name.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());

        // Product name placeholder
        TemplatePlaceholder productName = new TemplatePlaceholder();
        productName.setKey("productName");
        productName.setValue(templateRequest.getEmissionRequestDto().getOrder().getResponse().getProduct().getDataProduct().getDescription());

        // Policy number placeholder
        TemplatePlaceholder policyNumber = new TemplatePlaceholder();
        policyNumber.setKey("policyNumber");

        DataOrderResponseDto order = templateRequest.getEmissionRequestDto().getOrder().getResponse();
        JsonNode instance = order.getOrderItem().get(0).getInstance();
        String policyCode = null;
        if(instance.hasNonNull("paymentFrequency")) {
            String paymentFrequency = instance.get("paymentFrequency").asText();
            policyCode = order.getPacket().getDataPacket().getConfiguration().get(paymentFrequency).asText();
        }
        policyNumber.setValue(policyCode != null ? policyCode : templateRequest.getEmissionRequestDto().getPolicy().getPolicyCode());

        // End date placeholder
        String endDateStr = templateRequest.getEmissionRequestDto().getPolicy().getEndDate();
        LocalDateTime endDateTime = LocalDateTime.parse(endDateStr);
        TemplatePlaceholder endDate = new TemplatePlaceholder("endDate", 
            Utility.localDateTimeToFormattedDate(endDateTime.toString()));

        // End date with offset placeholder
        TemplatePlaceholder endDateWithOffset = new TemplatePlaceholder("endDateWithOffset", 
            Utility.localDateTimeToFormattedDate(endDateTime.plusDays(14).toString()));

        responseDto.getOptions().getTemplatePlaceholder().add(name);
        responseDto.getOptions().getTemplatePlaceholder().add(productName);
        responseDto.getOptions().getTemplatePlaceholder().add(policyNumber);
        responseDto.getOptions().getTemplatePlaceholder().add(endDate);
        responseDto.getOptions().getTemplatePlaceholder().add(endDateWithOffset);

        responseDto.getMessage().setKey("MANCATO_PAGAMENTO_ARAG");
        return responseDto;
    }
}
