package it.yolo.client.communicationManager.template.europAssistance;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.order.dto.response.OrderResponseDto;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

@RequestScoped
public class TimProtezioneViaggiAnnualeW implements Template {
        private static String CERTIFICATO_FILE_NAME = "CERTIFICATO";

        private static String CONTENT_TYPE_PDF = "application/pdf";

        @Override
        public TemplateResponseDto generate(TemplateRequest templateRequest) {
                TemplateResponseDto responseDto = new TemplateResponseDto();
                responseDto.getOptions()
                                .setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData()
                                                .getPrimaryMail());
                responseDto.getOptions().setLanguage("ita");
                responseDto.getOptions().setMessaggetype("html");
                responseDto.getMessage().setKey("TIM_ATTIVAZIONE_STANDARD");
                List<Attachments> attachmentsList = new ArrayList<>();

                attachmentsList.add(
                                new Attachments(CERTIFICATO_FILE_NAME + "."
                                                + templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getType().contains("pdf")
                                                                ? CONTENT_TYPE_PDF
                                                                : templateRequest.getCertificateResponseDto().getType(),
                                                templateRequest.getCertificateResponseDto().getFile()));

                informativaToBase64(attachmentsList, templateRequest.getEmissionRequestDto().getOrder());

                responseDto.setAttachment(attachmentsList);
                TemplatePlaceholder placeholder = new TemplatePlaceholder();
                placeholder.setKey("nome");
                placeholder.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
                responseDto.getOptions().setTemplatePlaceholder(Collections.singletonList(placeholder));
                return responseDto;
        }

        private void informativaToBase64(List<Attachments> attachmentsList, OrderResponseDto order) {
                try {
                        String urlFromorder = order.getResponse().getProduct().getDataProduct().getPackets().stream().filter(p->
                            p.getId().equals(order.getResponse().getPacketId())
                        ).findFirst().orElse(null).getConfiguration().get("packetDoc").asText();

                        URL url = new URL(urlFromorder);
                        String[] informativaNames = url.toString().split("/");

                        InputStream in = new BufferedInputStream(url.openStream());
                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        in.transferTo(Base64.getEncoder().wrap(out));

                        attachmentsList.add(new Attachments(
                                        informativaNames[informativaNames.length - 1],
                                        CONTENT_TYPE_PDF,
                                        out.toString()));
                } catch (Exception e) {
                        e.printStackTrace();
                }
        }

}