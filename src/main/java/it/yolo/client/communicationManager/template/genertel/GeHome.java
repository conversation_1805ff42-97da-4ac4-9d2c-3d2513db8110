package it.yolo.client.communicationManager.template.genertel;


import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.Collections;

@RequestScoped
public class GeHome implements Template {

    private static String CONTENT_TYPE_PDF="application/pdf";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        Attachments attachments =new Attachments();
        attachments.setFilename(templateRequest.getCertificateResponseDto().getNomeFile());

        if(templateRequest.getCertificateResponseDto().getType().contains("pdf")){
            attachments.setContenttype(CONTENT_TYPE_PDF);
        }else{
            attachments.setContenttype(templateRequest.getCertificateResponseDto().getType());
        }

        attachments.setBody(templateRequest.getEmissionResponseDto().getCertificate());
        responseDto.setAttachment(Collections.singletonList(attachments));
        responseDto.getMessage().setKey("POLIZZA_CASA_ATTIVAZIONE");
        return responseDto;
    }
}
