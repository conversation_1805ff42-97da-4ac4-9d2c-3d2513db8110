package it.yolo.client.communicationManager.template.ergo;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.Collections;
//        POLIZZA_CASA_ATTIVAZIONE	Attivazione polizza casa
//        FCA_ATTIVAZIONE_DEFAULT	Attivazione polizza MiFido Gold/Silver
//        RCAUTO_CLASSIC_ATTIVAZIONE	Attivazione polizza RcAuto Classic
//        WINTER_SPORT_PLUS_ATTIVAZIONE	Attivazione polizza Winter Sport Plus
//        WINTER_SPORT_PREMIUM_ATTIVAZIONE	Attivazione polizza Winter Sport Premium
@RequestScoped
public class ErgoMountainGold implements Template {
    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {

        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        Attachments attachments =new Attachments();
        attachments.setFilename(templateRequest.getCertificateResponseDto().getNomeFile());
        attachments.setContenttype(templateRequest.getCertificateResponseDto().getType());
        attachments.setBody(templateRequest.getCertificateResponseDto().getFile());
        responseDto.setAttachment(Collections.singletonList(attachments));
        responseDto.getMessage().setKey("SCI_GOLD_ATTIVAZIONE");
        return responseDto;
    }
}
