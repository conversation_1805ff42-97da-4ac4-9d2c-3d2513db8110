package it.yolo.client.communicationManager.template.genertel;


import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@RequestScoped
public class GeWinterSportPlus implements Template {

    private static String CONTENT_TYPE_PDF="application/pdf";

    private static String SET_INFORMATIVO_FILE_NAME ="GT_DIP_Yolo_SportInvernali_Plus_11.pdf";

    private static String CONTRATTO_ASSICURATIVO ="Contratto di assicurazione";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        List<Attachments> attachmentsList=new ArrayList<>();
        attachmentsList.add(new Attachments(CONTRATTO_ASSICURATIVO+"."+templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));
        URL url= null;
        try {
            url = new URL(templateRequest.getEmissionRequestDto().getOrder().getResponse()
                    .getProduct().getDataProduct().getInformativeSet());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (InputStream in = new BufferedInputStream(url.openStream())) {
            in.transferTo(Base64.getEncoder().wrap(out));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String base64 = out.toString();
        attachmentsList.add(new Attachments(SET_INFORMATIVO_FILE_NAME, CONTENT_TYPE_PDF, base64));
        responseDto.setAttachment(attachmentsList);

        responseDto.getMessage().setKey("WINTER_SPORT_PLUS_ATTIVAZIONE");
        return responseDto;
    }
}
