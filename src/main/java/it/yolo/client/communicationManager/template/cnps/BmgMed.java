package it.yolo.client.communicationManager.template.cnps;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class BmgMed implements Template {


    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {


        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
//        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties")
//                .get(""));
        responseDto.getOptions().setFromemail(templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties")
                .get("fromEmail").asText());
        responseDto.getOptions().setMessaggetype("html");
//        responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setUrgent(true);
        JsonNode order=new ObjectMapper().convertValue(templateRequest.getEmissionRequestDto().getOrder(), JsonNode.class);
        responseDto.getOptions().setMessagebody(order.toString());
        List<Attachments> attachmentsList=new ArrayList<>();
        responseDto.setAttachment(attachmentsList);
        responseDto.getMessage().setKey("INFO");
        return responseDto;
    }
}
