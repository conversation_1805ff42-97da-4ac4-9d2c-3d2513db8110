package it.yolo.client.communicationManager.template.genertel;

import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import javax.enterprise.context.RequestScoped;
import java.util.Collections;

@RequestScoped
public class GeHomeExtimate implements Template {

    private static String KEY_EMAIL_CASA="ASSICURAZIONE_CASA_PREVENTIVO";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        TemplatePlaceholder placeholder=new TemplatePlaceholder();
        placeholder.setKey("nome");
        placeholder.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        responseDto.getOptions().setTemplatePlaceholder(Collections.singletonList(placeholder));
        responseDto.getMessage().setKey(KEY_EMAIL_CASA);
        return responseDto;
    }
}
