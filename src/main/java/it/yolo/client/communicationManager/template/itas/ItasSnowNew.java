package it.yolo.client.communicationManager.template.itas;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@RequestScoped
public class ItasSnowNew implements Template {

    @ConfigProperty(name = "tenant.name")
    String tenantName;

    private static String CONTENT_TYPE_PDF="application/pdf";



    private static String CERTIFICATO_FILE_NAME ="CERTIFICATO";

    @ConfigProperty(name = "cc.mail")
    String ccMail;

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {


        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
        responseDto.getOptions().setMessaggetype("html");
        List<Attachments> attachmentsList=new ArrayList<>();
        attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME+"."+templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));
        AtomicBoolean fromCustomer=new AtomicBoolean(true);
        templateRequest.getEmissionRequestDto().getOrder().getResponse().getOrderItem().forEach(oItem -> {
            TemplatePlaceholder productName = new TemplatePlaceholder();
            productName.setKey("productName");
            productName.setValue(templateRequest.getEmissionRequestDto().getOrder().getResponse().getProduct().getDataProduct().getProductDescription());
            TemplatePlaceholder claimsMail = new TemplatePlaceholder();
            claimsMail.setKey("claimsMail");
            claimsMail.setValue(templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").get("claimsMail").asText());
            TemplatePlaceholder contactMail = new TemplatePlaceholder();
            contactMail.setKey("contactMail");
            contactMail.setValue(templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").get("contactMail").asText());
            responseDto.getOptions().getTemplatePlaceholder().add(productName);
            responseDto.getOptions().getTemplatePlaceholder().add(claimsMail);
            responseDto.getOptions().getTemplatePlaceholder().add(contactMail);
            String legalForm = templateRequest.getEmissionRequestDto().getCustomer().getData().getLegalForm();
            if (legalForm != null && legalForm.equals("true")) {
                TemplatePlaceholder name = new TemplatePlaceholder();
                name.setKey("nome");
                name.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getCompany());
                TemplatePlaceholder surname = new TemplatePlaceholder();
                surname.setKey("cognome");
                surname.setValue("");
                responseDto.getOptions().getTemplatePlaceholder().add(name);
                responseDto.getOptions().getTemplatePlaceholder().add(surname);
                fromCustomer.set(false);
            } else if (!oItem.getInsuredItem().get("customer_is_insured").asBoolean()) {
                oItem.getInsuredItem().withArray("insurance_holders").forEach(holder -> {
                    if (holder.get("is_contractor").asBoolean()) {
                        TemplatePlaceholder name = new TemplatePlaceholder();
                        name.setKey("nome");
                        name.setValue(holder.get("name").asText());
                        TemplatePlaceholder surname = new TemplatePlaceholder();
                        surname.setKey("cognome");
                        surname.setValue(holder.get("surname").asText());
                        responseDto.getOptions().getTemplatePlaceholder().add(name);
                        responseDto.getOptions().getTemplatePlaceholder().add(surname);
                        fromCustomer.set(false);
                    }
                });
            } else {
                TemplatePlaceholder name = new TemplatePlaceholder();
                name.setKey("nome");
                name.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
                TemplatePlaceholder surname = new TemplatePlaceholder();
                surname.setKey("cognome");
                surname.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());
                responseDto.getOptions().getTemplatePlaceholder().add(name);
                responseDto.getOptions().getTemplatePlaceholder().add(surname);
                fromCustomer.set(true);
            }
            responseDto.getOptions().setToMail(oItem.getInsuredItem().get("representative_email").asText());
        });
        if(fromCustomer.get()){
            responseDto.getOptions().setName(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
            responseDto.getOptions().setSurname(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());
        }
        if(ccMail != null && ccMail.contains("@"))
            responseDto.getOptions().setCc(Collections.singletonList(ccMail));
        URL url= null;
        String urlstring = templateRequest.getEmissionRequestDto().getProduct().get("data")
                .get("informativeSet").asText();
        try {
            url = new URL(urlstring);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (InputStream in = new BufferedInputStream(url.openStream())) {
            in.transferTo(Base64.getEncoder().wrap(out));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String base64 = out.toString();
        attachmentsList.add(new Attachments(urlstring.substring(urlstring.lastIndexOf("/")+1), CONTENT_TYPE_PDF, base64));
        responseDto.setAttachment(attachmentsList);
        responseDto.getMessage().setKey("EMISSIONE_POLIZZA_SNOW");
        return responseDto;
    }
}
