package it.yolo.client.communicationManager.template.netInsurance;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@RequestScoped
public class NetPetGold implements Template {

    @ConfigProperty(name = "tenant.name")
    String tenantName;

    private static String CONTENT_TYPE_PDF="application/pdf";

    private static String SET_INFORMATIVO_FILE_NAME ="SET_INFORMATIVO_NET_PET_GOLD.pdf";

    private static String CERTIFICATO_FILE_NAME ="CERTIFICATO";



    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {


        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        List<Attachments> attachmentsList=new ArrayList<>();
        attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME+"."+templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));
        URL url= null;
        try {
            url = new URL(templateRequest.getEmissionRequestDto().getOrder().getResponse()
                    .getProduct().getDataProduct().getInformativeSet());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (InputStream in = new BufferedInputStream(url.openStream())) {
            in.transferTo(Base64.getEncoder().wrap(out));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String base64 = out.toString();
        attachmentsList.add(new Attachments(SET_INFORMATIVO_FILE_NAME, CONTENT_TYPE_PDF, base64));
        responseDto.setAttachment(attachmentsList);
        if (tenantName.equalsIgnoreCase("intesa")) {
            responseDto.getMessage().setKey("PET_GOLD_ATTIVAZIONE");
        }
        else if (tenantName.equalsIgnoreCase("fca")) {
            responseDto.getMessage().setKey("FCA_ATTIVAZIONE_DEFAULT");
        }
        return responseDto;
    }
}
