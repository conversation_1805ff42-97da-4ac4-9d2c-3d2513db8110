package it.yolo.client.communicationManager.template.unipol;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

@RequestScoped
public class TimMyHome implements Template {

    private static String CERTIFICATO_FILE_NAME ="CERTIFICATO";

    private static String SET_INFORMATIVO_FILE_NAME ="Assistenza_TIM_Completo_LogoTIM.pdf";

    private static String CONTENT_TYPE_PDF="application/pdf";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        responseDto.getMessage().setKey("TIM_ATTIVAZIONE_STANDARD");
        List<Attachments> attachmentsList=new ArrayList<>();
        attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME+"."+templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));
        URL url= null;
        try {
            url = new URL(templateRequest.getEmissionRequestDto().getOrder().getResponse()
                    .getProduct().getDataProduct().getInformativeSet());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (InputStream in = new BufferedInputStream(url.openStream())) {
            in.transferTo(Base64.getEncoder().wrap(out));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String base64 = out.toString();
        attachmentsList.add(new Attachments(SET_INFORMATIVO_FILE_NAME, CONTENT_TYPE_PDF, base64));
        responseDto.setAttachment(attachmentsList);
        TemplatePlaceholder placeholder = new TemplatePlaceholder();
        placeholder.setKey("nome");
        placeholder.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        responseDto.getOptions().setTemplatePlaceholder(Collections.singletonList(placeholder));
        return responseDto;
    }

}
