package it.yolo.client.communicationManager.template.ergo;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class ErgoMountainSilver implements Template {

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        Attachments attachments =new Attachments();
        attachments.setFilename(templateRequest.getCertificateResponseDto().getNomeFile());
        attachments.setContenttype(templateRequest.getCertificateResponseDto().getType());
        attachments.setBody(templateRequest.getCertificateResponseDto().getFile());
        responseDto.setAttachment(Collections.singletonList(attachments));
        responseDto.getMessage().setKey("SCI_SILVER_ATTIVAZIONE");
        return responseDto;
    }
}
