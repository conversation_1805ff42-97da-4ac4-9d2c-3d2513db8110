package it.yolo.client.communicationManager.template.yolo;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.common.Utility;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@RequestScoped
public class MancatoPagamento implements Template {

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {

        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getEmissionRequestDto().getCustomer().getData().getPrimaryMail());
        responseDto.getOptions().setMessaggetype("html");
        responseDto.getOptions().setLanguage(templateRequest.getEmissionRequestDto().getOrder().getResponse().getLanguage());
        responseDto.getOptions().setName(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        responseDto.getOptions().setSurname(templateRequest.getEmissionRequestDto().getCustomer().getData().getSurname());
        TemplatePlaceholder name = new TemplatePlaceholder();
        name.setKey("nome");
        name.setValue(templateRequest.getEmissionRequestDto().getCustomer().getData().getName());
        TemplatePlaceholder productType = new TemplatePlaceholder();
        productType.setKey("tipoProdotto");
        productType.setValue(templateRequest.getEmissionRequestDto().getOrder().getResponse().getProductType());
        TemplatePlaceholder policyCode = new TemplatePlaceholder();
        policyCode.setKey("numeroPolizza");
        policyCode.setValue(templateRequest.getEmissionRequestDto().getPolicy().getPolicyCode());
        TemplatePlaceholder endDate = new TemplatePlaceholder();
        endDate.setKey("dataDisattivazione");
        endDate.setValue(Utility.localDateTimeToFormattedDate(templateRequest.getEmissionRequestDto().getPolicy().getEndDate()));
        responseDto.getOptions().getTemplatePlaceholder().add(name);
        responseDto.getOptions().getTemplatePlaceholder().add(productType);
        responseDto.getOptions().getTemplatePlaceholder().add(policyCode);
        responseDto.getOptions().getTemplatePlaceholder().add(endDate);

        responseDto.getMessage().setKey("MANCATO_PAGAMENTO");
        return responseDto;
    }
}
