package it.yolo.client.customer;

import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import it.yolo.client.dto.iad.request.ApiRequestDto;
import it.yolo.exception.EmissionFlowException;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-customer")
@Path("v1/customers")
public interface CustomerClient {

    @GET
    @Path("{id}")
    Response findById(@RestHeader("Authorization") String token,Long id);

    @PUT
    @Path("{tax_code}")
    Response updateByTaxCode(String tax_code, ApiRequestDto customerRequest);

    @PUT
    @Path("ndg/{ndg}")
    Response updateByNdg(String ndg, ApiRequestDto customerRequest, @RestHeader("Authorization") String token);

    @GET
    @Path("ndg/{ndg}")
    Response findByNdg(@RestHeader("Authorization") String token, String ndg);

    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 400 || response.getStatus() == 401 || response.getStatus() == 403 || response.getStatus() == 404
                || response.getStatus() == 422 || response.getStatus() == 500 || response.getStatus() == 502 || response.getStatus() == 503) {
            return new EmissionFlowException("Errore chiamata customer", "Status response customer: "+response.getStatus());
        }
        return null;
    }

}
