package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

public class UpdateWarrantiesRequest {

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("packetId")
    private int packetId;

    @JsonProperty("instance")
    private JsonNode instance;

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("packetId")
    public int getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(int packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("instance")
    public JsonNode getInstance() {
        return instance;
    }

    @JsonProperty("instance")
    public void setInstance(JsonNode instance) {
        this.instance = instance;
    }
}
