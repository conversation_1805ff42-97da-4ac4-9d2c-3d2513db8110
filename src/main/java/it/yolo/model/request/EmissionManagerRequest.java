package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class EmissionManagerRequest {
    @JsonProperty("data")
    private EmissionManagerRequestDto data;

    @JsonProperty("data")
    public EmissionManagerRequestDto getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(EmissionManagerRequestDto data) {
        this.data = data;
    }
}
