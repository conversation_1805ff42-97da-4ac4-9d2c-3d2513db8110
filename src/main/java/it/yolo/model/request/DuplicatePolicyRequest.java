package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DuplicatePolicyRequest {

    @JsonProperty("newOrderId")
    private Integer newOrderId;

    @JsonProperty("oldOrderId")
    private Integer oldOrderId;

    @JsonProperty("newOrderItemId")
    private Integer newOrderItemId;

    @JsonProperty("newPolicyCode")
    private String newPolicyCode;

    @JsonProperty("newOrderId")
    public Integer getNewOrderId() {
        return newOrderId;
    }

    @JsonProperty("newOrderId")
    public void setNewOrderId(Integer newOrderId) {
        this.newOrderId = newOrderId;
    }

    @JsonProperty("oldOrderId")
    public Integer getOldOrderId() {
        return oldOrderId;
    }

    @JsonProperty("oldOrderId")
    public void setOldOrderId(Integer oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    @JsonProperty("newOrderItemId")
    public Integer getNewOrderItemId() {
        return newOrderItemId;
    }

    @JsonProperty("newOrderItemId")
    public void setNewOrderItemId(Integer newOrderItemId) {
        this.newOrderItemId = newOrderItemId;
    }

    @JsonProperty("newPolicyCode")
    public String getNewPolicyCode() {
        return newPolicyCode;
    }

    @JsonProperty("newPolicyCode")
    public void setNewPolicyCode(String newPolicyCode) {
        this.newPolicyCode = newPolicyCode;
    }

    public DuplicatePolicyRequest() {
    }

    public DuplicatePolicyRequest(Integer newOrderId, Integer oldOrderId, Integer newOrderItemId, String newPolicyCode) {
        this.newOrderId = newOrderId;
        this.oldOrderId = oldOrderId;
        this.newOrderItemId = newOrderItemId;
        this.newPolicyCode = newPolicyCode;
    }
}
