package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class EmissionManagerRequestDto {

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("discount")
    private String discount;

    @JsonProperty("paymentType")
    private String paymentType;

    @JsonProperty("paymentTransactionId")
    private Integer paymentTransactionId;

    @JsonProperty("subscriptionId")
    private String subscriptionId;

    @JsonProperty("paymentToken")
    private String paymentToken;

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("discount")
    public String getDiscount() {
        return discount;
    }

    @JsonProperty("discount")
    public void setDiscount(String discount) {
        this.discount = discount;
    }

    @JsonProperty("paymentType")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("paymentType")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JsonProperty("paymentTransactionId")
    public Integer getPaymentTransactionId() {
        return paymentTransactionId;
    }

    @JsonProperty("paymentTransactionId")
    public void setPaymentTransactionId(Integer paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }

    @JsonProperty("paymentToken")
    public String getPaymentToken() {
        return paymentToken;
    }

    @JsonProperty("paymentToken")
    public void setPaymentToken(String paymentToken) {
        this.paymentToken = paymentToken;
    }

    @JsonProperty("subscriptionId")
    public String getSubscriptionId() {
        return subscriptionId;
    }

    @JsonProperty("subscriptionId")
    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }
}
