package it.yolo.model.request;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "firstname",
        "lastname",
        "taxcode",
        "phone",
        "birth_date",
        "city",
        "zipcode",
        "country_id",
        "state_id",
        "birth_city",
        "birth_city_id",
        "birth_state_id",
        "birth_country_id",
        "address1"
})
public class BillAddressAttributes {

    @JsonProperty("firstname")
    private String firstname;
    @JsonProperty("lastname")
    private String lastname;
    @JsonProperty("taxcode")
    private String taxcode;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("birth_date")
    private String birthDate;
    @JsonProperty("city")
    private String city;
    @JsonProperty("zipcode")
    private String zipcode;
    @JsonProperty("country_id")
    private Integer countryId;
    @JsonProperty("state_id")
    private Integer stateId;
    @JsonProperty("birth_city")
    private String birthCity;
    @JsonProperty("birth_city_id")
    private Integer birthCityId;
    @JsonProperty("birth_state_id")
    private Integer birthStateId;
    @JsonProperty("birth_country_id")
    private Integer birthCountryId;
    @JsonProperty("address1")
    private String address1;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("firstname")
    public String getFirstname() {
        return firstname;
    }

    @JsonProperty("firstname")
    public void setFirstname(String firstname) {
        this.firstname = firstname;
    }

    @JsonProperty("lastname")
    public String getLastname() {
        return lastname;
    }

    @JsonProperty("lastname")
    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    @JsonProperty("taxcode")
    public String getTaxcode() {
        return taxcode;
    }

    @JsonProperty("taxcode")
    public void setTaxcode(String taxcode) {
        this.taxcode = taxcode;
    }

    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }

    @JsonProperty("phone")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @JsonProperty("birth_date")
    public String getBirthDate() {
        return birthDate;
    }

    @JsonProperty("birth_date")
    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    @JsonProperty("city")
    public String getCity() {
        return city;
    }

    @JsonProperty("city")
    public void setCity(String city) {
        this.city = city;
    }

    @JsonProperty("zipcode")
    public String getZipcode() {
        return zipcode;
    }

    @JsonProperty("zipcode")
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @JsonProperty("country_id")
    public Integer getCountryId() {
        return countryId;
    }

    @JsonProperty("country_id")
    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    @JsonProperty("state_id")
    public Integer getStateId() {
        return stateId;
    }

    @JsonProperty("state_id")
    public void setStateId(Integer stateId) {
        this.stateId = stateId;
    }

    @JsonProperty("birth_city")
    public String getBirthCity() {
        return birthCity;
    }

    @JsonProperty("birth_city")
    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    @JsonProperty("birth_city_id")
    public Integer getBirthCityId() {
        return birthCityId;
    }

    @JsonProperty("birth_city_id")
    public void setBirthCityId(Integer birthCityId) {
        this.birthCityId = birthCityId;
    }

    @JsonProperty("birth_state_id")
    public Integer getBirthStateId() {
        return birthStateId;
    }

    @JsonProperty("birth_state_id")
    public void setBirthStateId(Integer birthStateId) {
        this.birthStateId = birthStateId;
    }

    @JsonProperty("birth_country_id")
    public Integer getBirthCountryId() {
        return birthCountryId;
    }

    @JsonProperty("birth_country_id")
    public void setBirthCountryId(Integer birthCountryId) {
        this.birthCountryId = birthCountryId;
    }

    @JsonProperty("address1")
    public String getAddress1() {
        return address1;
    }

    @JsonProperty("address1")
    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
