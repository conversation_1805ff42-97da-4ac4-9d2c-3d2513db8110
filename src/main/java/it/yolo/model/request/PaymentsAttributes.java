package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PaymentsAttributes {

    @JsonProperty("payment_method_id")
    private String payment_method_id;

    @JsonProperty("source_attributes")
    private SourceAttributes sourceAttributes;

    @JsonProperty("payment_method_id")
    public String getPayment_method_id() {
        return payment_method_id;
    }

    @JsonProperty("payment_method_id")
    public void setPayment_method_id(String payment_method_id) {
        this.payment_method_id = payment_method_id;
    }

    @JsonProperty("source_attributes")
    public SourceAttributes getSourceAttributes() {
        return sourceAttributes;
    }

    @JsonProperty("source_attributes")
    public void setSourceAttributes(SourceAttributes sourceAttributes) {
        this.sourceAttributes = sourceAttributes;
    }
}
