package it.yolo.model.request;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order"
})
public class OrderManagerRequest {

    @JsonProperty("state")
    private String state;

    @JsonProperty("order")
    private Order order;

    @JsonProperty("version")
    private String version;

    @JsonProperty("payment_token")
    private String payment_token;

    @JsonProperty("discount")
    private String discount;

    @JsonProperty("type")
    private String type;

    @JsonProperty("payment_method_description")
    private String payment_method_description;


    @JsonProperty("bearerToken")
    private String bearerToken;

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("paymentTransactionId")
    private Integer paymentTransactionId;

    @JsonProperty("paymentToken")
    private Integer paymentToken;

    @JsonProperty("payment_type")
    private String paymentType;

    @JsonProperty("addons")
    private JsonNode addons;

    @JsonProperty("bill_customer_id")
    private Integer billCustomerId;

    @JsonProperty("fieldToRecover")
    private Object fieldToRecover;
    @JsonProperty("channel")
    private String channel;

    @JsonProperty("customerId")
    private Long customerId;




    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("order")
    public Order getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(Order order) {
        this.order = order;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }


    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getQuantity() {
        if (this.order != null && this.order.getLineItemsAttributes() != null
                && this.order.getLineItemsAttributes().getNode() != null
                && this.order.getLineItemsAttributes().getNode().get("quantity") != null)
            return this.order.getLineItemsAttributes().getNode().get("quantity").asInt();
        else
            return 0;
    }

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("payment_token")
    public String getPayment_token() {
        return payment_token;
    }

    @JsonProperty("payment_token")
    public void setPayment_token(String payment_token) {
        this.payment_token = payment_token;
    }

    @JsonProperty("bearerToken")
    public String getBearerToken() {
        return bearerToken;
    }

    @JsonProperty("bearerToken")
    public void setBearerToken(String bearerToken) {
        this.bearerToken = bearerToken;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }


    @JsonProperty("payment_method_description")
    public String getPayment_method_description() {
        return payment_method_description;
    }

    @JsonProperty("payment_method_description")
    public void setPayment_method_description(String payment_method_description) {
        this.payment_method_description = payment_method_description;
    }

    @JsonProperty("paymentTransactionId")
    public Integer getPaymentTransactionId() {
        return paymentTransactionId;
    }

    @JsonProperty("paymentTransactionId")
    public void setPaymentTransactionId(Integer paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }

    @JsonProperty("paymentToken")
    public Integer getPaymentToken() {
        return paymentToken;
    }

    @JsonProperty("paymentToken")
    public void setPaymentToken(Integer paymentToken) {
        this.paymentToken = paymentToken;
    }

    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    @JsonProperty("payment_type")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("payment_type")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JsonProperty("addons")
    public JsonNode getAddons() {
        return addons;
    }

    @JsonProperty("addons")
    public void setAddons(JsonNode addons) {
        this.addons = addons;
    }

    @JsonProperty("discount")
    public String getDiscount() {
        return discount;
    }

    @JsonProperty("discount")
    public void setDiscount(String discount) {
        this.discount = discount;
    }

    @JsonProperty("bill_customer_id")
    public Integer getBillCustomerId() {
        return billCustomerId;
    }

    @JsonProperty("bill_customer_id")
    public void setBillCustomerId(Integer billCustomerId) {
        this.billCustomerId = billCustomerId;
    }

    @JsonProperty("fieldToRecover")
    public Object getFieldToRecover() {
        return fieldToRecover;
    }

    @JsonProperty("fieldToRecover")
    public void setFieldToRecover(Object fieldToRecover) {
        this.fieldToRecover = fieldToRecover;
    }
    @JsonProperty("channel")
    public String getChannel() {
        return this.channel;
    }

    @JsonProperty("channel")
    public void setChannel(String channel) {
        this.channel = channel;
    }

    
    @JsonProperty("customerId")
    public Long getCustomerId() {
        return this.customerId;
    }
    @JsonProperty("customerId")
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

}
