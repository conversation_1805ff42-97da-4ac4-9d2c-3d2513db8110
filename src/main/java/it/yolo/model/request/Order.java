package it.yolo.model.request;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonPropertyOrder({
        "line_items_attributes",
        "order_attributes"
})
@Generated("jsonschema2pojo")
public class Order {


    @JsonProperty("line_items_attributes")
    private LineItemsAttributes lineItemsAttributes;
    @JsonProperty("order_attributes")
    private OrderAttributes orderAttributes;

    @JsonProperty("bill_address_attributes")
    private BillAddressAttributes billAddressAttributes;

    @JsonProperty("payments_attributes")
    private List<PaymentsAttributes> paymentsAttributes;


    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("line_items_attributes")
    public LineItemsAttributes getLineItemsAttributes() {
        return lineItemsAttributes;
    }

    @JsonProperty("line_items_attributes")
    public void setLineItemsAttributes(LineItemsAttributes lineItemsAttributes) {
        this.lineItemsAttributes = lineItemsAttributes;
    }

    @JsonProperty("order_attributes")
    public OrderAttributes getOrderAttributes() {
        return orderAttributes;
    }

    @JsonProperty("order_attributes")
    public void setOrderAttributes(OrderAttributes orderAttributes) {
        this.orderAttributes = orderAttributes;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }


    @JsonProperty("bill_address_attributes")
    public BillAddressAttributes getBillAddressAttributes() {
        return billAddressAttributes;
    }


    @JsonProperty("bill_address_attributes")
    public void setBillAddressAttributes(BillAddressAttributes billAddressAttributes) {
        this.billAddressAttributes = billAddressAttributes;
    }

    @JsonProperty("payments_attributes")
    public  List<PaymentsAttributes> getPaymentsAttributes() {
        return paymentsAttributes;
    }

    @JsonProperty("payments_attributes")
    public void setPaymentsAttributes( List<PaymentsAttributes> paymentsAttributes) {
        this.paymentsAttributes = paymentsAttributes;
    }
}
