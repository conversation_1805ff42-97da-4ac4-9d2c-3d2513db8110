package it.yolo.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SourceAttributes {

    @JsonProperty("verification_value")
    private String verificationValue;

    @JsonProperty("number")
    private String number;

    @JsonProperty("month")
    private String month;

    @JsonProperty("year")
    private String year;

    @JsonProperty("name")
    private String name;

    @JsonProperty("verification_value")
    public String getVerificationValue() {
        return verificationValue;
    }

    @JsonProperty("verification_value")
    public void setVerificationValue(String verificationValue) {
        this.verificationValue = verificationValue;
    }

    @JsonProperty("number")
    public String getNumber() {
        return number;
    }

    @JsonProperty("number")
    public void setNumber(String number) {
        this.number = number;
    }

    @JsonProperty("month")
    public String getMonth() {
        return month;
    }

    @JsonProperty("month")
    public void setMonth(String month) {
        this.month = month;
    }

    @JsonProperty("year")
    public String getYear() {
        return year;
    }

    @JsonProperty("year")
    public void setYear(String year) {
        this.year = year;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }
}
