package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "pet_min_age",
        "pet_max_age",
        "has_breed"
})
@Generated("jsonschema2pojo")
public class Constraints {

    @JsonProperty("pet_min_age")
    private PetMinAge petMinAge;
    @JsonProperty("pet_max_age")
    private PetMaxAge petMaxAge;
    @JsonProperty("has_breed")
    private List<String> hasBreed = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("pet_min_age")
    public PetMinAge getPetMinAge() {
        return petMinAge;
    }

    @JsonProperty("pet_min_age")
    public void setPetMinAge(PetMinAge petMinAge) {
        this.petMinAge = petMinAge;
    }

    @JsonProperty("pet_max_age")
    public PetMaxAge getPetMaxAge() {
        return petMaxAge;
    }

    @JsonProperty("pet_max_age")
    public void setPetMaxAge(PetMaxAge petMaxAge) {
        this.petMaxAge = petMaxAge;
    }

    @JsonProperty("has_breed")
    public List<String> getHasBreed() {
        return hasBreed;
    }

    @JsonProperty("has_breed")
    public void setHasBreed(List<String> hasBreed) {
        this.hasBreed = hasBreed;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
