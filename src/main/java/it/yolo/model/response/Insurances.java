package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "running",
        "history",
        "last_renewed",
        "last_renewed_policy_number",
        "renew_candidate",
        "renewal_date",
        "replace_date"
})
@Generated("jsonschema2pojo")
public class Insurances {

    @JsonProperty("running")
    private Integer running;
    @JsonProperty("history")
    private List<Integer> history = null;
    @JsonProperty("last_renewed")
    private Object lastRenewed;
    @JsonProperty("last_renewed_policy_number")
    private Object lastRenewedPolicyNumber;
    @JsonProperty("renew_candidate")
    private Object renewCandidate;
    @JsonProperty("renewal_date")
    private Object renewalDate;
    @JsonProperty("replace_date")
    private Object replaceDate;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("running")
    public Integer getRunning() {
        return running;
    }

    @JsonProperty("running")
    public void setRunning(Integer running) {
        this.running = running;
    }

    @JsonProperty("history")
    public List<Integer> getHistory() {
        return history;
    }

    @JsonProperty("history")
    public void setHistory(List<Integer> history) {
        this.history = history;
    }

    @JsonProperty("last_renewed")
    public Object getLastRenewed() {
        return lastRenewed;
    }

    @JsonProperty("last_renewed")
    public void setLastRenewed(Object lastRenewed) {
        this.lastRenewed = lastRenewed;
    }

    @JsonProperty("last_renewed_policy_number")
    public Object getLastRenewedPolicyNumber() {
        return lastRenewedPolicyNumber;
    }

    @JsonProperty("last_renewed_policy_number")
    public void setLastRenewedPolicyNumber(Object lastRenewedPolicyNumber) {
        this.lastRenewedPolicyNumber = lastRenewedPolicyNumber;
    }

    @JsonProperty("renew_candidate")
    public Object getRenewCandidate() {
        return renewCandidate;
    }

    @JsonProperty("renew_candidate")
    public void setRenewCandidate(Object renewCandidate) {
        this.renewCandidate = renewCandidate;
    }

    @JsonProperty("renewal_date")
    public Object getRenewalDate() {
        return renewalDate;
    }

    @JsonProperty("renewal_date")
    public void setRenewalDate(Object renewalDate) {
        this.renewalDate = renewalDate;
    }

    @JsonProperty("replace_date")
    public Object getReplaceDate() {
        return replaceDate;
    }

    @JsonProperty("replace_date")
    public void setReplaceDate(Object replaceDate) {
        this.replaceDate = replaceDate;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
