package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "unique_name_presenter",
        "uniq_name",
        "cod_ramo",
        "cod_prodotto",
        "cod_rete_vendita_produttore",
        "livello_rete_vendita_produttore"
})
@Generated("jsonschema2pojo")
public class ProductProperties {

    @JsonProperty("unique_name_presenter")
    private String uniqueNamePresenter;
    @JsonProperty("uniq_name")
    private String uniqName;
    @JsonProperty("cod_ramo")
    private String codRamo;
    @JsonProperty("cod_prodotto")
    private String codProdotto;
    @JsonProperty("cod_rete_vendita_produttore")
    private String codReteVenditaProduttore;
    @JsonProperty("livello_rete_vendita_produttore")
    private String livelloReteVenditaProduttore;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("unique_name_presenter")
    public String getUniqueNamePresenter() {
        return uniqueNamePresenter;
    }

    @JsonProperty("unique_name_presenter")
    public void setUniqueNamePresenter(String uniqueNamePresenter) {
        this.uniqueNamePresenter = uniqueNamePresenter;
    }

    @JsonProperty("uniq_name")
    public String getUniqName() {
        return uniqName;
    }

    @JsonProperty("uniq_name")
    public void setUniqName(String uniqName) {
        this.uniqName = uniqName;
    }

    @JsonProperty("cod_ramo")
    public String getCodRamo() {
        return codRamo;
    }

    @JsonProperty("cod_ramo")
    public void setCodRamo(String codRamo) {
        this.codRamo = codRamo;
    }

    @JsonProperty("cod_prodotto")
    public String getCodProdotto() {
        return codProdotto;
    }

    @JsonProperty("cod_prodotto")
    public void setCodProdotto(String codProdotto) {
        this.codProdotto = codProdotto;
    }

    @JsonProperty("cod_rete_vendita_produttore")
    public String getCodReteVenditaProduttore() {
        return codReteVenditaProduttore;
    }

    @JsonProperty("cod_rete_vendita_produttore")
    public void setCodReteVenditaProduttore(String codReteVenditaProduttore) {
        this.codReteVenditaProduttore = codReteVenditaProduttore;
    }

    @JsonProperty("livello_rete_vendita_produttore")
    public String getLivelloReteVenditaProduttore() {
        return livelloReteVenditaProduttore;
    }

    @JsonProperty("livello_rete_vendita_produttore")
    public void setLivelloReteVenditaProduttore(String livelloReteVenditaProduttore) {
        this.livelloReteVenditaProduttore = livelloReteVenditaProduttore;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
