package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonPropertyOrder({
        "id",
        "number",
        "total",
        "state",
        "adjustment_total",
        "user_id",
        "created_by_id",
        "created_at",
        "updated_at",
        "completed_at",
        "payment_state",
        "email",
        "special_instructions",
        "verbal_order",
        "channel",
        "utm_source",
        "currency",
        "canceler_id",
        "type",
        "quotation_id",
        "promo_token",
        "intermediated",
        "item_total",
        "payment_total",
        "display_item_total",
        "common_original_total",
        "common_adjustment_total",
        "total_quantity",
        "display_total",
        "token",
        "checkout_steps",
        "payments_sources",
        "payment_methods",
        "bill_address",
        "payments",
        "credit_cards",
        "estimate",
        "insurances",
        "extra",
        "data",
        "line_items"
})
public class EmissionManagerResponse {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("number")
    private String number;
    @JsonProperty("total")
    private Double total;
    @JsonProperty("state")
    private String state;
    @JsonProperty("adjustment_total")
    private String adjustmentTotal;
    @JsonProperty("user_id")
    private Integer userId;
    @JsonProperty("created_by_id")
    private Object createdById;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("completed_at")
    private Object completedAt;
    @JsonProperty("payment_state")
    private Object paymentState;
    @JsonProperty("email")
    private String email;
    @JsonProperty("special_instructions")
    private Object specialInstructions;
    @JsonProperty("verbal_order")
    private String verbalOrder;
    @JsonProperty("channel")
    private String channel;
    @JsonProperty("utm_source")
    private String utmSource;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("canceler_id")
    private Object cancelerId;
    @JsonProperty("type")
    private String type;
    @JsonProperty("quotation_id")
    private Object quotationId;
    @JsonProperty("promo_token")
    private Object promoToken;
    @JsonProperty("intermediated")
    private Boolean intermediated;
    @JsonProperty("item_total")
    private Double itemTotal;
    @JsonProperty("payment_total")
    private Double paymentTotal;
    @JsonProperty("display_item_total")
    private String displayItemTotal;
    @JsonProperty("common_original_total")
    private Object commonOriginalTotal;
    @JsonProperty("common_adjustment_total")
    private Object commonAdjustmentTotal;
    @JsonProperty("total_quantity")
    private Double totalQuantity;
    @JsonProperty("display_total")
    private String displayTotal;
    @JsonProperty("token")
    private String token;
    @JsonProperty("checkout_steps")
    private List<String> checkoutSteps = null;
    @JsonProperty("payments_sources")
    private List<PaymentsSource> paymentsSources = null;
    @JsonProperty("payment_methods")
    private List<PaymentMethod> paymentMethods = null;
    @JsonProperty("bill_address")
    private Object billAddress;
    @JsonProperty("payments")
    private List<Object> payments = null;
    @JsonProperty("credit_cards")
    private List<Object> creditCards = null;
    @JsonProperty("estimate")
    private Object estimate;
    @JsonProperty("insurances")
    private Insurances insurances;
    @JsonProperty("extra")
    private Extra extra;
    @JsonProperty("data")
    private Object data;
    @JsonProperty("line_items")
    private List<LineItem> lineItems = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("number")
    public String getNumber() {
        return number;
    }

    @JsonProperty("number")
    public void setNumber(String number) {
        this.number = number;
    }

    @JsonProperty("total")
    public Double getTotal() {
        return total;
    }

    @JsonProperty("total")
    public void setTotal(Double total) {
        this.total = total;
    }

    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    @JsonProperty("adjustment_total")
    public String getAdjustmentTotal() {
        return adjustmentTotal;
    }

    @JsonProperty("adjustment_total")
    public void setAdjustmentTotal(String adjustmentTotal) {
        this.adjustmentTotal = adjustmentTotal;
    }

    @JsonProperty("user_id")
    public Integer getUserId() {
        return userId;
    }

    @JsonProperty("user_id")
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @JsonProperty("created_by_id")
    public Object getCreatedById() {
        return createdById;
    }

    @JsonProperty("created_by_id")
    public void setCreatedById(Object createdById) {
        this.createdById = createdById;
    }

    @JsonProperty("created_at")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("created_at")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updated_at")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updated_at")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @JsonProperty("completed_at")
    public Object getCompletedAt() {
        return completedAt;
    }

    @JsonProperty("completed_at")
    public void setCompletedAt(Object completedAt) {
        this.completedAt = completedAt;
    }

    @JsonProperty("payment_state")
    public Object getPaymentState() {
        return paymentState;
    }

    @JsonProperty("payment_state")
    public void setPaymentState(Object paymentState) {
        this.paymentState = paymentState;
    }

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("special_instructions")
    public Object getSpecialInstructions() {
        return specialInstructions;
    }

    @JsonProperty("special_instructions")
    public void setSpecialInstructions(Object specialInstructions) {
        this.specialInstructions = specialInstructions;
    }

    @JsonProperty("verbal_order")
    public String getVerbalOrder() {
        return verbalOrder;
    }

    @JsonProperty("verbal_order")
    public void setVerbalOrder(String verbalOrder) {
        this.verbalOrder = verbalOrder;
    }

    @JsonProperty("channel")
    public String getChannel() {
        return channel;
    }

    @JsonProperty("channel")
    public void setChannel(String channel) {
        this.channel = channel;
    }

    @JsonProperty("utm_source")
    public String getUtmSource() {
        return utmSource;
    }

    @JsonProperty("utm_source")
    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    @JsonProperty("currency")
    public String getCurrency() {
        return currency;
    }

    @JsonProperty("currency")
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @JsonProperty("canceler_id")
    public Object getCancelerId() {
        return cancelerId;
    }

    @JsonProperty("canceler_id")
    public void setCancelerId(Object cancelerId) {
        this.cancelerId = cancelerId;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("quotation_id")
    public Object getQuotationId() {
        return quotationId;
    }

    @JsonProperty("quotation_id")
    public void setQuotationId(Object quotationId) {
        this.quotationId = quotationId;
    }

    @JsonProperty("promo_token")
    public Object getPromoToken() {
        return promoToken;
    }

    @JsonProperty("promo_token")
    public void setPromoToken(Object promoToken) {
        this.promoToken = promoToken;
    }

    @JsonProperty("intermediated")
    public Boolean getIntermediated() {
        return intermediated;
    }

    @JsonProperty("intermediated")
    public void setIntermediated(Boolean intermediated) {
        this.intermediated = intermediated;
    }

    @JsonProperty("item_total")
    public Double getItemTotal() {
        return itemTotal;
    }

    @JsonProperty("item_total")
    public void setItemTotal(Double itemTotal) {
        this.itemTotal = itemTotal;
    }

    @JsonProperty("payment_total")
    public Double getPaymentTotal() {
        return paymentTotal;
    }

    @JsonProperty("payment_total")
    public void setPaymentTotal(Double paymentTotal) {
        this.paymentTotal = paymentTotal;
    }

    @JsonProperty("display_item_total")
    public String getDisplayItemTotal() {
        return displayItemTotal;
    }

    @JsonProperty("display_item_total")
    public void setDisplayItemTotal(String displayItemTotal) {
        this.displayItemTotal = displayItemTotal;
    }

    @JsonProperty("common_original_total")
    public Object getCommonOriginalTotal() {
        return commonOriginalTotal;
    }

    @JsonProperty("common_original_total")
    public void setCommonOriginalTotal(Object commonOriginalTotal) {
        this.commonOriginalTotal = commonOriginalTotal;
    }

    @JsonProperty("common_adjustment_total")
    public Object getCommonAdjustmentTotal() {
        return commonAdjustmentTotal;
    }

    @JsonProperty("common_adjustment_total")
    public void setCommonAdjustmentTotal(Object commonAdjustmentTotal) {
        this.commonAdjustmentTotal = commonAdjustmentTotal;
    }

    @JsonProperty("total_quantity")
    public Double getTotalQuantity() {
        return totalQuantity;
    }

    @JsonProperty("total_quantity")
    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    @JsonProperty("display_total")
    public String getDisplayTotal() {
        return displayTotal;
    }

    @JsonProperty("display_total")
    public void setDisplayTotal(String displayTotal) {
        this.displayTotal = displayTotal;
    }

    @JsonProperty("token")
    public String getToken() {
        return token;
    }

    @JsonProperty("token")
    public void setToken(String token) {
        this.token = token;
    }

    @JsonProperty("checkout_steps")
    public List<String> getCheckoutSteps() {
        return checkoutSteps;
    }

    @JsonProperty("checkout_steps")
    public void setCheckoutSteps(List<String> checkoutSteps) {
        this.checkoutSteps = checkoutSteps;
    }

    @JsonProperty("payments_sources")
    public List<PaymentsSource> getPaymentsSources() {
        return paymentsSources;
    }

    @JsonProperty("payments_sources")
    public void setPaymentsSources(List<PaymentsSource> paymentsSources) {
        this.paymentsSources = paymentsSources;
    }

    @JsonProperty("payment_methods")
    public List<PaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    @JsonProperty("payment_methods")
    public void setPaymentMethods(List<PaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    @JsonProperty("bill_address")
    public Object getBillAddress() {
        return billAddress;
    }

    @JsonProperty("bill_address")
    public void setBillAddress(Object billAddress) {
        this.billAddress = billAddress;
    }

    @JsonProperty("payments")
    public List<Object> getPayments() {
        return payments;
    }

    @JsonProperty("payments")
    public void setPayments(List<Object> payments) {
        this.payments = payments;
    }

    @JsonProperty("credit_cards")
    public List<Object> getCreditCards() {
        return creditCards;
    }

    @JsonProperty("credit_cards")
    public void setCreditCards(List<Object> creditCards) {
        this.creditCards = creditCards;
    }

    @JsonProperty("estimate")
    public Object getEstimate() {
        return estimate;
    }

    @JsonProperty("estimate")
    public void setEstimate(Object estimate) {
        this.estimate = estimate;
    }

    @JsonProperty("insurances")
    public Insurances getInsurances() {
        return insurances;
    }

    @JsonProperty("insurances")
    public void setInsurances(Insurances insurances) {
        this.insurances = insurances;
    }

    @JsonProperty("extra")
    public Extra getExtra() {
        return extra;
    }

    @JsonProperty("extra")
    public void setExtra(Extra extra) {
        this.extra = extra;
    }

    @JsonProperty("data")
    public Object getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Object data) {
        this.data = data;
    }

    @JsonProperty("line_items")
    public List<LineItem> getLineItems() {
        return lineItems;
    }

    @JsonProperty("line_items")
    public void setLineItems(List<LineItem> lineItems) {
        this.lineItems = lineItems;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }
}
