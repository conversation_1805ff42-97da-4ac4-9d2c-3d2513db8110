package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "y",
        "m",
        "d"
})
@Generated("jsonschema2pojo")
public class PetMaxAge {

    @JsonProperty("y")
    private Integer y;
    @JsonProperty("m")
    private Integer m;
    @JsonProperty("d")
    private Integer d;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("y")
    public Integer getY() {
        return y;
    }

    @JsonProperty("y")
    public void setY(Integer y) {
        this.y = y;
    }

    @JsonProperty("m")
    public Integer getM() {
        return m;
    }

    @JsonProperty("m")
    public void setM(Integer m) {
        this.m = m;
    }

    @JsonProperty("d")
    public Integer getD() {
        return d;
    }

    @JsonProperty("d")
    public void setD(Integer d) {
        this.d = d;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
