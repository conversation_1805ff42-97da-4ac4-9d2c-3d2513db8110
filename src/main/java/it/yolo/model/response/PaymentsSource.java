package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "month",
        "year",
        "cc_type",
        "last_digits",
        "gateway_customer_profile_id",
        "gateway_payment_profile_id",
        "created_at",
        "updated_at",
        "name",
        "user_id",
        "payment_method_id",
        "default",
        "address_id",
        "data",
        "owner_intermediary",
        "deleted_at"
})
@Generated("jsonschema2pojo")
public class PaymentsSource {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("month")
    private String month;
    @JsonProperty("year")
    private String year;
    @JsonProperty("cc_type")
    private String ccType;
    @JsonProperty("last_digits")
    private String lastDigits;
    @JsonProperty("gateway_customer_profile_id")
    private String gatewayCustomerProfileId;
    @JsonProperty("gateway_payment_profile_id")
    private String gatewayPaymentProfileId;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("name")
    private String name;
    @JsonProperty("user_id")
    private Integer userId;
    @JsonProperty("payment_method_id")
    private Integer paymentMethodId;
    @JsonProperty("default")
    private Boolean _default;
    @JsonProperty("address_id")
    private Object addressId;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("owner_intermediary")
    private Boolean ownerIntermediary;
    @JsonProperty("deleted_at")
    private Object deletedAt;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("month")
    public String getMonth() {
        return month;
    }

    @JsonProperty("month")
    public void setMonth(String month) {
        this.month = month;
    }

    @JsonProperty("year")
    public String getYear() {
        return year;
    }

    @JsonProperty("year")
    public void setYear(String year) {
        this.year = year;
    }

    @JsonProperty("cc_type")
    public String getCcType() {
        return ccType;
    }

    @JsonProperty("cc_type")
    public void setCcType(String ccType) {
        this.ccType = ccType;
    }

    @JsonProperty("last_digits")
    public String getLastDigits() {
        return lastDigits;
    }

    @JsonProperty("last_digits")
    public void setLastDigits(String lastDigits) {
        this.lastDigits = lastDigits;
    }

    @JsonProperty("gateway_customer_profile_id")
    public String getGatewayCustomerProfileId() {
        return gatewayCustomerProfileId;
    }

    @JsonProperty("gateway_customer_profile_id")
    public void setGatewayCustomerProfileId(String gatewayCustomerProfileId) {
        this.gatewayCustomerProfileId = gatewayCustomerProfileId;
    }

    @JsonProperty("gateway_payment_profile_id")
    public String getGatewayPaymentProfileId() {
        return gatewayPaymentProfileId;
    }

    @JsonProperty("gateway_payment_profile_id")
    public void setGatewayPaymentProfileId(String gatewayPaymentProfileId) {
        this.gatewayPaymentProfileId = gatewayPaymentProfileId;
    }

    @JsonProperty("created_at")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("created_at")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updated_at")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updated_at")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("user_id")
    public Integer getUserId() {
        return userId;
    }

    @JsonProperty("user_id")
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @JsonProperty("payment_method_id")
    public Integer getPaymentMethodId() {
        return paymentMethodId;
    }

    @JsonProperty("payment_method_id")
    public void setPaymentMethodId(Integer paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    @JsonProperty("default")
    public Boolean getDefault() {
        return _default;
    }

    @JsonProperty("default")
    public void setDefault(Boolean _default) {
        this._default = _default;
    }

    @JsonProperty("address_id")
    public Object getAddressId() {
        return addressId;
    }

    @JsonProperty("address_id")
    public void setAddressId(Object addressId) {
        this.addressId = addressId;
    }

    @JsonProperty("data")
    public Data getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Data data) {
        this.data = data;
    }

    @JsonProperty("owner_intermediary")
    public Boolean getOwnerIntermediary() {
        return ownerIntermediary;
    }

    @JsonProperty("owner_intermediary")
    public void setOwnerIntermediary(Boolean ownerIntermediary) {
        this.ownerIntermediary = ownerIntermediary;
    }

    @JsonProperty("deleted_at")
    public Object getDeletedAt() {
        return deletedAt;
    }

    @JsonProperty("deleted_at")
    public void setDeletedAt(Object deletedAt) {
        this.deletedAt = deletedAt;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
