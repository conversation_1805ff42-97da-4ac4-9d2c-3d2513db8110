package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonPropertyOrder({
        "id",
        "quantity",
        "price",
        "variant_id",
        "policy_number",
        "master_policy_number",
        "external_id",
        "state",
        "start_date",
        "expiration_date",
        "contractor_is_owner",
        "insured_is_contractor",
        "shipment_is_contractor",
        "papery_docs",
        "instant",
        "replace_date",
        "payment_frequency",
        "callback_on_order_updated",
        "days_number",
        "appliances_properties",
        "pet_properties",
        "house_properties",
        "variant",
        "addons",
        "insurance_info",
        "installments",
        "single_display_amount",
        "display_amount",
        "total",
        "insured_entities",
        "answers"
})
@Generated("jsonschema2pojo")
public class LineItem {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("quantity")
    private Integer quantity;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("variant_id")
    private Integer variantId;
    @JsonProperty("policy_number")
    private Object policyNumber;
    @JsonProperty("master_policy_number")
    private Object masterPolicyNumber;
    @JsonProperty("external_id")
    private Object externalId;
    @JsonProperty("state")
    private String state;
    @JsonProperty("start_date")
    private String startDate;
    @JsonProperty("expiration_date")
    private String expirationDate;
    @JsonProperty("contractor_is_owner")
    private Object contractorIsOwner;
    @JsonProperty("insured_is_contractor")
    private Boolean insuredIsContractor;
    @JsonProperty("shipment_is_contractor")
    private Boolean shipmentIsContractor;
    @JsonProperty("papery_docs")
    private Boolean paperyDocs;
    @JsonProperty("instant")
    private Boolean instant;
    @JsonProperty("replace_date")
    private Object replaceDate;
    @JsonProperty("payment_frequency")
    private Object paymentFrequency;
    @JsonProperty("callback_on_order_updated")
    private Object callbackOnOrderUpdated;
    @JsonProperty("days_number")
    private Object daysNumber;
    @JsonProperty("appliances_properties")
    private Object appliancesProperties;
    @JsonProperty("pet_properties")
    private PetProperties petProperties;
    @JsonProperty("house_properties")
    private HouseProperties houseProperties;
    @JsonProperty("variant")
    private Variant variant;
    @JsonProperty("addons")
    private List<Object> addons = null;
    @JsonProperty("installments")
    private Installments installments;
    @JsonProperty("single_display_amount")
    private String singleDisplayAmount;
    @JsonProperty("display_amount")
    private String displayAmount;
    @JsonProperty("total")
    private Double total;
    @JsonProperty("insured_entities")
    private InsuredEntities insuredEntities;
    @JsonProperty("answers")
    private List<Object> answers = null;
    @JsonProperty("insurance_info")
    private Object insurance_info;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("variant_id")
    public Integer getVariantId() {
        return variantId;
    }

    @JsonProperty("variant_id")
    public void setVariantId(Integer variantId) {
        this.variantId = variantId;
    }

    @JsonProperty("policy_number")
    public Object getPolicyNumber() {
        return policyNumber;
    }

    @JsonProperty("policy_number")
    public void setPolicyNumber(Object policyNumber) {
        this.policyNumber = policyNumber;
    }

    @JsonProperty("master_policy_number")
    public Object getMasterPolicyNumber() {
        return masterPolicyNumber;
    }

    @JsonProperty("master_policy_number")
    public void setMasterPolicyNumber(Object masterPolicyNumber) {
        this.masterPolicyNumber = masterPolicyNumber;
    }

    @JsonProperty("external_id")
    public Object getExternalId() {
        return externalId;
    }

    @JsonProperty("external_id")
    public void setExternalId(Object externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    @JsonProperty("start_date")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("start_date")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("expiration_date")
    public String getExpirationDate() {
        return expirationDate;
    }

    @JsonProperty("expiration_date")
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    @JsonProperty("contractor_is_owner")
    public Object getContractorIsOwner() {
        return contractorIsOwner;
    }

    @JsonProperty("contractor_is_owner")
    public void setContractorIsOwner(Object contractorIsOwner) {
        this.contractorIsOwner = contractorIsOwner;
    }

    @JsonProperty("insured_is_contractor")
    public Boolean getInsuredIsContractor() {
        return insuredIsContractor;
    }

    @JsonProperty("insured_is_contractor")
    public void setInsuredIsContractor(Boolean insuredIsContractor) {
        this.insuredIsContractor = insuredIsContractor;
    }

    @JsonProperty("shipment_is_contractor")
    public Boolean getShipmentIsContractor() {
        return shipmentIsContractor;
    }

    @JsonProperty("shipment_is_contractor")
    public void setShipmentIsContractor(Boolean shipmentIsContractor) {
        this.shipmentIsContractor = shipmentIsContractor;
    }

    @JsonProperty("papery_docs")
    public Boolean getPaperyDocs() {
        return paperyDocs;
    }

    @JsonProperty("papery_docs")
    public void setPaperyDocs(Boolean paperyDocs) {
        this.paperyDocs = paperyDocs;
    }

    @JsonProperty("instant")
    public Boolean getInstant() {
        return instant;
    }

    @JsonProperty("instant")
    public void setInstant(Boolean instant) {
        this.instant = instant;
    }

    @JsonProperty("replace_date")
    public Object getReplaceDate() {
        return replaceDate;
    }

    @JsonProperty("replace_date")
    public void setReplaceDate(Object replaceDate) {
        this.replaceDate = replaceDate;
    }

    @JsonProperty("payment_frequency")
    public Object getPaymentFrequency() {
        return paymentFrequency;
    }

    @JsonProperty("payment_frequency")
    public void setPaymentFrequency(Object paymentFrequency) {
        this.paymentFrequency = paymentFrequency;
    }

    @JsonProperty("callback_on_order_updated")
    public Object getCallbackOnOrderUpdated() {
        return callbackOnOrderUpdated;
    }

    @JsonProperty("callback_on_order_updated")
    public void setCallbackOnOrderUpdated(Object callbackOnOrderUpdated) {
        this.callbackOnOrderUpdated = callbackOnOrderUpdated;
    }

    @JsonProperty("days_number")
    public Object getDaysNumber() {
        return daysNumber;
    }

    @JsonProperty("days_number")
    public void setDaysNumber(Object daysNumber) {
        this.daysNumber = daysNumber;
    }

    @JsonProperty("appliances_properties")
    public Object getAppliancesProperties() {
        return appliancesProperties;
    }

    @JsonProperty("appliances_properties")
    public void setAppliancesProperties(Object appliancesProperties) {
        this.appliancesProperties = appliancesProperties;
    }

    @JsonProperty("pet_properties")
    public PetProperties getPetProperties() {
        return petProperties;
    }

    @JsonProperty("pet_properties")
    public void setPetProperties(PetProperties petProperties) {
        this.petProperties = petProperties;
    }

    @JsonProperty("house_properties")
    public HouseProperties getHouseProperties() {
        return houseProperties;
    }

    @JsonProperty("house_properties")
    public void setHouseProperties(HouseProperties houseProperties) {
        this.houseProperties = houseProperties;
    }

    @JsonProperty("variant")
    public Variant getVariant() {
        return variant;
    }

    @JsonProperty("variant")
    public void setVariant(Variant variant) {
        this.variant = variant;
    }

    @JsonProperty("addons")
    public List<Object> getAddons() {
        return addons;
    }

    @JsonProperty("addons")
    public void setAddons(List<Object> addons) {
        this.addons = addons;
    }

    @JsonProperty("installments")
    public Installments getInstallments() {
        return installments;
    }

    @JsonProperty("installments")
    public void setInstallments(Installments installments) {
        this.installments = installments;
    }

    @JsonProperty("single_display_amount")
    public String getSingleDisplayAmount() {
        return singleDisplayAmount;
    }

    @JsonProperty("single_display_amount")
    public void setSingleDisplayAmount(String singleDisplayAmount) {
        this.singleDisplayAmount = singleDisplayAmount;
    }

    @JsonProperty("display_amount")
    public String getDisplayAmount() {
        return displayAmount;
    }

    @JsonProperty("display_amount")
    public void setDisplayAmount(String displayAmount) {
        this.displayAmount = displayAmount;
    }

    @JsonProperty("total")
    public Double getTotal() {
        return total;
    }

    @JsonProperty("total")
    public void setTotal(Double total) {
        this.total = total;
    }

    @JsonProperty("insured_entities")
    public InsuredEntities getInsuredEntities() {
        return insuredEntities;
    }

    @JsonProperty("insured_entities")
    public void setInsuredEntities(InsuredEntities insuredEntities) {
        this.insuredEntities = insuredEntities;
    }

    @JsonProperty("answers")
    public List<Object> getAnswers() {
        return answers;
    }

    @JsonProperty("answers")
    public void setAnswers(List<Object> answers) {
        this.answers = answers;
    }

    @JsonProperty("insurance_info")
    public Object getInsurance_info() {
        return insurance_info;
    }

    @JsonProperty("insurance_info")
    public void setInsurance_info(Object insurance_info) {
        this.insurance_info = insurance_info;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
