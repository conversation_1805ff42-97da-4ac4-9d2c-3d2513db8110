package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "product_id",
        "id",
        "name",
        "sku",
        "option_values",
        "product_properties",
        "price",
        "fixed_start_date",
        "fixed_end_date",
        "images"
})
@Generated("jsonschema2pojo")
public class Variant {

    @JsonProperty("product_id")
    private Integer productId;
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("sku")
    private String sku;

    @JsonInclude(JsonInclude.Include.ALWAYS)
    @JsonProperty("option_values")
    private Object optionValues = null;
    @JsonProperty("product_properties")
    private ProductProperties productProperties;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("fixed_start_date")
    private Object fixedStartDate;
    @JsonProperty("fixed_end_date")
    private Object fixedEndDate;
    @JsonProperty("images")
    private List<Image> images = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("product_id")
    public Integer getProductId() {
        return productId;
    }

    @JsonProperty("product_id")
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("sku")
    public String getSku() {
        return sku;
    }

    @JsonProperty("sku")
    public void setSku(String sku) {
        this.sku = sku;
    }

    @JsonProperty("option_values")
    public Object getOptionValues() {
        return optionValues;
    }

    @JsonProperty("option_values")
    public void setOptionValues(Object optionValues) {
        this.optionValues = optionValues;
    }

    @JsonProperty("product_properties")
    public ProductProperties getProductProperties() {
        return productProperties;
    }

    @JsonProperty("product_properties")
    public void setProductProperties(ProductProperties productProperties) {
        this.productProperties = productProperties;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("fixed_start_date")
    public Object getFixedStartDate() {
        return fixedStartDate;
    }

    @JsonProperty("fixed_start_date")
    public void setFixedStartDate(Object fixedStartDate) {
        this.fixedStartDate = fixedStartDate;
    }

    @JsonProperty("fixed_end_date")
    public Object getFixedEndDate() {
        return fixedEndDate;
    }

    @JsonProperty("fixed_end_date")
    public void setFixedEndDate(Object fixedEndDate) {
        this.fixedEndDate = fixedEndDate;
    }

    @JsonProperty("images")
    public List<Image> getImages() {
        return images;
    }

    @JsonProperty("images")
    public void setImages(List<Image> images) {
        this.images = images;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
