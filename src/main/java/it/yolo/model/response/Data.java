package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "prepaid",
        "payroll",
        "debit"
})
@Generated("jsonschema2pojo")
public class Data {

    @JsonProperty("prepaid")
    private String prepaid;
    @JsonProperty("payroll")
    private String payroll;
    @JsonProperty("debit")
    private String debit;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("prepaid")
    public String getPrepaid() {
        return prepaid;
    }

    @JsonProperty("prepaid")
    public void setPrepaid(String prepaid) {
        this.prepaid = prepaid;
    }

    @JsonProperty("payroll")
    public String getPayroll() {
        return payroll;
    }

    @JsonProperty("payroll")
    public void setPayroll(String payroll) {
        this.payroll = payroll;
    }

    @JsonProperty("debit")
    public String getDebit() {
        return debit;
    }

    @JsonProperty("debit")
    public void setDebit(String debit) {
        this.debit = debit;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
