package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "presentation",
        "value",
        "dangerous"
})
@Generated("jsonschema2pojo")
public class Breed {

    @JsonProperty("presentation")
    private String presentation;
    @JsonProperty("value")
    private String value;
    @JsonProperty("dangerous")
    private Boolean dangerous;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("presentation")
    public String getPresentation() {
        return presentation;
    }

    @JsonProperty("presentation")
    public void setPresentation(String presentation) {
        this.presentation = presentation;
    }

    @JsonProperty("value")
    public String getValue() {
        return value;
    }

    @JsonProperty("value")
    public void setValue(String value) {
        this.value = value;
    }

    @JsonProperty("dangerous")
    public Boolean getDangerous() {
        return dangerous;
    }

    @JsonProperty("dangerous")
    public void setDangerous(Boolean dangerous) {
        this.dangerous = dangerous;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
