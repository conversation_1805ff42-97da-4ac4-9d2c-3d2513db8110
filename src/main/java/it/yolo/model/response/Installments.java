package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "receipt_certificate_url"
})
@Generated("jsonschema2pojo")
public class Installments {

    @JsonProperty("receipt_certificate_url")
    private Object receiptCertificateUrl;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("receipt_certificate_url")
    public Object getReceiptCertificateUrl() {
        return receiptCertificateUrl;
    }

    @JsonProperty("receipt_certificate_url")
    public void setReceiptCertificateUrl(Object receiptCertificateUrl) {
        this.receiptCertificateUrl = receiptCertificateUrl;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
