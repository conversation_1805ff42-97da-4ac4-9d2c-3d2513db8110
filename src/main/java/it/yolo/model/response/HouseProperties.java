package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "building_types",
        "construction_materials",
        "owner_types"
})
@Generated("jsonschema2pojo")
public class HouseProperties {

    @JsonProperty("building_types")
    private Object buildingTypes;
    @JsonProperty("construction_materials")
    private Object constructionMaterials;
    @JsonProperty("owner_types")
    private Object ownerTypes;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("building_types")
    public Object getBuildingTypes() {
        return buildingTypes;
    }

    @JsonProperty("building_types")
    public void setBuildingTypes(Object buildingTypes) {
        this.buildingTypes = buildingTypes;
    }

    @JsonProperty("construction_materials")
    public Object getConstructionMaterials() {
        return constructionMaterials;
    }

    @JsonProperty("construction_materials")
    public void setConstructionMaterials(Object constructionMaterials) {
        this.constructionMaterials = constructionMaterials;
    }

    @JsonProperty("owner_types")
    public Object getOwnerTypes() {
        return ownerTypes;
    }

    @JsonProperty("owner_types")
    public void setOwnerTypes(Object ownerTypes) {
        this.ownerTypes = ownerTypes;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
