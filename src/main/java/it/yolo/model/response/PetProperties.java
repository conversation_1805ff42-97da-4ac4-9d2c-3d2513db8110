package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "kinds",
        "breeds",
        "constraints"
})
@Generated("jsonschema2pojo")
public class PetProperties {

    @JsonProperty("kinds")
    private Kinds kinds;
    @JsonProperty("breeds")
    private List<Breed> breeds = null;
    @JsonProperty("constraints")
    private Constraints constraints;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("kinds")
    public Kinds getKinds() {
        return kinds;
    }

    @JsonProperty("kinds")
    public void setKinds(Kinds kinds) {
        this.kinds = kinds;
    }

    @JsonProperty("breeds")
    public List<Breed> getBreeds() {
        return breeds;
    }

    @JsonProperty("breeds")
    public void setBreeds(List<Breed> breeds) {
        this.breeds = breeds;
    }

    @JsonProperty("constraints")
    public Constraints getConstraints() {
        return constraints;
    }

    @JsonProperty("constraints")
    public void setConstraints(Constraints constraints) {
        this.constraints = constraints;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
