package it.yolo.model.response;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "insurance_holders",
        "shipment_address_attributes",
        "insureds_excel",
        "pets",
        "house",
        "houses",
        "car",
        "bike",
        "appliances",
        "building"
})
@Generated("jsonschema2pojo")
public class InsuredEntities {

    @JsonProperty("insurance_holders")
    private Object insuranceHolders;
    @JsonProperty("shipment_address_attributes")
    private Object shipmentAddressAttributes;
    @JsonProperty("insureds_excel")
    private Object insuredsExcel;
    @JsonProperty("pets")
    private Object[] pets = null;
    @JsonProperty("house")
    private Object house;
    @JsonProperty("houses")
    private Object houses;
    @JsonProperty("car")
    private Object car;
    @JsonProperty("bike")
    private Object bike;
    @JsonProperty("appliances")
    private Object appliances;
    @JsonProperty("building")
    private Object building;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("insurance_holders")
    public Object getInsuranceHolders() {
        return insuranceHolders;
    }

    @JsonProperty("insurance_holders")
    public void setInsuranceHolders(Object insuranceHolders) {
        this.insuranceHolders = insuranceHolders;
    }

    @JsonProperty("shipment_address_attributes")
    public Object getShipmentAddressAttributes() {
        return shipmentAddressAttributes;
    }

    @JsonProperty("shipment_address_attributes")
    public void setShipmentAddressAttributes(Object shipmentAddressAttributes) {
        this.shipmentAddressAttributes = shipmentAddressAttributes;
    }

    @JsonProperty("insureds_excel")
    public Object getInsuredsExcel() {
        return insuredsExcel;
    }

    @JsonProperty("insureds_excel")
    public void setInsuredsExcel(Object insuredsExcel) {
        this.insuredsExcel = insuredsExcel;
    }

    @JsonProperty("pets")
    public Object[] getPets() {
        return pets;
    }

    @JsonProperty("pets")
    public void setPets(Object[] pets) {
        this.pets = pets;
    }

    @JsonProperty("house")
    public Object getHouse() {
        return house;
    }

    @JsonProperty("house")
    public void setHouse(Object house) {
        this.house = house;
    }

    @JsonProperty("houses")
    public Object getHouses() {
        return houses;
    }

    @JsonProperty("houses")
    public void setHouses(Object houses) {
        this.houses = houses;
    }

    @JsonProperty("car")
    public Object getCar() {
        return car;
    }

    @JsonProperty("car")
    public void setCar(Object car) {
        this.car = car;
    }

    @JsonProperty("bike")
    public Object getBike() {
        return bike;
    }

    @JsonProperty("bike")
    public void setBike(Object bike) {
        this.bike = bike;
    }

    @JsonProperty("appliances")
    public Object getAppliances() {
        return appliances;
    }

    @JsonProperty("appliances")
    public void setAppliances(Object appliances) {
        this.appliances = appliances;
    }

    @JsonProperty("building")
    public Object getBuilding() {
        return building;
    }

    @JsonProperty("building")
    public void setBuilding(Object building) {
        this.building = building;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
