package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class ActionBoundaryRequest {

    @JsonProperty("action")
    private String action;

    @JsonProperty("code")
    private String code;


    @JsonProperty("additionalProperties")
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonProperty("action")
    public String getAction() {
        return action;
    }

    @JsonProperty("action")
    public void setAction(String action) {
        this.action = action;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    @JsonProperty("additionalProperties")
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonProperty("additionalProperties")
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    public ActionBoundaryRequest() {
    }

    public ActionBoundaryRequest(String action, String code) {
        this.action = action;
        this.code = code;
        this.additionalProperties = additionalProperties;
    }
}
