package it.yolo.model.error;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@AllArgsConstructor
@RegisterForReflection
@Schema(description = "Error response")
public class ErrorResponse {

    @Getter
    @Setter
    @Schema(description = "Error")
    private String error;

    @Getter
    @Setter
    @Schema(description = "Information")
    private String errorInfo;
}
