package it.yolo.exception;

/**
 * mother class for all yolo exceptions
 * every yolo exception must have a title and a descritpion
 * to be used to show what's wrong to frontend
 */
public class YoloException extends RuntimeException{

	private static final long serialVersionUID = -3718573297825873344L;
	
	private String title, description;
	
	public YoloException(String title, String descritpion) {
		super(title);
		this.title = title;
		this.description = descritpion;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
