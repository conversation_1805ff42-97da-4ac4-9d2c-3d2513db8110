package it.yolo.exception;

import it.yolo.model.error.ErrorResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.resteasy.reactive.server.ServerExceptionMapper;

import javax.ws.rs.core.Response;

public class ExceptionMappers {

    private static final Logger LOGGER = Logger.getLogger(ExceptionMappers.class);
    private static final String SEPARATOR = "\n";


    @ServerExceptionMapper(Exception.class)
    public RestResponse<ErrorResponse> mapException(Exception ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);
        return RestResponse.status(Response.Status.INTERNAL_SERVER_ERROR,
                new ErrorResponse(ex.getMessage(),formatStackTrace(ex.getStackTrace())));
    }

    public static String formatStackTrace(StackTraceElement[] stackTrace) {
        StringBuilder buffer = new StringBuilder();
        for (StackTraceElement element : stackTrace) {
            buffer.append(element).append(SEPARATOR);
        }
        return buffer.toString();
    }

}
