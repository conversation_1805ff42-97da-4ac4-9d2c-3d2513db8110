package it.yolo.workflow;

import it.yolo.client.payment.BrainTreeClient;
import it.yolo.client.payment.dto.ProviderPaymentRequestDto;
import it.yolo.client.payment.dto.ProviderPaymentResponseDto;
import it.yolo.exception.PaymentException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class BrainTreeProvider implements PaymentProvider<ProviderPaymentResponseDto, ProviderPaymentRequestDto> {

    @Inject
    @RestClient
    BrainTreeClient brainTreeClient;

    private static final String TYPE_SINGLE = "single";
    private static final String TYPE_RECURRENT = "recurring";

    @Override
    public ProviderPaymentResponseDto execute(ProviderPaymentRequestDto brainTreeRequest) throws Exception {
        Response res = null;
        ProviderPaymentResponseDto brainTreeResponse=null;

        if (brainTreeRequest.getType().equalsIgnoreCase(TYPE_SINGLE)) {
            try {
                res = brainTreeClient.paymentSingle(brainTreeRequest.getBearerToken(), brainTreeRequest.getRequest());
            }catch (Exception e){
                throw new PaymentException(e.getMessage());
            }
            brainTreeResponse = res.readEntity(ProviderPaymentResponseDto.class);
        } else if (brainTreeRequest.getType().equalsIgnoreCase(TYPE_RECURRENT)) {
            try {
                res = brainTreeClient.paymentRecurring(brainTreeRequest.getBearerToken(), brainTreeRequest.getRequest());
            }catch (Exception e){
                throw new PaymentException(e.getMessage());
            }
            brainTreeResponse= res.readEntity(ProviderPaymentResponseDto.class);
        } else {
            throw new Exception("Frequenza pagamento non conforme: " + brainTreeRequest.getType());
        }
        return brainTreeResponse;
    }
}