package it.yolo.workflow;

import it.yolo.client.payment.NoPayClient;
import it.yolo.client.payment.dto.ProviderPaymentRequestDto;
import it.yolo.client.payment.dto.ProviderPaymentResponseDto;
import it.yolo.exception.PaymentException;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class NoPayProvider implements PaymentProvider<ProviderPaymentResponseDto, ProviderPaymentRequestDto> {

    @Inject
    @RestClient
    NoPayClient noPayClient;

    @Override
    public ProviderPaymentResponseDto execute(ProviderPaymentRequestDto noPayRequestDto) throws Exception {
        Response res = null;
        ProviderPaymentResponseDto noPayResponseDto=null;
            try {
                res = noPayClient.payment(noPayRequestDto.getBearerToken(), noPayRequestDto.getRequest(), noPayRequestDto.getOrcerCode());
            }catch (Exception e){
                throw new PaymentException(e.getMessage());
            }
        noPayResponseDto = res.readEntity(ProviderPaymentResponseDto.class);
        return noPayResponseDto;
    }
}