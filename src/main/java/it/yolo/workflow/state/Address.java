package it.yolo.workflow.state;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.dto.iad.response.ApiResponseDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.exception.AdpException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.service.ServiceCustomer;
import it.yolo.service.ServiceOrder;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class Address implements State {

    @Inject
    ServiceCustomer serviceCustomer;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    JsonWebToken jsonWebToken;

    @WithSpan("Address.workflow")
    @Override
    public EmissionManagerResponse workflow(OrderManagerRequest req, String orderCode) {
        String ndg=jsonWebToken.getClaim("username");
        String token = "Bearer " + jsonWebToken.getRawToken();
        ApiResponseDto billAdressesAttr = serviceCustomer.updateByNdg(req, token, ndg.toUpperCase());
        req.setBillCustomerId(billAdressesAttr.getResponse().getId());
        OrderResponseDto orderResponseDto = serviceOrder.updateByVersion(req.getVersion(), orderCode, req);
        orderResponseDto.setVersion(req.getVersion());
        Response reponseAdapter;
        try {
            reponseAdapter = adpOrderClient.trasformResponse(orderResponseDto);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);
        orderManageResponse.setBillAddress(req.getOrder().getBillAddressAttributes());
        return orderManageResponse;
    }
}
