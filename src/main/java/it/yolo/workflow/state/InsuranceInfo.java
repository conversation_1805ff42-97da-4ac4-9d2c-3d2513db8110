package it.yolo.workflow.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.dto.legacy.Addons;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.pricing.PricingClient;
import it.yolo.client.product.response.dto.ProductResponseDto;
import it.yolo.exception.AdpException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.service.ServiceOrder;
import it.yolo.service.ServiceProduct;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequestScoped
public class InsuranceInfo implements State {
    @Inject
    ServiceOrder serviceOrder;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    @RestClient
    PricingClient pricingClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceProduct serviceProduct;

    @WithSpan("InsuranceInfo.workflow")
    @Override
    public EmissionManagerResponse workflow(OrderManagerRequest req, String orderCode) {
        pricingCheck(req);
        OrderResponseDto orderResponseDto = serviceOrder.updateByVersion(req.getVersion(), orderCode, req);
        orderResponseDto.setVersion(req.getVersion());
        Response reponseAdapter;
        try {
            reponseAdapter = adpOrderClient.trasformResponse(orderResponseDto);
        } catch (Exception e) {
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);

        return orderManageResponse;
    }

    private void pricingCheck(OrderManagerRequest req) {
        if (req.getOrder().getLineItemsAttributes().getNode().get("pricing") != null) {
            ProductResponseDto productResponseDto = serviceProduct.findByOrderVersion(req.getVersion(), req);
            JsonNode pricing = pricingClient.quote("Bearer " + jsonWebToken.getRawToken(),
                    req.getOrder().getLineItemsAttributes().getNode().get("pricing")).readEntity(JsonNode.class);
            Double price = Double.parseDouble(pricing.get("total").asText());

            /* RIMOSSA VERTICALIZZAZIONE SUL PRODUCT CODE TIM-MY-HOME
            if (productResponseDto.getDataProduct().getCode().equalsIgnoreCase("tim-my-home")) {
                JsonNode manipulation = this.manipulationAddonsHome(productResponseDto, pricing);
                req.getAdditionalProperties().put("quotatorPrice", price);
                req.getAdditionalProperties().put("quotatorAddons", manipulation);
            } else {
                req.getAdditionalProperties().put("quotatorPrice", price);
                req.getAdditionalProperties().put("quotatorAddons", pricing);
            }*/

            req.getAdditionalProperties().put("quotatorPrice", price);
            req.getAdditionalProperties().put("quotatorAddons", pricing);
        }
    }


    private JsonNode manipulationAddonsHome(ProductResponseDto productResponseDto, JsonNode resposnePricing) {
        JsonNode addons = productResponseDto.getDataProduct().getLegacy().get("addons");
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //mapper.writeValueAsString(addons)
        try {
            List<Addons> adddonsResultMap = new ArrayList<>();
            List<Addons> addonsList = mapper.readValue(addons.toString(), new TypeReference<List<Addons>>() {
            });
            JsonNode priceAddonsResponse = resposnePricing.get("addons");
            List<Addons> addonsPriceResponse = mapper.readValue(priceAddonsResponse.toString(), new TypeReference<List<Addons>>() {
            });
            addonsList.forEach(addonsPrdouctConf -> {
                addonsPriceResponse.forEach(addonsResponse -> {
                    if (addonsPrdouctConf.getCode().equalsIgnoreCase(addonsResponse.getId())) {
                        addonsResponse.setName(addonsPrdouctConf.getName());
                        addonsResponse.setDescription(addonsPrdouctConf.getDescription());
                        adddonsResultMap.add(addonsResponse);
                    }
                });
            });
            Map<String, Object> nodeMap = mapper.convertValue(resposnePricing, new TypeReference<Map<String, Object>>() {
            });
            nodeMap.put("addons", adddonsResultMap);
            resposnePricing = mapper.convertValue(nodeMap, JsonNode.class);
            return resposnePricing;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}