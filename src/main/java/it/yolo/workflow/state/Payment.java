package it.yolo.workflow.state;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.adp.payment.AdpPaymentClient;
import it.yolo.client.dto.iad.response.ProductPaymentMethod;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.payment.dto.ProviderPaymentRequestDto;
import it.yolo.client.payment.dto.ProviderPaymentResponseDto;
import it.yolo.client.product.ProductClient;
import it.yolo.client.product.response.dto.ProductResponseDto;
import it.yolo.common.Utility;
import it.yolo.exception.AdpException;
import it.yolo.exception.OrderException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.records.PaymentExecute;
import it.yolo.service.ServiceOrder;
import it.yolo.workflow.BrainTreeProvider;
import it.yolo.workflow.NoPayProvider;
import it.yolo.workflow.PaymentProvider;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class Payment implements State {

    @ConfigProperty(name = "sub.pay.mock.plainId")
    String mockPlainId;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    @RestClient
    AdpPaymentClient adpPaymentClient;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    @RestClient
    ProductClient producClient;

    @Inject
    BrainTreeProvider brainTreeProvider;

    @Inject
    NoPayProvider gupProvider;

    @Inject
    JsonWebToken jsonWebToken;

    private Map<String, PaymentProvider<ProviderPaymentResponseDto, ProviderPaymentRequestDto>> mapProvider;

    private static final String TYPE_BRAINTREE="Braintree";

    private static final String TYPE_NO_PAY ="NoPay";

    @PostConstruct
    void init() {
        mapProvider = new HashMap<>();
        mapProvider.put(TYPE_BRAINTREE, brainTreeProvider);
        mapProvider.put(TYPE_NO_PAY, gupProvider);
    }

    @Override
    public EmissionManagerResponse workflow(OrderManagerRequest req, String orderCode) throws Exception {
        //call iad-order
        req.setOrderCode(orderCode);
        OrderResponseDto orderResponseDto= serviceOrder.findByOrderCode(req);

        String token = "Bearer " + jsonWebToken.getRawToken();
        // call iad-product with response.productId from order
        Response responseProduct;
        try {
             responseProduct= producClient.findById(token, null, Long.valueOf(orderResponseDto.getResponse().getProductId()));
        }catch (Exception e){
            throw new OrderException(e.getMessage());
        }
        ProductResponseDto productResponseDto=responseProduct.readEntity(ProductResponseDto.class);
        productResponseDto.getDataProduct().getProductPaymentMethod();
        productResponseDto.getDataProduct().getPlanName();

        // call adapter for trasfrorm request
        String paymentToken = req.getPayment_token();

        //calcolo tipologia pagamento configurata sul prodotto e scelta dall'utente
        PaymentExecute paymentExecute = getPaymentExecute(productResponseDto,req);


        orderResponseDto.getResponse().getAdditionalProperties().put("additionalInfo",req);
        // nel dto della order ci settiamo un campo per la frequenze singola o riccorente e la inviamo all'adapter
        orderResponseDto.getResponse().setPaymentProvider(paymentExecute.paymentProvider());
        orderResponseDto.getResponse().setType(paymentExecute.frequency());
        if(paymentExecute.externalId()!=null){
            orderResponseDto.getResponse().setExternalId(String.valueOf(paymentExecute.externalId()));
        }

        req.setPaymentType(paymentExecute.frequency());
        //verifica del plainId qualora fosse configurato sul prodotto
        if(productResponseDto.getDataProduct().getPlanId()!=null){
            String plainId=Utility.getPlanId(productResponseDto.getDataProduct().getPlanId(),req.getPaymentType());
            orderResponseDto.getResponse().setPlanId(plainId);
            orderResponseDto.getResponse().setPlanName(productResponseDto.getDataProduct().getPlanName());
        }

        //verifica codice sconto
        if(req.getDiscount()!=null){
            orderResponseDto.setDiscount(req.getDiscount());
            if(req.getDiscount().equalsIgnoreCase("sconto1@@")){
                orderResponseDto.getResponse().setPlanId(mockPlainId);
            }
        }

        Response response;
        try {
            response= adpPaymentClient.trasfomrRequest(paymentToken,orderResponseDto);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }

        JsonNode jsonRequest=response.readEntity(JsonNode.class);
        ProviderPaymentResponseDto providerPaymentResponseDto = null;

        if(paymentExecute==null || paymentExecute.paymentMethod()==null){
            throw new Exception("Attenzione c'è stato un problema sulla scelta del pagamento");
        }


        ProviderPaymentRequestDto providerPaymentRequestDto= new ProviderPaymentRequestDto();
        providerPaymentRequestDto.setRequest(jsonRequest);
        providerPaymentRequestDto.setBearerToken(req.getBearerToken());
        providerPaymentRequestDto.setType(paymentExecute.frequency());
        providerPaymentRequestDto.setOrcerCode(orderCode);
        providerPaymentResponseDto = mapProvider.get(paymentExecute.paymentProvider()).execute(providerPaymentRequestDto);




        req.setPaymentTransactionId(Integer.valueOf(providerPaymentResponseDto.getTransactionId()));
        OrderResponseDto responseDto=serviceOrder.updateByVersion(req.getVersion(),req.getOrderCode(),req);

        responseDto.setVersion(req.getVersion());
        Response reponseAdapter;
        try {
            reponseAdapter = adpOrderClient.trasformResponse(responseDto);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);
        return orderManageResponse;
    }



    public PaymentExecute getPaymentExecute(ProductResponseDto productResponseDto, OrderManagerRequest req){
        PaymentExecute paymentExecute = null;
        for (ProductPaymentMethod paymentMethod: productResponseDto.getDataProduct().getProductPaymentMethod()) {
                if(paymentMethod.getPaymentMethod().getPaymentMethodType().equalsIgnoreCase(req.getType())){
                    String paymentMethodType= paymentMethod.getPaymentMethod().getPaymentMethodType();
                    String frequency=  paymentMethod.getPaymentMethod().getType();
                    String payMethod=paymentMethod.getPaymentMethod().getPaymentMethod();
                    String paymentProvider = paymentMethod.getPaymentMethod().getProvider();
                    Integer externalId=paymentMethod.getPaymentMethod().getExternalId();
                    paymentExecute = new PaymentExecute(paymentMethodType,frequency, payMethod, paymentProvider,externalId);
                    break;
                }
        }
        return paymentExecute;
    }


}
