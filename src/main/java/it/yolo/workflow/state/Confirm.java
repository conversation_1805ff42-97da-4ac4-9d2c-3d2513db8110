package it.yolo.workflow.state;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoResponse;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.communicationManager.template.ergo.ErgoMountainGold;
import it.yolo.client.communicationManager.template.ergo.ErgoMountainSilver;
import it.yolo.client.communicationManager.template.genertel.GeHome;
import it.yolo.client.communicationManager.template.genertel.GeWinterSportPlus;
import it.yolo.client.communicationManager.template.genertel.GeWinterSportPremium;
import it.yolo.client.communicationManager.template.genertel.GenertelRca;
import it.yolo.client.communicationManager.template.netInsurance.*;
import it.yolo.client.communicationManager.template.quixa.QuixaHealth;
import it.yolo.client.communicationManager.template.unipol.TimMyHome;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.dto.response.ProductOrderResponse;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.client.yin.YinClient;
import it.yolo.emission.dto.request.*;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.AdpException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.service.*;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class Confirm implements State {

    @Inject
    JsonWebToken jsonWebToken;
    @Inject
    ServiceOrder serviceOrder;

    @Inject
    ServiceCustomer serviceCustomer;

    @Inject
    ServiceEmission serviceEmission;

    @Inject
    NetPetSilver netPetSilver;

    @Inject
    NetPetGold netPetGold;

    @Inject
    ErgoMountainSilver ergoMountainSilver;

    @Inject
    ErgoMountainGold ergoMountainGold;

    @Inject
    GenertelRca genertelRca;

    @Inject
    GeHome geHome;

    @Inject
    GeWinterSportPlus geWinterSportPlus;

    @Inject
    GeWinterSportPremium geWinterSportPremium;

    @Inject
    TimPet timPet;

    @Inject
    TimSci timSci;

    @Inject
    QuixaHealth quixaHealth;

    @Inject
    TimMyHome timMyHome;

    @Inject
    TimEmployeePet timEmployeePet;

    @Inject
    ServicePolicy servicePolicy;

    @Inject
    ServiceCommunicationManager serviceCommunicationManager;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    Logger logger;

    @ConfigProperty(name = "yin.coreApiKey")
    String coreApiKey;


    @Inject
    @RestClient
    YinClient yinClient;


    private Map<String, Map<String, Template>> mapProductToTemplate = new HashMap<>();

    @PostConstruct
    void initMap() {
        Map<String, Template> mapNetInsurance = new HashMap<>();
        mapNetInsurance.put("net-pet-silver", netPetSilver);
        mapNetInsurance.put("net-pet-gold", netPetGold);
        mapNetInsurance.put("customers-tim-pet", timPet);
        mapNetInsurance.put("tim-my-pet", timEmployeePet);
        mapNetInsurance.put("tim-for-ski-gold", timSci);
        mapNetInsurance.put("tim-for-ski-silver", timSci);
        mapNetInsurance.put("tim-for-ski-platinum", timSci);
        Map<String, Template> mapErgo = new HashMap<>();
        mapErgo.put("ergo-mountain-silver", ergoMountainSilver);
        mapErgo.put("ergo-mountain-gold", ergoMountainGold);
        Map<String, Template> mapGenertel = new HashMap<>();
        mapGenertel.put("genertel-rca", genertelRca);
        mapGenertel.put("ge-home", geHome);
        mapGenertel.put("winter-sport-plus", geWinterSportPlus);
        mapGenertel.put("winter-sport-premium", geWinterSportPremium);
        Map<String, Template> mapQuixa = new HashMap<>();
        mapQuixa.put("ehealth-quixa-standard", quixaHealth);
        Map<String, Template> mapUnipol = new HashMap<>();
        mapUnipol.put("tim-my-home", timMyHome);
        mapProductToTemplate.put("Net Insurance", mapNetInsurance);
        mapProductToTemplate.put("Ergo", mapErgo);
        mapProductToTemplate.put("Genertel", mapGenertel);
        mapProductToTemplate.put("Quixa", mapQuixa);
        mapProductToTemplate.put("Unipol", mapUnipol);
    }

    @Override
    public EmissionManagerResponse workflow(OrderManagerRequest req, String orderCode) throws Exception {
        CustomerResponseDto customerResponseDto;
        Response reponseAdapter;
        String token = "Bearer " + jsonWebToken.getRawToken();
        String ndg = jsonWebToken.getClaim("username");
        OrderResponseDto orderResponseDto = serviceOrder.updateByVersion(req.getVersion(), req.getOrderCode(), req);
        /*
         *   Introdotta logica per YIN DOC in quanto non era possibile prelevare
         *   n'ndg dal token.Il token staccato è un token tecnico
         *  l'invocazione alla customer id avverrà solo quando verremo richiamati dal be in node di
         *  intermediari . WhiteLabel ragiona con il jwt
         */
        if(req.getChannel()!=null && req.getChannel().equalsIgnoreCase("YIN") &&
                req.getCustomerId()!=null){
           customerResponseDto = serviceCustomer.findById(token, req.getCustomerId());
        }else{
         customerResponseDto = serviceCustomer.findByNdg(token, ndg);
        }

        EmissionRequestDto emissionRequestDto = new EmissionRequestDto();
        emissionRequestDto.setCustomer(customerResponseDto);
        emissionRequestDto.setOrder(orderResponseDto);
        emissionRequestDto.setPayment_frequency(orderResponseDto.getResponse().getPaymentType());
        emissionRequestDto.setAddons(req.getAddons());

        if (emissionRequestDto.getOrder().getResponse().getProduct() == null) {
            ProductOrderResponse productOrderResponse = new ProductOrderResponse();
            productOrderResponse.setDataProduct(emissionRequestDto.getOrder().getResponse().getPacket().getDataPacket().getProduct());
            emissionRequestDto.getOrder().getResponse().setProduct(productOrderResponse);
        }


        EmissionResponseDto emissionResponseDto = serviceEmission.emissionPolicy(emissionRequestDto);
        CertificateRequestDto certificateRequestDto = emissionToCertificate(emissionRequestDto, emissionResponseDto);
        CertificateResponseDto certificateResponseDto = serviceEmission.generateCerticatePolicy(certificateRequestDto, emissionResponseDto);

//        PolicyResponseDto res = servicePolicy.updateAfterEmission(emissionResponseDto.getPolicyResponseDto().getData().getPolicyCode(),
//                                            Costant.STUB_CERTIFCATE_NAME, Costant.STUB_LINK_CERTIFCATE,Costant.STUB_CONTENT_TYPE);


        if (emissionResponseDto.getPolicyResponseDto().getData() != null && emissionRequestDto.getOrder().getResponse().getProduct() != null) {
            String nomeFile = emissionResponseDto.getPolicyResponseDto().getData().getPolicyCode()+ ".pdf";
            certificateResponseDto.setNomeFile(nomeFile);
        }

        PolicyResponseDto res = servicePolicy.updateAfterEmission(emissionResponseDto,
                certificateResponseDto.getNomeFile(), certificateResponseDto.getLink(), certificateResponseDto.getType(),
                orderResponseDto.getResponse().getProduct().getDataProduct().getCode(), null);

        CommunicationManagerDtoResponse response = serviceCommunicationManager.sendEmail(this.certificationToCommunicationRequest(certificateResponseDto, emissionResponseDto, emissionRequestDto));
        orderResponseDto.setVersion(req.getVersion());

        try {
            reponseAdapter = adpOrderClient.trasformResponse(orderResponseDto);
        } catch (Exception e) {
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);

        //LOGICA PER YIN
        if(req.getChannel()!=null && req.getChannel().equalsIgnoreCase("YIN")){
            try{
                logger.info("align order ");
                Response responseYin= yinClient.alignOder(coreApiKey, req.getOrderCode(),req.getVersion());
                logger.info("align end  " + responseYin.readEntity(JsonNode.class).toString());
            }catch (Exception ex){
                logger.info("align error  " + ex.getMessage() + res);
                return orderManageResponse;
            }
        }

        return orderManageResponse;
    }


    private CertificateRequestDto emissionToCertificate(EmissionRequestDto emissionRequestDto, EmissionResponseDto emissionResponseDto) {
        CertificateRequestDto certificateRequestDto = new CertificateRequestDto();
//        certificateRequestDto.setProduct(emissionRequestDto.getOrder().getResponse().getProduct());
        certificateRequestDto.setPolicy(emissionResponseDto.getPolicyResponseDto());
        certificateRequestDto.setOrder(emissionRequestDto.getOrder());
        certificateRequestDto.setCustomer(emissionRequestDto.getCustomer());
        certificateRequestDto.setCertificate(emissionResponseDto.getCertificate());
        if(emissionResponseDto.getEmission() != null) {
            certificateRequestDto.getOrder().getResponse().getOrderItem().forEach(orderItem -> {
                orderItem.setEmission(emissionResponseDto.getEmission());});
        }
        return certificateRequestDto;
    }

    private CommunicationManagerDtoRequest certificationToCommunicationRequest(CertificateResponseDto certificate, EmissionResponseDto emissionResponseDto, EmissionRequestDto emissionRequestDto) {
        CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
        TemplateRequest templateRequest = new TemplateRequest();
        templateRequest.setCertificateResponseDto(certificate);
        templateRequest.setEmissionRequestDto(emissionRequestDto);
        templateRequest.setEmissionResponseDto(emissionResponseDto);
        TemplateResponseDto templateResponseDto = mapProductToTemplate.get(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsuranceCompany()).get(
                emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode()).generate(templateRequest);
        communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
        communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
        communicationManagerDtoRequest.setAttachments(templateResponseDto.getAttachment());
        return communicationManagerDtoRequest;
    }


}