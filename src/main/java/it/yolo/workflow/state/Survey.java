package it.yolo.workflow.state;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.dto.response.ProductOrderResponse;
import it.yolo.exception.AdpException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.service.ServiceCommunicationManager;
import it.yolo.service.ServiceCustomer;
import it.yolo.service.ServiceOrder;
import it.yolo.service.ServiceSurvey;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class Survey implements State {

    @Inject
    ServiceSurvey serviceSurvey;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    ServiceCustomer serviceCustomer;

    @Inject
    ServiceCommunicationManager communicationManager;

    @Inject
    JsonWebToken jsonWebToken;

    // RIMOSSA VERTICALIZZAZIONE GE-HOME
    // private static String CASA_PRODUCT="ge-home";


    @WithSpan("Survey.workflow")
    @Override
    public EmissionManagerResponse workflow(OrderManagerRequest req, String orderCode) {
        req.setOrderCode(orderCode);
        serviceSurvey.create(req);
        OrderResponseDto orderResponseDto = serviceOrder.updateByVersion(req.getVersion(), orderCode, req);
        orderResponseDto.setVersion(req.getVersion());
        Response reponseAdapter;
        try {
            reponseAdapter = adpOrderClient.trasformResponse(orderResponseDto);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);
        sendEstimateEmail(orderResponseDto);
        return orderManageResponse;
    }

    private void sendEstimateEmail(OrderResponseDto orderResponseDto){
        if (orderResponseDto.getResponse().getProduct() == null ||
                orderResponseDto.getResponse().getProduct().getDataProduct() == null) {
            ProductOrderResponse productOrderResponse = new ProductOrderResponse();
            productOrderResponse.setDataProduct(orderResponseDto.getResponse().getPacket().getDataPacket().getProduct());
            orderResponseDto.getResponse().setProduct(productOrderResponse);
        }
        /* RIMOSSA VERTICALIZZAZIONE CASA GE-HOME
        if (CASA_PRODUCT.equalsIgnoreCase(orderResponseDto.getResponse().getProduct().getDataProduct().getCode())){
            CustomerResponseDto customerResponseDto=serviceCustomer.findByNdg("Bearer " + jsonWebToken.getRawToken(), jsonWebToken.getClaim("username"));
            //TODO da migliorare (in vista del rilascio)
            GeHomeExtimate template=new GeHomeExtimate();
            TemplateRequest req=new TemplateRequest();
            req.setEmissionRequestDto(new EmissionRequestDto());
            req.getEmissionRequestDto().setCustomer(new CustomerResponseDto());
            req.getEmissionRequestDto().getCustomer().setData(new DataCustomerResponseDto());
            req.getEmissionRequestDto().getCustomer().getData().setPrimaryMail(customerResponseDto.getData().getPrimaryMail());
            req.getEmissionRequestDto().getCustomer().getData().setName(customerResponseDto.getData().getName());
            CommunicationManagerDtoRequest communicationManagerDtoRequest=new CommunicationManagerDtoRequest();
            TemplateResponseDto resDTO=template.generate(req);
            communicationManagerDtoRequest.setMessage(resDTO.getMessage());
            communicationManagerDtoRequest.setOptions(resDTO.getOptions());
            communicationManagerDtoRequest.setAttachments(resDTO.getAttachment());
            communicationManager.sendEmail(communicationManagerDtoRequest);
        }*/
    }
}
