package it.yolo.service;


import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.communicationManager.CommunicationManagerClient;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoResponse;
import it.yolo.exception.CommunicationManagerException;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
@RequestScoped
public class ServiceCommunicationManager {

    @Inject
    @RestClient
    CommunicationManagerClient communicationManagerClient;

    @Inject
    JsonWebToken jsonWebToken;

    @WithSpan("ServiceCustomer.update")
    public CommunicationManagerDtoResponse sendEmail(CommunicationManagerDtoRequest request) {
        Response res;
        try{
            res=communicationManagerClient.sendEmail("Bearer "+jsonWebToken.getRawToken(), request.getOptions().getLanguage(), request);
        }catch (Exception e){
            throw new CommunicationManagerException(e.getMessage());
        }
        return res.readEntity(CommunicationManagerDtoResponse.class);
    }
}
