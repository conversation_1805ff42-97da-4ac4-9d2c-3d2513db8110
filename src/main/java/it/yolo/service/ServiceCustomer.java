package it.yolo.service;


import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.dto.iad.response.ApiResponseDto;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.service.client.InvokeCustomer;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ServiceCustomer {

    @Inject
    InvokeCustomer invokeCustomer;


    @WithSpan("ServiceCustomer.update")
    public ApiResponseDto update(OrderManagerRequest request, String taxCode) {
        return invokeCustomer.update(request, taxCode);
    }

    @WithSpan("ServiceCustomer.findByNdg")
    public CustomerResponseDto findByNdg(String token, String ndg) {
        return invokeCustomer.findByNdg(token, ndg);
    }

    @WithSpan("ServiceCustomer.findById")
    public CustomerResponseDto findById(String token, Long id) {
        return invokeCustomer.findById(token, id);
    }

    @WithSpan("ServiceCustomer.updateByNdg")
    public ApiResponseDto updateByNdg(OrderManagerRequest request, String token, String ndg) {
        return invokeCustomer.updateByNdg(request, token, ndg);
    }
}
