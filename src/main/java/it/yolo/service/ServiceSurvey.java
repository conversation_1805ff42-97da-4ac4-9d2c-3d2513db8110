package it.yolo.service;


import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.survey.AdpSurveyClient;
import it.yolo.client.order.SurveyClient;
import it.yolo.client.order.dto.request.AnswerDto;
import it.yolo.exception.AdpException;
import it.yolo.exception.SurveyException;
import it.yolo.model.request.OrderManagerRequest;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServiceSurvey {

    @Inject
    @RestClient
    SurveyClient surveyClient;

    @Inject
    @RestClient
    AdpSurveyClient adpSurveyClient;

    @Inject
    JsonWebToken jsonWebToken;

    @WithSpan("ServiceSurvey.create")
    public void create(OrderManagerRequest request) {
        String token = "Bearer " + jsonWebToken.getRawToken();
        Response responseAdp;
        try {
            responseAdp= adpSurveyClient.trasfomrRequest(request);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        AnswerDto requestSurvey = responseAdp.readEntity(AnswerDto.class);
        Response res;
        try {
            res= surveyClient.create(token, requestSurvey);
        }catch (Exception e){
            throw new SurveyException(e.getMessage());
        }
    }


}
