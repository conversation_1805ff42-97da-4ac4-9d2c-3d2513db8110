package it.yolo.service.V2;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.OrderClient;
import it.yolo.client.order.dto.response.DataProductOrderResponse;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.costants.Costant;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.exception.CommunicationManagerException;
import it.yolo.exception.CustomerEx;
import it.yolo.exception.OrderException;
import it.yolo.exception.PolicyException;
import it.yolo.model.ActionBoundaryRequest;
import it.yolo.service.ServiceCommunicationManager;
import it.yolo.service.ServiceCustomer;
import it.yolo.service.TemplateService;
import it.yolo.service.client.InvokePolicy;
import it.yolo.service.client.V2.InvokePolicyV2;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

@RequestScoped
public class ServicePolicyV2 {
    @Inject
    InvokePolicyV2 invokePolicy;
    @Inject
    InvokePolicy invokePolicyLegacy;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    TemplateService templateService;

    @Inject
    ServiceCommunicationManager serviceCommunicationManager;

    @Inject
    ServiceCustomer serviceCustomer;

    @Inject
    Logger logger;


    public PolicyResponseDto updateNumberFailedPayment(PolicyRequestDto req, String token, String subscriptionId)
    {
        PolicyResponseDto policy;
        try {
            policy = invokePolicy.updateNumberFailedPayment(token, req, subscriptionId);
        }catch (Exception e) {
            throw new PolicyException(e.getMessage());
        }
        CustomerResponseDto customer;
        try {
            customer = serviceCustomer.findById(token, policy.getData().getCustomer().getId());
        } catch (Exception e) {
            logger.error("while fetching customer", e);
            throw new CustomerEx(e.getMessage());
        }
        OrderResponseDto order;
        try {
            order = orderClient.findByIdUnchecked(token, policy.getData().getOrderId()).readEntity(OrderResponseDto.class);
        } catch (Exception e) {
            logger.error("while fetching order", e);
            throw new OrderException(e.getMessage());
        }
        try {
            logger.info("sending e-mail...");
            serviceCommunicationManager.sendEmail(this.policyToCommunicationRequest(policy, customer, order));
            logger.info("e-mail has been sent successfully");
        } catch (Exception e) {
            logger.error("while sending e-mail", e);
            throw new CommunicationManagerException(e.getMessage());
        }

        return policy;
    }

    public PolicyResponseDto updateSuccessfulPayment(PolicyRequestDto req, String token, String subscriptionId)
    {
        OrderResponseDto order=orderClient.findByOrderCodeUnchecked(token, req.getDto().getOrderCode())
                .readEntity(OrderResponseDto.class);
        DataProductOrderResponse product=order.getResponse().getProduct().getDataProduct();
        Long orderId=Long.valueOf((order.getResponse().getId()));
        req.getDto().setOrderId(orderId);
        PolicyResponseDto policy= invokePolicy.readPolicyByOrderIdActive(token, orderId);
        LocalDate nBD=LocalDate.parse(policy.getData().getNextBillingDate().substring(0,10));
        LocalDate end=LocalDate.parse(policy.getData().getEndDate().substring(0,10));
        try {
            if(product.getRecurring() && nBD.until(end, ChronoUnit.DAYS) < 12 &&
                    req.getDto().getPaymentMethod().equalsIgnoreCase(Costant.STRIPE)) {
                String action=order.getResponse().getProduct().getDataProduct().getConfiguration().getProperties().has("renewalAction") ?
                        order.getResponse().getProduct().getDataProduct().getConfiguration().getProperties().get("renewalAction").asText() :
                        Costant.STD_ACTION;
                ActionBoundaryRequest requestData=new ActionBoundaryRequest(action, order.getResponse().getOrderCode());
                ObjectNode request= JsonNodeFactory.instance.objectNode();
                request.putPOJO("data", requestData);
                policy=invokePolicyLegacy.action(request);
            }else {
                policy = invokePolicy.updateSuccessfulPayment(token, req, subscriptionId);
            }
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }

        return policy;
    }

    private CommunicationManagerDtoRequest policyToCommunicationRequest(PolicyResponseDto policy, CustomerResponseDto customer, OrderResponseDto order) {
        EmissionRequestDto emissionRequestDto = new EmissionRequestDto();
        emissionRequestDto.setPolicy(policy.getData());
        emissionRequestDto.setCustomer(customer);
        emissionRequestDto.setOrder(order);
        CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
        TemplateRequest templateRequest = new TemplateRequest();
        templateRequest.setEmissionRequestDto(emissionRequestDto);
        String company = emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsuranceCompany();
        String product = emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode();
        logger.infov("Retrieving template for company: {0} and product: {1}", company, product);
        Template template = templateService.getKoPaymentTemplate(company, product);
        TemplateResponseDto templateResponseDto = template.generate(templateRequest);
        communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
        communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
        return communicationManagerDtoRequest;
    }
}
