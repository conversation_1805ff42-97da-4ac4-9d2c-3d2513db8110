package it.yolo.service.V2;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.exception.InvokeOrderEx;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.service.client.V2.InvokeOrderV2;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ServiceOrderV2 {

    @Inject
    InvokeOrderV2 invokeOrderV2;


    @WithSpan("ServiceOrder.update")
    public OrderResponseDto update(OrderManagerRequest request, String id) throws InvokeOrderEx {
        return invokeOrderV2.update(id, request);
    }

    @WithSpan("ServiceOrder.create")
    public OrderResponseDto create(OrderManagerRequest request) throws InvokeOrderEx {
        return invokeOrderV2.create(request);
    }


}
