package it.yolo.service;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyStatesResponse;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.AdpException;
import it.yolo.exception.PolicyException;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import com.fasterxml.jackson.databind.JsonNode;

import it.yolo.client.adp.policy.AdpPolicyClient;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.service.client.InvokePolicy;

import java.time.LocalDateTime;

@RequestScoped
public class ServicePolicy {
    @Inject
    InvokePolicy invokePolicy;
    @Inject
    ServiceCustomer customerService;

    @Inject
    @RestClient
    PolicyClient policyClient;
    @Inject
    @RestClient
    AdpPolicyClient adpPolicyClient;

    @Inject
    JsonWebToken jsonWebToken;

    private static String ACTIVE_STATE="Active";

    public PolicyResponseDto create(String policyCode, String orderIdCode, String startDate,
            String endDate, String stateId) {

        DataPolicyRequestDto dataPolicyRequestDto = new DataPolicyRequestDto();
        dataPolicyRequestDto.setPolicyCode(policyCode);
        dataPolicyRequestDto.setOrderIdCode(orderIdCode);
        dataPolicyRequestDto.setStartDate(startDate);
        dataPolicyRequestDto.setEndDate(endDate);
        dataPolicyRequestDto.setStateId(Long.parseLong(stateId));
        PolicyResponseDto res = invokePolicy.create("Bearer "+jsonWebToken.getRawToken(), dataPolicyRequestDto);
        return res;
    }

    public JsonNode read(String user, String token) {
        CustomerResponseDto customer = customerService.findByNdg(token, user);
        Integer customerId = customer.getData().getId();
        Response res;
        try {
            res = policyClient.get(token, customerId);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        JsonNode policies = res.readEntity(JsonNode.class);
        Response adpRes;
        try{
            adpRes=adpPolicyClient.trasfomrResponse(policies);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        JsonNode transformedPolicies = adpRes.readEntity(JsonNode.class);

        return transformedPolicies;
    }

    public PolicyResponseDto updateAfterEmission(EmissionResponseDto emissionResponseDto, String certficateFileName, String certficateLink,
                                                 String certificateContentType, String productCode, String startDate)
    {
        String token = "Bearer "+jsonWebToken.getRawToken();
        PolicyRequestDto policyRequestDto = new PolicyRequestDto();
        policyRequestDto.setDto(new DataPolicyRequestDto());
        policyRequestDto.getDto().setCertificateFileName(certficateFileName);
        policyRequestDto.getDto().setCertificateLink(certficateLink);
        policyRequestDto.getDto().setCertificateContentType(certificateContentType);
        policyRequestDto.getDto().setPolicyCode(emissionResponseDto.getPolicyResponseDto().getData().getPolicyCode());
        policyRequestDto.getDto().setId(emissionResponseDto.getPolicyResponseDto().getData().getId());
        policyRequestDto.getDto().getProduct().setCode(productCode);
        if (startDate!=null) {
            LocalDateTime date=LocalDateTime.parse(startDate);
            if(date.toLocalDate().isBefore(LocalDateTime.now().toLocalDate().plusDays(1))){
//                set stato attivo
                PolicyStatesResponse responseState=policyClient.getStateByName(token, ACTIVE_STATE).readEntity(PolicyStatesResponse.class);
                policyRequestDto.getDto().setStateId(responseState.getData().getId());
            }
        }
        Response res;
        try {
            res = policyClient.updateAfterEmission(token, policyRequestDto);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto responseDto=res.readEntity(PolicyResponseDto.class);
        return responseDto;
    }

    public Integer getNextPolicy(String productCode, String token){
        Integer code=policyClient.getNextPolicy(token, productCode).readEntity(JsonNode.class).get("data").asInt();
        return code;
    }

}
