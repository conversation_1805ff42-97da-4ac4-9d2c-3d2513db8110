package it.yolo.service;

import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.arag.AragTutelaLegaleKoPayment;
import it.yolo.client.communicationManager.template.yolo.MancatoPagamento;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

@ApplicationScoped
public class TemplateService {

    @Inject
    Logger logger;
    
    private static final String ARAG_COMPANY = "ARAG";
    private static final String TUTELA_LEGALE_FAMIGLIA_PRODUCT = "yolo-tutela-legale-famiglia";
    private static final String TUTELA_LEGALE_PROFESSIONISTI_PRODUCT = "yolo-tutela-legale-professionisti";
    private static final String TUTELA_LEGALE_PMI_PRODUCT = "yolo-tutela-legale-pmi";
    
    @Inject
    private AragTutelaLegaleKoPayment aragTutelaLegaleKoPayment;

    @Inject
    private MancatoPagamento mancatoPagamento;

    private final Map<String, Map<String, Template>> koPaymentTemplates = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        logger.info("Initializing Template Service...");
        try {
            Map<String, Template> mapArag = new HashMap<>();
            mapArag.put(TUTELA_LEGALE_FAMIGLIA_PRODUCT, aragTutelaLegaleKoPayment);
            mapArag.put(TUTELA_LEGALE_PROFESSIONISTI_PRODUCT, aragTutelaLegaleKoPayment);
            mapArag.put(TUTELA_LEGALE_PMI_PRODUCT, aragTutelaLegaleKoPayment);

            koPaymentTemplates.put(ARAG_COMPANY, mapArag);
            logger.infov("Template service successfully initialized with {0} companies", koPaymentTemplates.size());
        } catch (Exception e) {
            logger.error("Failed to initialize Template Service", e);
            throw new RuntimeException("Critical error during Template Service initialization", e);
        }
    }

    public Template getKoPaymentTemplate(String insuranceCompany, String productCode) {
        if (StringUtils.isBlank(insuranceCompany) || StringUtils.isBlank(productCode)) {
            logger.warnv("Invalid input parameters: company={0}, product={1}", insuranceCompany, productCode);
            return mancatoPagamento;
        }

        Map<String, Template> companyTemplates = koPaymentTemplates.get(insuranceCompany);
        if (companyTemplates == null || companyTemplates.get(productCode) == null) {
            logger.debugv("Template not found for company={0}, product={1}", insuranceCompany, productCode);
            return mancatoPagamento;
        }

        return companyTemplates.get(productCode);
    }

}
