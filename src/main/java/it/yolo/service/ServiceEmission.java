package it.yolo.service;

import io.quarkus.arc.All;
import it.yolo.common.ImplExtractor;
import it.yolo.emission.Emission;
import it.yolo.emission.dto.request.CertificateRequestDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.GenerateMacException;
import it.yolo.exception.PGException;
import it.yolo.exception.PolicyException;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.List;

@RequestScoped
public class ServiceEmission {

    @Inject
    @All
    List<Emission> emissions;

    @Inject
    ImplExtractor<Emission> emissionImplExtractor;

    public EmissionResponseDto emissionPolicy(EmissionRequestDto emissionRequestDto) throws PGException, PolicyException, GenerateMacException {
        String emmission = emissionRequestDto.getOrder().getResponse()
                .getProduct().getDataProduct().getConfiguration().getEmission();
        return emissionImplExtractor.getImplementationFromList(emissions, emmission).emission(emissionRequestDto);
    }

    public CertificateResponseDto generateCerticatePolicy(CertificateRequestDto certificateRequestDto, EmissionResponseDto emissionResponseDto){
        String emmission= certificateRequestDto.getProduct().get("data").get("configuration").get("emission").asText();
        return emissionImplExtractor.getImplementationFromList(emissions, emmission).generateCertificate(emissionResponseDto, certificateRequestDto);
    }



















}
