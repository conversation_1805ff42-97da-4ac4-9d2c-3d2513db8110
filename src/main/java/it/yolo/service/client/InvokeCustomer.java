package it.yolo.service.client;


import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.dto.iad.response.ApiResponseDto;
import it.yolo.model.request.OrderManagerRequest;

public interface InvokeCustomer {
    ApiResponseDto update(OrderManagerRequest request, String taxCode);

    CustomerResponseDto findByNdg(String token, String ndg);

    CustomerResponseDto findById(String token,Long id);

    ApiResponseDto updateByNdg(OrderManagerRequest request, String token, String ndg);
}
