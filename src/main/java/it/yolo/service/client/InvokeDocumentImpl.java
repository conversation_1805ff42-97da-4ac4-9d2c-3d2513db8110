package it.yolo.service.client;

import it.yolo.client.document.DocumentClient;
import it.yolo.client.document.dto.DownloadDocumentRequest;
import it.yolo.client.document.dto.DownloadDocumentResponse;
import it.yolo.exception.DocumentException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class InvokeDocumentImpl implements InvokeDocument {
    @Inject
    @RestClient
    DocumentClient documentClient;

    @Override
    public DownloadDocumentResponse downloadLink(DownloadDocumentRequest certificateRequestDto) {
        DownloadDocumentResponse response;
        try {
            response = documentClient.downloadLink(certificateRequestDto).readEntity(DownloadDocumentResponse.class);
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        return response;
    }
}
