package it.yolo.service.client;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.OrderClient;
import it.yolo.client.order.dto.request.OrderRequestDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.common.OrderStateEnum;
import it.yolo.exception.InvokeOrderEx;
import it.yolo.exception.OrderException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.service.ServiceCustomer;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

import java.util.HashMap;
import java.util.Map;

import static it.yolo.costants.Costant.ERROR_MSG;

@RequestScoped
public class InvokeOrderImpl implements InvokeOrder {

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceCustomer serviceCustomer;

    Map<String, String> mapAnagStates;

    @PostConstruct
    void init() {
        mapAnagStates = new HashMap<>();
        mapAnagStates.put("insurance_info", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("address", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("survey", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("payment", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("confirm", OrderStateEnum.COMPLETED.getValue());
    }

    @Override
    public OrderResponseDto create(OrderManagerRequest request) {
        try {
            Response responseAdp = adpOrderClient.trasfomrRequest(request);
            OrderRequestDto requestOrder = responseAdp.readEntity(OrderRequestDto.class);
            String ndg=jsonWebToken.getClaim("username");
            String token = "Bearer " + jsonWebToken.getRawToken();
            CustomerResponseDto customerResponse=serviceCustomer.findByNdg(token, ndg.toUpperCase());
            requestOrder.getDataDtoRequest().setCustomerId(customerResponse.getData().getId());
            requestOrder.getDataDtoRequest().setCustomerCode(customerResponse.getData().getCustomerCode());
            Response responseOrder;
            try{
                responseOrder = orderClient.create(token,requestOrder);
            }catch (Exception e){
                throw new OrderException(e.getMessage());
            }
            OrderResponseDto orderResponseDto = responseOrder.readEntity(OrderResponseDto.class);
            orderResponseDto.getResponse().setCustomer(customerResponse.getData());
            return orderResponseDto;
        } catch (Exception ex) {
            throw new InvokeOrderEx(ERROR_MSG + "errore durante la chiamata al client order"
                    + ex.getCause().toString());
        }
    }

    @Override
    public OrderResponseDto update(String id, OrderManagerRequest request) {
        try {
            request.setOrderCode(id);
            Response responseAdp = adpOrderClient.trasfomrRequest(request);
            String token = "Bearer " + jsonWebToken.getRawToken();
            OrderRequestDto requestOrder = responseAdp.readEntity(OrderRequestDto.class);
            requestOrder.getDataDtoRequest().setAnagState(mapAnagStates.get(request.getState()));
            if (request.getPayment_token()!=null) {
                requestOrder.getDataDtoRequest().setPaymentToken(request.getPayment_token());
            }
            if (request.getPaymentTransactionId()!=null) {
                requestOrder.getDataDtoRequest().setPaymentTransactionId(request.getPaymentTransactionId());
            }
            if(request.getPaymentType() != null){
                requestOrder.getDataDtoRequest().setPaymentType(request.getPaymentType());
            }
            //verifica codice sconto
            if(request.getDiscount()!=null){
                requestOrder.getDataDtoRequest().setDiscount(request.getDiscount());
            }
            if(request.getBillCustomerId()!=null) {
                requestOrder.getDataDtoRequest().setCustomerId(request.getBillCustomerId());
            }
            if(request.getAdditionalProperties().get("quotatorPrice")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setPrice(request.getAdditionalProperties().get("quotatorPrice").toString());
                });
            }
            if(request.getAdditionalProperties().get("quotatorAddons")!=null) {
//                requestOrder.getDataDtoRequest().setCustomerId(request.getBillCustomerId());
            }
            if(request.getAdditionalProperties().get("quotatorPrice")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setPrice(request.getAdditionalProperties().get("quotatorPrice").toString());
                });
            }
            if(request.getAdditionalProperties().get("quotatorAddons")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setQuotation((JsonNode) request.getAdditionalProperties().get("quotatorAddons"));
                });
            }
            Response responseOrder;
            try{
                responseOrder = orderClient.update(token, requestOrder,id);
            } catch (Exception e){
                throw new OrderException(e.getMessage());
            }
            OrderResponseDto orderResponseDto = responseOrder.readEntity(OrderResponseDto.class);
            return orderResponseDto;
        } catch (Exception ex) {
            throw new InvokeOrderEx(ERROR_MSG + "errore durante la chiamata al client order"
                    + ex.getCause().toString());
        }
    }

    @Override
    public OrderResponseDto findByOrderCode(String orderCode) {
        String token = "Bearer " + jsonWebToken.getRawToken();
        Response responseOrder;
        try{
            responseOrder = orderClient.findByOrderCode(token,orderCode);
        } catch (Exception e){
            throw new OrderException(e.getMessage());
        }
        OrderResponseDto orderDto = responseOrder.readEntity(OrderResponseDto.class);
        return orderDto;
    }

    @Override
    public OrderResponseDto updateFailed(String id, OrderManagerRequest request) {



        return null;
    }


}
