package it.yolo.service.client;

import it.yolo.client.adp.customer.AdpCustomerClient;
import it.yolo.client.customer.CustomerClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.dto.iad.request.ApiRequestDto;
import it.yolo.client.dto.iad.response.ApiResponseDto;
import it.yolo.exception.AdpException;
import it.yolo.exception.CustomerEx;
import it.yolo.model.request.OrderManagerRequest;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class InvokeCustomerImpl implements InvokeCustomer {

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    @RestClient
    AdpCustomerClient adpCustomer;


    @Override
    public ApiResponseDto update(OrderManagerRequest request, String taxCode) {
        Response responseAdp=null;
        try {
            responseAdp= adpCustomer.trasfomrRequest(request);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        ApiRequestDto requestCustomer = responseAdp.readEntity(ApiRequestDto.class);
        Response res;
        try {
            res = customerClient.updateByTaxCode(taxCode, requestCustomer);
        }catch (Exception e){
            throw new CustomerEx(e.getMessage());
        }
        ApiResponseDto customerResponse = res.readEntity(ApiResponseDto.class);
        return customerResponse;
    }

    @Override
    public CustomerResponseDto findByNdg(String token, String ndg) {
        Response responseCustomer;
        try {
            responseCustomer= customerClient.findByNdg(token, ndg);
        }catch (Exception e){
            throw new CustomerEx(e.getMessage());
        }
        CustomerResponseDto customerDto = responseCustomer.readEntity(CustomerResponseDto.class);
        return customerDto;
    }

    @Override
    public ApiResponseDto updateByNdg(OrderManagerRequest request, String token, String ndg) {
        Response responseAdp=null;
        try {
            responseAdp= adpCustomer.trasfomrRequest(request);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        ApiRequestDto requestCustomer = responseAdp.readEntity(ApiRequestDto.class);
        Response res;
        try {
            res = customerClient.updateByNdg(ndg, requestCustomer, token);
        }catch (Exception e){
            throw new CustomerEx(e.getMessage());
        }
        ApiResponseDto customerResponse = res.readEntity(ApiResponseDto.class);
        return customerResponse;
    }

    @Override
    public CustomerResponseDto findById(String token,Long id) {
    Response responseCustomer;
        try {
            responseCustomer= customerClient.findById(token,id);
        }catch (Exception e){
            throw new CustomerEx(e.getMessage());
        }
        CustomerResponseDto customerDto = responseCustomer.readEntity(CustomerResponseDto.class);
        return customerDto;
    }
}
