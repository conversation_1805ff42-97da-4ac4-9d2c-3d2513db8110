package it.yolo.service.client.V2;


import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;

public interface InvokePolicyV2 {

    PolicyResponseDto create(String token, DataPolicyRequestDto request);
    PolicyResponseDto updateNumberFailedPayment(String token, PolicyRequestDto request, String subscriptionId);
    PolicyResponseDto updateSuccessfulPayment(String token, PolicyRequestDto request, String subscriptionId);
    PolicyResponseDto readPolicyByCode(String token, String code);
    PolicyResponseDto readPolicyByOrderId(String token, Integer orderId);
    PolicyResponseDto readPolicyByOrderIdActive(String token, Long orderId);
    PolicyResponseDto duplicatePolicy(String token, Integer newOrderId, Integer oldOrderId, Integer newOrderItemId, String policyCode);
}
