package it.yolo.service.client.V2;

import it.yolo.client.policy.v2.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.exception.PolicyException;
import it.yolo.model.request.DuplicatePolicyRequest;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class InvokePolicyImplV2 implements InvokePolicyV2 {

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Override
    public PolicyResponseDto create(String token, DataPolicyRequestDto request) {
        PolicyRequestDto policyRequestDto =new PolicyRequestDto(request);
        Response res;
        try {
            res = policyClient.create(token, policyRequestDto);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto updateNumberFailedPayment(String token, PolicyRequestDto request, String subscriptionId) {
        Response res;
        try {
            res = policyClient.updateNumberFailedPayment(token, request, subscriptionId);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto updateSuccessfulPayment(String token, PolicyRequestDto request, String subscriptionId) {
        Response res;
        try {
            res = policyClient.updateSuccessfulPayment(token, request, subscriptionId);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto readPolicyByCode(String token, String code) {
        Response res;
        try {
            res = policyClient.readPolicyByCode(token, code);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto readPolicyByOrderId(String token, Integer orderId) {
        Response res;
        try {
            res = policyClient.readPolicyByOrderId(token, orderId);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto readPolicyByOrderIdActive(String token, Long orderId) {
        Response res;
        try {
            res = policyClient.readPolicyByOrderIdActive(token, orderId);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }

    @Override
    public PolicyResponseDto duplicatePolicy(String token, Integer newOrderId, Integer oldOrderId, Integer newOrderItemId, String policyCode){
        Response res;
        try {
            res = policyClient.duplicatePolicy(token, new DuplicatePolicyRequest(newOrderId, oldOrderId, newOrderItemId, policyCode));
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }
}
