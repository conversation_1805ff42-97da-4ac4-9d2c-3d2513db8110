package it.yolo.service.client;

import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.request.DataPolicyRequestDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.exception.PolicyException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class InvokePolicyImpl implements InvokePolicy {

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Override
    public PolicyResponseDto create(String token, DataPolicyRequestDto request) {
        PolicyRequestDto policyRequestDto =new PolicyRequestDto(request);
        Response res;
        try {
            res = policyClient.create(token, policyRequestDto);
        }catch (Exception e){
            throw new PolicyException(e.getMessage());
        }
        PolicyResponseDto policyResponseDto = res.readEntity(PolicyResponseDto.class);
        return policyResponseDto;
    }


    @Override
    public PolicyResponseDto action(ObjectNode request){
        return policyClient.action(request).readEntity(PolicyResponseDto.class);
    }
}
