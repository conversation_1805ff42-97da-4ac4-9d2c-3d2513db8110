package it.yolo.service.client;


import it.yolo.client.pg.PGClient;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.emission.dto.request.EmissionRequestV2;
import it.yolo.exception.PGException;
import org.eclipse.microprofile.rest.client.inject.RestClient;


import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;


@RequestScoped
public class InvokePGImpl implements InvokePG {

    @Inject
    @RestClient
    PGClient pgClient;

    @Override
    public PgResponseDto callback(EmissionRequestV2 request) {
        Response res;
        try {
            res = pgClient.callback(request);
        } catch (Exception e) {
            throw new PGException(e.getMessage());
        }
        PgResponseDto pgResponseDto = res.readEntity(PgResponseDto.class);
        return pgResponseDto;
    }

    @Override
    public PgResponseDto receipt(EmissionRequestV2 request) {
        Response res;
        try {
            res = pgClient.receipt(request);
        } catch (Exception e) {
            throw new PGException(e.getMessage());
        }
        PgResponseDto pgResponseDto = res.readEntity(PgResponseDto.class);
        return pgResponseDto;
    }
}
