package it.yolo.service.client;


import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.model.request.OrderManagerRequest;

public interface InvokeOrder {

    OrderResponseDto create(OrderManagerRequest request);

    OrderResponseDto update(String id, OrderManagerRequest request);

    OrderResponseDto findByOrderCode(String orderCode);


    OrderResponseDto updateFailed(String id, OrderManagerRequest request);

}
