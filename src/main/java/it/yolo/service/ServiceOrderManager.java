package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.dto.response.Packet;
import it.yolo.client.order.dto.response.ProductOrderResponse;
import it.yolo.client.packet.PacketClient;
import it.yolo.client.product.ProductClient;
import it.yolo.client.yin.YinClient;
import it.yolo.exception.AdpException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.InsuredEntities;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.workflow.state.*;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;


import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class ServiceOrderManager {
    @Inject
    ServiceOrder serviceOrder;

    @Inject
    InsuranceInfo insuranceInfo;

    @Inject
    Address address;

    @Inject
    Survey survey;

    @Inject
    Payment payment;

    @Inject
    Confirm confirm;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @Inject
    @RestClient
    PacketClient packetClient;

    @Inject
    @RestClient
    YinClient yinClient;

    Map<String, State> mapState;

    private static String STATE_COMPLETE = "confirm" ;

    @ConfigProperty(name = "yin.coreApiKey")
    String coreApiKey;


    @Inject
    Logger logger;
    @PostConstruct
    void init() {
        mapState = new HashMap<>();
        mapState.put("insurance_info", insuranceInfo);
        mapState.put("address", address);
        mapState.put("survey", survey);
        mapState.put("payment", payment);
        mapState.put("confirm",confirm);
    }

    @WithSpan("ServiceOrderManager.create")
    public EmissionManagerResponse createOrder(String version, OrderManagerRequest request) {
        OrderResponseDto responseOrder = serviceOrder.createByVersion(version, request);
        responseOrder.setAdditionalInfo(request);
        responseOrder.setVersion(version);
        Response reponseAdapter;
        try {
            reponseAdapter = adpOrderClient.trasformResponse(responseOrder);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        EmissionManagerResponse orderManageResponse = reponseAdapter.readEntity(EmissionManagerResponse.class);

        // Adding insurance_info_attributes node from request, because yes.
        if (orderManageResponse.getLineItems().size() > 0
                && orderManageResponse.getLineItems().get(0).getInsurance_info() != null
                && request.getOrder().getLineItemsAttributes() != null) {
            orderManageResponse
                    .getLineItems()
                    .get(0)
                    .setInsurance_info(request.getOrder().getLineItemsAttributes().getNode());
        }


        //QUANDO COMBATTI CONTRO UN PRODOTTO E UN FE DI MERDA --> NON GIUDICATEMI MA CASA PURTROPPO FACEVA SCHIFO DA GESTIRE ED ABBIAMO DOVUTO
        // AGIRE DIRETTAMENTE QUI
       if(request.getOrder().getLineItemsAttributes().getNode()!=null && request.getOrder().getLineItemsAttributes().getNode().get("house_attributes")!=null){
           if(orderManageResponse.getLineItems()!=null){
               for(int i =0;i<orderManageResponse.getLineItems().size();i++){
                   Object house = request.getOrder().getLineItemsAttributes().getNode().get("house_attributes");
                   InsuredEntities insuredEntities= new InsuredEntities();
                   insuredEntities.setHouse(house);
                   orderManageResponse.getLineItems().get(i).setInsuredEntities(insuredEntities);
               }
           }
       }


        return orderManageResponse;
    }

    @WithSpan("ServiceOrderManager.update")
    public EmissionManagerResponse updateOrder(String orderCode, OrderManagerRequest request) throws Exception {
        String state = request.getState();

        return mapState.get(state).workflow(request, orderCode);
    }

    @WithSpan("ServiceOrderManager.complete")
    public EmissionManagerResponse complete(String orderCode, OrderManagerRequest request, String channel ) throws Exception {
        String state = request.getState();
        EmissionManagerResponse res = mapState.get(STATE_COMPLETE).workflow(request, orderCode);
        // se emetto completamente inutile bloccare la complete
        if(request.getChannel()!=null &&channel.equalsIgnoreCase("YIN")){
            try{
                logger.info("align order ");
                Response response= yinClient.alignOder(coreApiKey, request.getOrderCode(),request.getVersion());
                logger.info("align end  " + response.readEntity(JsonNode.class).toString());
            }catch (Exception ex){
                logger.info("align error  " + ex.getMessage() + res);
                return res;
            }
        }
        return res;
    }

    @WithSpan("ServiceOrderManager.updateFailed")
    public EmissionManagerResponse updateOrderFailed(String orderCode, String token,String version) throws Exception {
        OrderResponseDto orderResponseDto=serviceOrder.updateOrderFailed(orderCode,token);
        Response responseP;
        if(version.equalsIgnoreCase("v1")){
            responseP=productClient.findById(token, null, Long.valueOf(String.valueOf(orderResponseDto.getResponse().getProductId())));
            orderResponseDto.getResponse().setProduct(responseP.readEntity(ProductOrderResponse.class));
        }else if (version.equalsIgnoreCase("v2")){
            responseP=packetClient.findById(token, orderResponseDto.getResponse().getPacketId());
            orderResponseDto.getResponse().setPacket(responseP.readEntity(Packet.class));
        }
        try {
            orderResponseDto.setVersion(version);
            Response response = adpOrderClient.trasformResponse(orderResponseDto);
            return response.readEntity(EmissionManagerResponse.class);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
    }



}
