package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.dto.iad.response.Packet;
import it.yolo.client.packet.PacketClient;
import it.yolo.client.product.ProductClient;
import it.yolo.client.product.response.dto.ProductResponseDto;
import it.yolo.exception.InvokeOrderEx;
import it.yolo.model.request.OrderManagerRequest;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class ServiceProduct {

    private final static String VERSION_V1_API = "v1";

    @RestClient
    @Inject
    ProductClient productClient;

    @RestClient
    @Inject
    PacketClient packetClient;

    @Inject
    JsonWebToken jsonWebToken;



    @WithSpan("ServiceOrder.findByOrderVersion")
    public ProductResponseDto findByOrderVersion(String version, OrderManagerRequest request) throws InvokeOrderEx {
        Long id=request.getOrder().getLineItemsAttributes().getNode().get("variant_id").asLong();
        if (version.contains(VERSION_V1_API)) {
            Response response = productClient.findById("Bearer " + jsonWebToken.getRawToken(), null, id);
            return response.readEntity(ProductResponseDto.class);
        }
        Response response = packetClient.findById("Bearer " + jsonWebToken.getRawToken(),id.intValue());
        Packet packetResponse= response.readEntity(Packet.class);
        ProductResponseDto productResponseDto=this.packetToProductDto(packetResponse);
        return productResponseDto;
    }

    @WithSpan("ServiceOrder.findByOrderVersion")
    public JsonNode findById(Long id, String xtl) throws InvokeOrderEx {
        Response response = productClient.findById("Bearer " + jsonWebToken.getRawToken(), xtl, id);
        return response.readEntity(JsonNode.class);
    }


    private ProductResponseDto packetToProductDto(Packet packetDtoResponse){
        List<Packet> packets = new ArrayList<>();
        packets.add(packetDtoResponse);
        ProductResponseDto productResponseDto= new ProductResponseDto();
        productResponseDto.setDataProduct(packetDtoResponse.getDataPacket().getProduct());
        productResponseDto.getDataProduct().setPackets(packets);
        return productResponseDto;
    }

}
