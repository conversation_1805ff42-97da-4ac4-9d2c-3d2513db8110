package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.OrderClient;
import it.yolo.client.order.dto.emissionRequest.DataEmissionRequest;
import it.yolo.client.order.dto.emissionRequest.EmissionRequest;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.common.Utility;
import it.yolo.exception.AdpException;
import it.yolo.exception.InvokeOrderEx;
import it.yolo.exception.OrderException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.service.client.InvokeOrder;
import it.yolo.service.client.V2.InvokeOrderV2;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServiceOrder {

    @Inject
    InvokeOrder invokeOrder;

    @Inject
    InvokeOrderV2 invokeOrderV2;

    @Inject
    ServiceCustomer customerService;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;


    private final static String VERSION_V1_API = "v1";

    @WithSpan("ServiceOrder.update")
    public OrderResponseDto update(OrderManagerRequest request, String id) throws InvokeOrderEx {
        return invokeOrder.update(id, request);
    }

    @WithSpan("ServiceOrder.update")
    public OrderResponseDto create(OrderManagerRequest request) throws InvokeOrderEx {
        return invokeOrder.create(request);
    }

    @WithSpan("ServiceOrder.findById")
    public OrderResponseDto findByOrderCode(OrderManagerRequest request) throws InvokeOrderEx {
        return invokeOrder.findByOrderCode(request.getOrderCode());
    }


    @WithSpan("ServiceOrder.updateByVersion")
    public OrderResponseDto updateByVersion(String version, String orderCode, OrderManagerRequest request) throws InvokeOrderEx {
        if (version.contains(VERSION_V1_API)) {
            OrderResponseDto orderResponseApi = invokeOrder.update(orderCode, request);
            return orderResponseApi;
        }
        OrderResponseDto orderResponseApi = invokeOrderV2.update(orderCode, request);
        return orderResponseApi;
    }

    @WithSpan("ServiceOrder.createByVersion")
    public OrderResponseDto createByVersion(String version, OrderManagerRequest request) throws InvokeOrderEx {
        if (version.contains(VERSION_V1_API)) {
            OrderResponseDto orderResponseApi = invokeOrder.create(request);
            return orderResponseApi;
        }
        OrderResponseDto orderResponseApi = invokeOrderV2.create(request);
        return orderResponseApi;
    }

    public JsonNode findOrderCode(String token,String orderCode, String version) {
        if (version.equalsIgnoreCase("v1")) {
            try {
                Response res = orderClient.getdetailsOrder(token,orderCode);
                OrderResponseDto responseOrder= res.readEntity(OrderResponseDto.class);
                responseOrder.setVersion("v1");
                Response resAdp = adpOrderClient.trasformResponse(responseOrder);
                JsonNode transformedOrder = resAdp.readEntity(JsonNode.class);
                return transformedOrder;
            } catch (Exception e) {
                throw new AdpException(e.getMessage());
            }
        }
        try{
            Response res = orderClient.getdetailsOrder(token,orderCode);
            OrderResponseDto responseOrder= res.readEntity(OrderResponseDto.class);
            responseOrder.setVersion("v2");
            Response resAdp = adpOrderClient.trasformResponse(responseOrder);
            JsonNode transformedOrder = resAdp.readEntity(JsonNode.class);
            return transformedOrder;
        }catch (Exception e) {
            throw new AdpException(e.getMessage());
        }
    }

    public JsonNode readByOrderCodeAndNdg(String orderCode, String user, String token) {
        CustomerResponseDto customer = customerService.findByNdg(token, user);
        Integer customerId = customer.getData().getId();
        Response res;
        try {
            res = orderClient.findByOrderCodeAndIdCustomer(token, customerId, orderCode);
        }catch (Exception e){
            throw new OrderException(e.getMessage());
        }
        OrderResponseDto order = res.readEntity(OrderResponseDto.class);
        order.setVersion(order.getResponse().getVersion());
        Response adpRes;
        try{
            adpRes=adpOrderClient.trasformResponse(order);
        }catch (Exception e){
            throw new AdpException(e.getMessage());
        }
        JsonNode transformedOrder = adpRes.readEntity(JsonNode.class);

        return transformedOrder;
    }


    @WithSpan("ServiceOrder.updateOrderFailed")
    public OrderResponseDto updateOrderFailed( String orderCode, String token) throws InvokeOrderEx {
        Response res=orderClient.failed(token,orderCode);
        OrderResponseDto orderResponseDto =res.readEntity(OrderResponseDto.class);
        return orderResponseDto;
    }

    @WithSpan("ServiceOrder.updateEmissionDetail")
    public OrderResponseDto updateEmissionDetail(String token, String orderCode, Integer orderItemId,JsonNode emissionResponse) throws InvokeOrderEx {
        EmissionRequest req = new EmissionRequest();
        DataEmissionRequest data = new DataEmissionRequest();
        req.setDataEmissionRequest(data);
        req.getDataEmissionRequest().setOrderCode(orderCode);
        req.getDataEmissionRequest().setId(orderItemId);
        req.getDataEmissionRequest().setEmission(emissionResponse);
        Response res = orderClient.updateEmissionDetail(token, req);
        OrderResponseDto orderResponseDto = res.readEntity(OrderResponseDto.class);
        return orderResponseDto;
    }

    @WithSpan("ServiceOrder.updateEmissionDetail")
    public OrderResponseDto updateEmissionDetail(String token, String orderCode, Integer orderItemId,String emissionResponse) throws InvokeOrderEx {
        EmissionRequest req = new EmissionRequest();
        DataEmissionRequest data = new DataEmissionRequest();
        req.setDataEmissionRequest(data);
        req.getDataEmissionRequest().setOrderCode(orderCode);
        req.getDataEmissionRequest().setId(orderItemId);

        JsonNode emission = Utility.stringToJsonNode(emissionResponse);
        req.getDataEmissionRequest().setEmission(emission);

        Response res = orderClient.updateEmissionDetail(token, req);
        OrderResponseDto orderResponseDto = res.readEntity(OrderResponseDto.class);
        return orderResponseDto;
    }

}
