package it.yolo.costants;

public class Costant {

    public final static String ERROR_MSG = " MSG: ";
    public final static String ERROR = "{ERROR:}";
    public final static String STACK = "{STACK:}";
    public final static String SDD_PAYMENT_METHOD = "SEPA";
    public final static String STD_ACTION = "PATH_RENEW_POLICY_ACTION";
    public final static String STRIPE = "STRIPE";
    public final static String STUB_LINK_CERTIFCATE="https://yolo.s3.amazonaws.com/yolo/";
    public final static String STUB_CERTIFCATE_NAME="policy.pdf";
    public final static String STUB_CONTENT_TYPE="pdf";
    public final static String ERROR_EXISTING_POLICY="Polizza già esistente";
}
