package it.yolo.common;

import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.List;

@ApplicationScoped
public class ImplExtractor<T> {
    public T getImplementationFromList(List<T> classes, String code){
        for(T clas: classes){
            Annotation annotation=Arrays.stream(clas.getClass().getSuperclass().getAnnotations()).filter(a ->
                    a.annotationType().getSimpleName().equalsIgnoreCase(code))
            .findFirst().orElse(null);
            if(annotation!=null){
                return clas;
            }
        }
        return null;
    }
}
