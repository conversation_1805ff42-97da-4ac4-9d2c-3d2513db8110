package it.yolo.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.emission.dto.request.EmissionRequestDto;
import it.yolo.exception.GenerateMacException;
import org.apache.commons.lang3.StringUtils;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.stream.IntStream;

public class Utility {
    /* RIMOSSA VERTICALIZZAZIONE A SECONDA DEL PRODUCT CODE
    private static String intesaSciGold="ergo-mountain-gold";
    private static String intesaSciSilver="ergo-mountain-silver";
    private static String rca="genertel-rca";*/

    private static char[] LowerCaseAlphabet = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    private static char[] UpperCaseAlphabet = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    private static int[] Numbers = IntStream.range(1, 101).toArray();

    public static String generateInternalPolicyCode(String prefix , String orderCode, EmissionRequestDto emissionRequestDto){

        if(prefix==null){
            prefix=orderCode;
        }

        /* RIMOSSA VERTICALIZZAZIONE A SECONDA DEL PRODUCT CODE
        else{
            if(intesaSciGold.equalsIgnoreCase(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode()) ||
                    intesaSciSilver.equalsIgnoreCase(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode())){
                prefix=prefix+emissionRequestDto.getOrder().getResponse().getId();
            }else if(rca.equalsIgnoreCase(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode())){
                int i=prefix.length()+String.valueOf(emissionRequestDto.getOrder().getResponse().getId()).length();
                for (;i<12; i++){
                   prefix+="0";
                }
                prefix+=String.valueOf(emissionRequestDto.getOrder().getResponse().getId());
            }
        }*/

        String policyNumber=prefix;
        return policyNumber;
    }


    public static String getPlanId(String planId,String paymentFrequncyRequest){
        String plainIdFinal=planId;
        if(planId.contains("_")){
            if(paymentFrequncyRequest.contains("M")){
                plainIdFinal=StringUtils.substringBefore(planId, "_");
            }else {
                plainIdFinal=StringUtils.substringAfter(planId, "_");
            }
        }
        return plainIdFinal;
    }

    public static String generateIdTrans() {
        String idTrans = StringUtils.EMPTY;
        Random rndm = new Random();
        while (idTrans.length() < 20) {
            Integer random = rndm.nextInt(1, 4);
            if (random == 1) {
                int inter = (int) Array.get(Numbers, rndm.nextInt(99));
                idTrans = idTrans.concat(String.valueOf(inter));
            } else if (random == 2) {
                char character = (char) Array.get(UpperCaseAlphabet, rndm.nextInt(26));
                idTrans = idTrans.concat(String.valueOf(character));
            } else {
                char character = (char) Array.get(LowerCaseAlphabet, rndm.nextInt(26));
                idTrans = idTrans.concat(String.valueOf(character));
            }
        }
        idTrans = idTrans.substring(0, 20);
        return idTrans;
    }

    public static String generateMac(String idTrans, String key) throws GenerateMacException {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256"));
            idTrans="IDTRANS=".concat(idTrans);
            mac.update(idTrans.getBytes());
            byte[] array = mac.doFinal();
            return DatatypeConverter.printHexBinary(array);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException | InvalidKeyException e) {
            throw new GenerateMacException();
        }
    }

    public static JsonNode stringToJsonNode(String string) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.readValue(string, JsonNode.class);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String localDateTimeToFormattedDate(String date) {
        String datePattern = "dd/MM/yyyy";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(datePattern);
        LocalDateTime formattedDate = LocalDateTime.parse(date);
        return dateTimeFormatter.format(formattedDate);
    }
}
