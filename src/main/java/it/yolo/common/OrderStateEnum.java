package it.yolo.common;

public enum OrderStateEnum {
    COMPLETED("Completed", "Completed"),
    ELABORATION("Elaboration", "Elaboration");
    private final String key;
    private final String value;
    OrderStateEnum(String key, String value){
        this.key = key; this.value = value;
    }
    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}