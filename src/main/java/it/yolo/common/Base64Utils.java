package it.yolo.common;

import it.yolo.client.communicationManager.dto.Attachments;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;
import java.util.List;

public class Base64Utils {

    public static void addAttachment(List<Attachments> attachmentsList, String link, String attachmentName, String contentType) {
        attachmentsList.add(new Attachments(attachmentName, contentType, getBase64FromLink(link)));
    }

    public static String getBase64FromLink(String link){
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            URL url = new URL(link);

            InputStream in = new BufferedInputStream(url.openStream());
            in.transferTo(Base64.getEncoder().wrap(out));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return out.toString();
    }
}
