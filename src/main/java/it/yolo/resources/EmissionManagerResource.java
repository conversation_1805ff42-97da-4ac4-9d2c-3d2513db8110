package it.yolo.resources;

import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.dto.request.PolicyRequestDto;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.model.request.EmissionManagerRequest;
import it.yolo.model.request.UpdateWarrantiesRequest;
import it.yolo.service.ServiceEmissionManager;
import it.yolo.service.ServiceOrder;
import it.yolo.service.ServicePolicy;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestResponse;
import com.fasterxml.jackson.databind.JsonNode;
import org.jboss.logging.Logger;
import io.quarkus.security.Authenticated;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.model.error.ErrorResponse;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.model.response.EmissionManagerResponse;
import it.yolo.service.ServiceOrderManager;
import it.yolo.service.V2.ServicePolicyV2;
import java.util.Map;

@Path("/v1")
public class EmissionManagerResource {
    private static final Logger LOGGER = Logger.getLogger(EmissionManagerResource.class);

    @Inject
    ServiceOrderManager orderManager;

    @Inject
    ServiceEmissionManager emissionManager;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    ServicePolicyV2 servicePolicyV2;

    @Inject
    ServicePolicy servicePolicy;

    @Inject
    JsonWebToken jsonWebToken;

    @POST
    @Path("/emission-manager")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "create a order from  orchestration ")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and create  ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<EmissionManagerResponse> createOrder(
            @HeaderParam("Authorization") String token,
            OrderManagerRequest req) {
        EmissionManagerResponse res;
        res = orderManager.createOrder(req.getVersion(), req);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @PUT
    @Path("emission-manager/{code}")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "update a order from orchestration")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and update order ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<EmissionManagerResponse> updateOrder(
            @HeaderParam("Authorization") String token,
            String code,
            OrderManagerRequest req
    ) throws Exception {
        EmissionManagerResponse res;
        req.setBearerToken(token);
        res = orderManager.updateOrder(code, req);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @PUT
    @Path("emission-manager/complete/{code}")
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "update a order complete from orchestration")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and update order complete ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<EmissionManagerResponse> updateOrderComplete(
            @HeaderParam("Authorization") String token,
            String code,
            OrderManagerRequest req
    ) throws Exception {
        EmissionManagerResponse res;
        req.setBearerToken(token);
        req.setState("confirm");
        res = orderManager.complete(code, req,req.getChannel());
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @GET
    @Path("policy")
    @ResponseStatus(HttpStatus.OK)
    @Authenticated
    @Operation(summary = "get all policy from the user provided through jwt")
    @APIResponse(responseCode = ResponseCode.OK, description = "policies retrieved", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<JsonNode> getPolicy(@HeaderParam("Authorization") String token) throws Exception {
        String ndgCode = jsonWebToken.getClaim("username");
        LOGGER.info("username: " + ndgCode + " jwt: " + token);
        JsonNode res = servicePolicy.read(ndgCode.toUpperCase(), "Bearer " + jsonWebToken.getRawToken());
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @GET
    @Path("emission-manager/{orderCode}")
    @ResponseStatus(HttpStatus.OK)
    @Authenticated
    @Operation(summary = "get an order from the user provided through jwt")
    @APIResponse(responseCode = ResponseCode.OK, description = "policies retrieved", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<JsonNode> getOrderByCodeAndCustomerId(
            @HeaderParam("Authorization") String token,
            String orderCode) throws Exception {
        String ndgCode = jsonWebToken.getClaim("username");
        LOGGER.info("username: " + ndgCode + " jwt: " + token);
        JsonNode res = serviceOrder.readByOrderCodeAndNdg(orderCode, ndgCode.toUpperCase(), "Bearer " + jsonWebToken.getRawToken());
        return RestResponse.ResponseBuilder.ok(res).build();
    }



    @GET
    @Path("emission-manager/code/{orderCode}/{version}")
    @ResponseStatus(HttpStatus.OK)
    @Authenticated
    @Operation(summary = "get an order from the user provided through jwt")
    @APIResponse(responseCode = ResponseCode.OK, description = "policies retrieved", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<JsonNode> getByOrderCode(
            @HeaderParam("Authorization") String token,
            String orderCode,
            String version) throws Exception {
        LOGGER.info("orderCode: " + orderCode);
        JsonNode res = serviceOrder.findOrderCode(token,orderCode,version);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @GET
    @Path("emission-manager/pending-payments")
    @ResponseStatus(HttpStatus.OK)
    @Authenticated
    @Operation(summary = "get an order from the user provided through jwt")
    @APIResponse(responseCode = ResponseCode.OK, description = "payments retrieved", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<Map<String, JsonNode>> getPayments(
            @HeaderParam("Authorization") String token) throws Exception {
        Map<String, JsonNode> res = emissionManager.getPayments(token);
        return RestResponse.ResponseBuilder.ok(res).build();
    }





    @PUT
    @Path("failed/{orderCode}")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "update a order from orchestration")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and update order ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<EmissionManagerResponse> updateOrderFailed(
            @HeaderParam("Authorization") String token,
            @HeaderParam("Version") String Version,
            String orderCode
    ) throws Exception {
        EmissionManagerResponse res;
        res = orderManager.updateOrderFailed(orderCode, token,Version);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @POST
    @Path("/emission-manager/{code}")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "create a order from  orchestration ")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and create  ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<?> emissionManager(
            @HeaderParam("Authorization") String token, String code,
            EmissionManagerRequest req) throws Exception {
        OrderResponseDto res;
        res = emissionManager.emission(req, token, code);
        if(res!=null) {
            return RestResponse.ResponseBuilder.ok(res).build();
        } else {
            return RestResponse.ResponseBuilder.serverError().build();
        }
    }


    @POST
    @Path("/emission-manager/proxy/{code}")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "create a order from  orchestration ")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and create  ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<?> emissionProxy(
            @HeaderParam("Authorization") String token, String code, EmissionManagerRequest request) throws Exception {
        JsonNode res;
        res = emissionManager.proxyEmission(token, code, request);
        if(res!=null) {
            return RestResponse.ResponseBuilder.ok(res).build();
        } else {
            return RestResponse.ResponseBuilder.serverError().build();
        }
    }


    @POST
    @Path("/emission-manager/generateCertificate/{code}")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "create a order from  orchestration ")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and create  ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<?> generateCertificate(
            @HeaderParam("Authorization") String token, String code
          ) throws Exception {
        JsonNode res;
        res = emissionManager.generateCertificate(token, code);
        if(res!=null) {
            return RestResponse.ResponseBuilder.ok(res).build();
        } else {
            return RestResponse.ResponseBuilder.serverError().build();
        }
    }

    @POST
    @Path("/emission-manager/update-warranties")
    @Authenticated
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "create a order from  orchestration ")
    @APIResponse(responseCode = ResponseCode.OK, description = "orchestration and create  ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<JsonNode> updateWarranties(
            @HeaderParam("Authorization") String token, UpdateWarrantiesRequest req) {
        JsonNode res = emissionManager.updateWarranties(req, token);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @POST
    @Path("/emission-manager/success-recurring/{subscriptionId}")
    @Authenticated
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "increase number_failed_payment value if payment failed ")
    @APIResponse(responseCode = ResponseCode.OK, description = "increase number_failed_payment value", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<PolicyResponseDto> updateSuccessfulPayment(
            @HeaderParam("Authorization") String token, String subscriptionId,
            PolicyRequestDto req) {
        PolicyResponseDto res;
        res = servicePolicyV2.updateSuccessfulPayment(req, token, subscriptionId);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @POST
    @Path("/emission-manager/number-failed-payment/{subscriptionId}")
    @Authenticated
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "increase number_failed_payment value if payment failed ")
    @APIResponse(responseCode = ResponseCode.OK, description = "increase number_failed_payment value", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<PolicyResponseDto> updateNumberFailedPayment(
            @HeaderParam("Authorization") String token, String subscriptionId,
            PolicyRequestDto req) {
        PolicyResponseDto res;
        res = servicePolicyV2.updateNumberFailedPayment(req, token, subscriptionId);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @POST
    @Path("/emission-manager/sendCertificate/{policyCode}")
    @Authenticated
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "send policy's certificate ")
    @APIResponse(responseCode = ResponseCode.OK, description = "send policy's certificate", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<PgResponseDto> sendCertificate(
            @HeaderParam("Authorization") String token, String policyCode,
            PolicyRequestDto req) {
        PgResponseDto res;
        res = emissionManager.sendCertificate(token, policyCode);
        return RestResponse.ResponseBuilder.ok(res).build();
    }

    @POST
    @Path("/emission-manager/sendReceipt/{policyCode}")
    @Authenticated
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "send policy's certificate ")
    @APIResponse(responseCode = ResponseCode.OK, description = "send policy's certificate", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<PgResponseDto> sendReceipt(
            @HeaderParam("Authorization") String token, String policyCode,
            PolicyRequestDto req) {
        PgResponseDto res;
        res = emissionManager.sendReceipt(token, policyCode);
        return RestResponse.ResponseBuilder.ok(res).build();
    }
}
