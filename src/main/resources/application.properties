quarkus.banner.enabled=false
quarkus.http.port=8080
tenant = cnpsi-polonia
quarkus.profile=dev
base-url = https://${tenant}-api.${quarkus.profile}.yoloassicurazioni.it

# OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_hWop30twv

# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false

# OIDC Configuration - disabilita introspection per evitare errori
# Disabilita completamente l'introspection
quarkus.oidc.token.introspection-required=false
# Permette solo JWT (disabilita token opachi)
quarkus.oidc.token.allow-opaque-token-introspection=false
quarkus.oidc.token.allow-jwt-introspection=false
# Forza l'uso di JWT
quarkus.oidc.token.verify-access-token-with-user-info=false

# JWT configuration
mp.jwt.verify.publickey.location=publicKey.pem
mp.jwt.decrypt.key.location=keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm=RSA_OAEP_256
smallrye.jwt.key-encryption-algorithm=RSA-OAEP-256

# Logging
quarkus.opentelemetry.enabled=true
quarkus.log.level=DEBUG
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{2.}] (%t) %s%e%n

# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Order Manager API
quarkus.smallrye-openapi.info-version=1.0.0

# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true

#ORDER REST CLIENT
quarkus.rest-client.iad-order.url=${base-url}/iad-order
quarkus.rest-client.iad-order.scope=javax.inject.Singleton

#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=${base-url}/iad-customer
quarkus.rest-client.iad-customer.scope=javax.inject.Singleton

#ADAPTER ORDER REST CLIENT
quarkus.rest-client.adp-order.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-order.scope=javax.inject.Singleton

#ADAPTER POLICY REST CLIENT
quarkus.rest-client.adp-policy.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-policy.scope=javax.inject.Singleton

#ADAPTER CUSTOMER REST CLIENT
quarkus.rest-client.adp-customer.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-customer.scope=javax.inject.Singleton

#SURVEY ADAPTER REST CLIENT
quarkus.rest-client.adp-survey.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-survey.scope=javax.inject.Singleton

#ADP-PAYMENT REST CLIENT
quarkus.rest-client.adp-payment.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-payment.scope=javax.inject.Singleton

#SURVEY REST CLIENT
quarkus.rest-client.iad-survey.url=${base-url}/iad-survey
quarkus.rest-client.iad-survey.scope=javax.inject.Singleton

#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=http://localhost:3000
quarkus.rest-client.provider-gateway.scope=javax.inject.Singleton
quarkus.rest-client.provider-gateway.read-timeout=60000
quarkus.rest-client.provider-gateway.connect-timeout=60000

#ADP PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.adp-pg.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-pg.scope=javax.inject.Singleton

#IAD-POLICY REST CLIENT
quarkus.rest-client.iad-policy.url=${base-url}/iad-policy
quarkus.rest-client.iad-policy.scope=javax.inject.Singleton

#BRAIN-TREE REST CLIENT
quarkus.rest-client.brain-tree.url=http://payment-braintree-deploy:8080
quarkus.rest-client.brain-tree.scope=javax.inject.Singleton

#IAD-PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=${base-url}/iad-product
quarkus.rest-client.iad-product.scope=javax.inject.Singleton

# IAD-TOKEN
quarkus.rest-client.iad-token.url=${base-url}/iad-token
quarkus.rest-client.iad-token.scope=javax.inject.Singleton

#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=${base-url}/iad-document
quarkus.rest-client.iad-document.scope=javax.inject.Singleton

#DOCUMENT-MANAGER REST CLIENT
quarkus.rest-client.iad-document-manager.url=${base-url}/iad-document-manager
quarkus.rest-client.iad-document-manager.scope=javax.inject.Singleton

#COMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=${base-url}/comunication-manager
quarkus.rest-client.communication-manager.scope=javax.inject.Singleton

#GUP REST CLIENT
quarkus.rest-client.no-pay.url=${base-url}/no-payment
quarkus.rest-client.no-pay.scope=javax.inject.Singleton

#PRICING REST CLIENT
quarkus.rest-client.iad-pricing.url=${base-url}/iad-pricing
quarkus.rest-client.iad-pricing.scope=javax.inject.Singleton

#REST CLIENT LOG
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=1000000
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG

#YIN CLIENT
quarkus.rest-client.yin-api.url=https://tim-intermediari-staging.yolo-insurance.com
quarkus.rest-client.yin-api.scope=javax.inject.Singleton

#ALIGN-ORDER REST CLIENT
quarkus.rest-client.align-order.url=https://intermediari-sys.yolo-insurance.com/api/orders/yep/${yin.api.key}
quarkus.rest-client.align-order.scope=javax.inject.Singleton

#ORDER-MANAGER REST CLIENT
quarkus.rest-client.iad-order-manager.url=http://iad-order-manager-deploy:8080
quarkus.rest-client.iad-order-manager.scope=javax.inject.Singleton

#PAYMENT-MANAGER REST CLIENT
quarkus.rest-client.payment-manager.url=${base-url}/iad-payment-manager
quarkus.rest-client.payment-manager.scope=javax.inject.Singleton

#PAYMENT-STRIPE REST CLIENT
quarkus.rest-client.payment-stripe.url=${base-url}/payment-stripe
quarkus.rest-client.payment-stripe.scope=javax.inject.Singleton

#CORE APY KEY YIN
yin.coreApiKey=AG444rLtxVQ2Z96bAfX3

# Open Telemetry
quarkus.opentelemetry.tracer.enabled=false

#BUSINESS PROP
emission.password=P4ssword.01
emission.username=max.uggeri
tenant.name=cnpsi-polonia

#YIN PROP
key.mac=7D00CE17-638B-490F-BDB9-1782E165D17B
cc.mail=<EMAIL>
tch.usr=tchusr29c23d122s
technical.users=technical-users,intermediary-users
yin.api.key=GX38Gn4ug9v8729AuDXs
sub.pay.mock.plainId=1
