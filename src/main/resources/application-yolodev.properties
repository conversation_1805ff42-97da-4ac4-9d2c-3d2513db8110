#ORDER REST CLIENT
#quarkus.rest-client.iad-order.url=http://localhost:8082
quarkus.rest-client.iad-order.url=https://yolo-api.dev.yoloassicurazioni.it/iad-order
#ORDER REST CLIENT
quarkus.rest-client.iad-token.url=https://yolo-api.yoloassicurazioni.it/iad-token
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=https://yolo-api.dev.yoloassicurazioni.it/iad-customer
#quarkus.rest-client.iad-customer.url=http://localhost:8080
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=https://intesa-api.dev.yoloassicurazioni.it/iad-policy
quarkus.rest-client.iad-policy.url=https://yolo-api.dev.yoloassicurazioni.it/iad-policy
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=http://localhost:3001
#quarkus.rest-client.iad-document.url=https://yolo-api.dev.yoloassicurazioni.it/iad-document
#SURVEY REST CLIENT
quarkus.rest-client.iad-survey.url=https://yolo-api.yoloassicurazioni.it/latest/survey
#SURVEY ADAPTER REST CLIENT
quarkus.rest-client.adp-survey.url=https://yolo-api.dev.yoloassicurazioni.it/iad-adp
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=http://127.0.0.1:3000
#quarkus.rest-client.provider-gateway.url=https://yolo-api.dev.yoloassicurazioni.it/providers-gateway
#BRAIN-TREE REST CLIENT
quarkus.rest-client.brain-tree.url=https://yolo-api.dev.yoloassicurazioni.it/payment-braintree
#ORDER-MANAGER REST CLIENT
quarkus.rest-client.iad-order-manager.url=http://localhost:8081
#GUP REST CLIENT
quarkus.rest-client.no-pay.url=https://yolo-api.dev.yoloassicurazioni.it/no-payment
#PAYMENT MANAGER
quarkus.rest-client.payment-manager.url=http://localhost:8088
#ADP-PAYMENT REST CLIENT
quarkus.rest-client.adp-payment.url=https://yolo-api.dev.yoloassicurazioni.it/iad-payment
#ALIGN-ORDER REST CLIENT
quarkus.rest-client.align-order.url=https://intermediari-sys.yolo-insurance.com/api/orders/yep/${yin.api.key}
#IAD-PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=https://yolo-api.dev.yoloassicurazioni.it/iad-product
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://yolo-api.dev.yoloassicurazioni.it/comunication-manager
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
yin.api.key=GX38Gn4ug9v8729AuDXs
tenant.name=yolo