quarkus.banner.enabled=true
quarkus.http.port=8080
quarkus.smallrye-jwt.enabled=false
# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Catalog API
quarkus.smallrye-openapi.info-version=1.0.0
# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
#Quarkus Config ssl
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true
# LOGGING /LEVEL
quarkus.log.level=DEBUG
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
#PRODUCT REST CLIENT
quarkus.rest-client.order.url=http://iad-order:8080/
quarkus.rest-client.order.scope=javax.inject.Singleton
#CUSTOMER REST CLIENT
quarkus.rest-client.customers.url=http://iad-customer:8080/
quarkus.rest-client.customers.scope=javax.inject.Singleton
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=1024
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG

