#ORDER REST CLIENT
quarkus.rest-client.iad-order.url=http://localhost:8082
#quarkus.rest-client.iad-order.url=http://timcustomer-api.dev.yoloassicurazioni.it/iad-order
#ORDER REST CLIENT
quarkus.rest-client.iad-token.url=https://timcustomer-api.yoloassicurazioni.it/iad-token
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-customer
#quarkus.rest-client.iad-customer.url=http://localhost:8080
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=https://intesa-api.dev.yoloassicurazioni.it/iad-policy
quarkus.rest-client.iad-policy.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-policy
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-document
#SURVEY REST CLIENT
quarkus.rest-client.iad-survey.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-survey
#SURVEY ADAPTER REST CLIENT
quarkus.rest-client.adp-survey.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-adp
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-providers-gateway
#BRAIN-TREE REST CLIENT
quarkus.rest-client.brain-tree.url=https://timcustomer-api.dev.yoloassicurazioni.it/payment-braintree
#GUP REST CLIENT
quarkus.rest-client.no-pay.url=https://timcustomer-api.dev.yoloassicurazioni.it/no-payment
#ADP-PAYMENT REST CLIENT
quarkus.rest-client.adp-payment.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-payment
#IAD-PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-product
#ORDER-MANAGER REST CLIENT
quarkus.rest-client.iad-order-manager.url=http://localhost:8081
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-comunication-manager
#ALIGN-ORDER REST CLIENT
quarkus.rest-client.align-order.url=https://intermediari-sys.yolo-insurance.com/api/orders/yep/${yin.api.key}
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_YiFnMdwnM
yin.api.key=GX38Gn4ug9v8729AuDXs
tenant.name=timcustomer
