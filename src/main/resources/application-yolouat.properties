#ORDER REST CLIENT
#quarkus.rest-client.iad-order.url=http://localhost:8082
quarkus.rest-client.iad-order.url=http://yolo-api.preprod.yoloassicurazioni.it/latest/order
#ORDER REST CLIENT
quarkus.rest-client.iad-token.url=https://yolo-api.yoloassicurazioni.it/iad-token
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/customer
#quarkus.rest-client.iad-customer.url=http://localhost:8080
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=https://intesa-api.preprod.yoloassicurazioni.it/iad-policy
quarkus.rest-client.iad-policy.url=https://yolo-api.yoloassicurazioni.it/latest/policy
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=https://yolo-api.yoloassicurazioni.it/latest/document
#ORDER-MANAGER REST CLIENT
quarkus.rest-client.iad-order-manager.url=http://localhost:8081
#PAYMENT MANAGER
quarkus.rest-client.payment-manager.url=http://localhost:8088
#SURVEY REST CLIENT
quarkus.rest-client.iad-survey.url=https://yolo-api.yoloassicurazioni.it/latest/survey
#SURVEY ADAPTER REST CLIENT
quarkus.rest-client.adp-survey.url=https://yolo-api.preprod.yoloassicurazioni.it/iad-adp
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/providers-gateway
#BRAIN-TREE REST CLIENT
quarkus.rest-client.brain-tree.url=https://yolo-api.preprod.yoloassicurazioni.it/payment-braintree
#GUP REST CLIENT
quarkus.rest-client.no-pay.url=https://yolo-api.preprod.yoloassicurazioni.it/no-payment
#ADP-PAYMENT REST CLIENT
quarkus.rest-client.adp-payment.url=https://yolo-api.preprod.yoloassicurazioni.it/iad-payment
#IAD-PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/product
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/comunication
#ALIGN-ORDER REST CLIENT
quarkus.rest-client.align-order.url=https://intermediari-sys.yolo-insurance.com/api/orders/yep/${yin.api.key}
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_pdGOaemaj
yin.api.key=nxQUjz9Xk75S34JEwJeE
tenant.name=yolo