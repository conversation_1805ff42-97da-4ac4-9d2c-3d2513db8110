package it.yolo.v3.resource;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import io.restassured.http.ContentType;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.client.CustomerClientV3;
import it.yolo.v3.client.OrderClientV3;
import it.yolo.v3.client.PolicyClientV3;
import it.yolo.v3.client.ProductClientV3;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.*;
import it.yolo.v3.repository.StatusRepositoryV3;
import org.awaitility.Awaitility;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.inject.Inject;
import java.time.Duration;
import java.time.LocalDateTime;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * Test di integrazione per EmissionResourceV3
 */
@QuarkusTest
class EmissionResourceV3Test {
    
    private static final String TEST_ORDER_CODE = "TEST_ORDER_001";
    private static final String TEST_TOKEN = "Bearer test-token";
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    @InjectMock
    @RestClient
    OrderClientV3 orderClient;
    
    @InjectMock
    @RestClient
    CustomerClientV3 customerClient;
    
    @InjectMock
    @RestClient
    ProductClientV3 productClient;
    
    @InjectMock
    @RestClient
    PolicyClientV3 policyClient;
    
    @BeforeEach
    void setUp() {
        // Pulisce il repository prima di ogni test
        statusRepository.clear();
        
        // Reset dei mock
        Mockito.reset(orderClient, customerClient, productClient, policyClient);
    }
    
    @Test
    void testSuccessfulEmission() {
        // Arrange
        setupSuccessfulMocks();
        
        EmissionRequestV3 request = new EmissionRequestV3(
            "CARD",
            "test-payment-token",
            "10",
            "test-subscription-id",
            12345
        );
        
        // Act & Assert
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", TEST_TOKEN)
            .body(request)
        .when()
            .post("/api/v3/emissions/{orderCode}", TEST_ORDER_CODE)
        .then()
            .statusCode(201)
            .body("orderCode", equalTo(TEST_ORDER_CODE))
            .body("policyNumber", notNullValue())
            .header("Location", containsString("/api/v3/emissions/" + TEST_ORDER_CODE + "/status"));
        
        // Verifica che lo stato sia stato inizializzato
        Awaitility.await()
            .atMost(Duration.ofSeconds(2))
            .until(() -> statusRepository.exists(TEST_ORDER_CODE));
    }
    
    @Test
    void testEmissionWithInvalidRequest() {
        // Arrange
        EmissionRequestV3 invalidRequest = new EmissionRequestV3(
            "CARD",
            null, // token nullo - dovrebbe fallire la validazione
            "10",
            "test-subscription-id",
            12345
        );
        
        // Act & Assert
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", TEST_TOKEN)
            .body(invalidRequest)
        .when()
            .post("/api/v3/emissions/{orderCode}", TEST_ORDER_CODE)
        .then()
            .statusCode(400);
    }
    
    @Test
    void testEmissionOrderNotFound() {
        // Arrange
        Mockito.when(orderClient.findByOrderCodeUnchecked(anyString(), anyString()))
            .thenReturn(Uni.createFrom().failure(new RuntimeException("Order not found")));
        
        EmissionRequestV3 request = new EmissionRequestV3(
            "CARD",
            "test-payment-token",
            "10",
            "test-subscription-id",
            12345
        );
        
        // Act & Assert
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", TEST_TOKEN)
            .body(request)
        .when()
            .post("/api/v3/emissions/{orderCode}", TEST_ORDER_CODE)
        .then()
            .statusCode(404);
    }
    
    @Test
    void testDuplicateEmission() {
        // Arrange
        setupSuccessfulMocks();
        statusRepository.init(TEST_ORDER_CODE); // Simula ordine già in elaborazione
        
        EmissionRequestV3 request = new EmissionRequestV3(
            "CARD",
            "test-payment-token",
            "10",
            "test-subscription-id",
            12345
        );
        
        // Act & Assert
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", TEST_TOKEN)
            .body(request)
        .when()
            .post("/api/v3/emissions/{orderCode}", TEST_ORDER_CODE)
        .then()
            .statusCode(409);
    }
    
    @Test
    void testGetStatusSuccess() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        statusRepository.updateWithPolicyNumber(TEST_ORDER_CODE, "POL123456");
        
        // Act & Assert
        given()
        .when()
            .get("/api/v3/emissions/{orderCode}/status", TEST_ORDER_CODE)
        .then()
            .statusCode(200)
            .body("orderCode", equalTo(TEST_ORDER_CODE))
            .body("policyNumber", equalTo("POL123456"))
            .body("status", equalTo("EMITTED"));
    }
    
    @Test
    void testGetStatusNotFound() {
        // Act & Assert
        given()
        .when()
            .get("/api/v3/emissions/{orderCode}/status", "NON_EXISTENT_ORDER")
        .then()
            .statusCode(404)
            .body("status", equalTo("NOT_FOUND"));
    }
    
    @Test
    void testCanEmitSuccess() {
        // Arrange
        setupSuccessfulMocks();
        
        // Act & Assert
        given()
            .header("Authorization", TEST_TOKEN)
        .when()
            .get("/api/v3/emissions/{orderCode}/can-emit", TEST_ORDER_CODE)
        .then()
            .statusCode(200)
            .body("orderCode", equalTo(TEST_ORDER_CODE))
            .body("canEmit", equalTo(true));
    }
    
    @Test
    void testGetOrderInfoSuccess() {
        // Arrange
        setupSuccessfulMocks();
        
        // Act & Assert
        given()
            .header("Authorization", TEST_TOKEN)
        .when()
            .get("/api/v3/emissions/{orderCode}/info", TEST_ORDER_CODE)
        .then()
            .statusCode(200)
            .body("orderCode", equalTo(TEST_ORDER_CODE))
            .body("customerId", notNullValue())
            .body("productId", notNullValue())
            .body("emissionType", notNullValue())
            .body("canEmit", notNullValue());
    }
    
    @Test
    void testCompleteAsyncFlow() {
        // Arrange
        setupSuccessfulMocks();
        
        EmissionRequestV3 request = new EmissionRequestV3(
            "CARD",
            "test-payment-token",
            "10",
            "test-subscription-id",
            12345
        );
        
        // Act - Avvia l'emissione
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", TEST_TOKEN)
            .body(request)
        .when()
            .post("/api/v3/emissions/{orderCode}", TEST_ORDER_CODE)
        .then()
            .statusCode(201);
        
        // Assert - Verifica che lo stato evolva correttamente
        Awaitility.await()
            .atMost(Duration.ofSeconds(5))
            .pollInterval(Duration.ofMillis(500))
            .until(() -> {
                var status = statusRepository.getStatus(TEST_ORDER_CODE);
                return status.status() == EmissionStatusV3.EMITTED;
            });
        
        // Verifica lo stato finale tramite API
        given()
        .when()
            .get("/api/v3/emissions/{orderCode}/status", TEST_ORDER_CODE)
        .then()
            .statusCode(200)
            .body("status", equalTo("EMITTED"))
            .body("policyNumber", notNullValue());
    }
    
    private void setupSuccessfulMocks() {
        // Mock OrderClient
        OrderResponseDtoV3 orderResponse = createMockOrderResponse();
        Mockito.when(orderClient.findByOrderCodeUnchecked(anyString(), anyString()))
            .thenReturn(Uni.createFrom().item(orderResponse));
        
        // Mock CustomerClient
        CustomerResponseDtoV3 customerResponse = createMockCustomerResponse();
        Mockito.when(customerClient.findById(anyString(), any(Long.class)))
            .thenReturn(Uni.createFrom().item(customerResponse));
        
        // Mock ProductClient
        ProductResponseDtoV3 productResponse = createMockProductResponse();
        Mockito.when(productClient.findById(anyString(), anyString(), any(Long.class)))
            .thenReturn(Uni.createFrom().item(productResponse));
        
        // Mock PolicyClient
        PolicyResponseDtoV3 policyResponse = createMockPolicyResponse();
        Mockito.when(policyClient.create(anyString(), any()))
            .thenReturn(Uni.createFrom().item(policyResponse));
    }
    
    private OrderResponseDtoV3 createMockOrderResponse() {
        DataOrderResponseDtoV3 data = new DataOrderResponseDtoV3(
            1, TEST_ORDER_CODE, "PROD001", null, null, 1, null, 1, 1, 1,
            null, 100.0, "system", "system", null, LocalDateTime.now(),
            LocalDateTime.now().plusYears(1), null, "2024-01-01", "2024-01-01",
            null, null, null, null, null, "INSURANCE"
        );
        return new OrderResponseDtoV3(data, null, "0", "1.0", null, null);
    }
    
    private CustomerResponseDtoV3 createMockCustomerResponse() {
        DataCustomerResponseDtoV3 data = new DataCustomerResponseDtoV3(
            1, "CUST001", null, null, "Mario", "Rossi", "1990-01-01",
            "Roma", "Italy", "Lazio", "Laurea", 1, 1, "NDG001", "Active",
            1, 1, 1, 1, "RM", "Roma", "****************", "M", "Via Roma 1",
            "1", "Roma", "PERSON", "Italy", "00100", "RM", "<EMAIL>",
            null, "1234567890", null, "it", "PERSON", "2024-01-01", "2024-01-01",
            "RM", null, "Acme Corp"
        );
        return new CustomerResponseDtoV3(data, null);
    }
    
    private ProductResponseDtoV3 createMockProductResponse() {
        ProductConfigurationV3 config = new ProductConfigurationV3(
            "internal", "internal", "INT", true
        );
        DataProductResponseV3 data = new DataProductResponseV3(
            1, "PROD001", "Test Product", "2024-01-01", false, null, null, null,
            null, 100.0, "Test Product Description", null, 65, false, true,
            false, 10000.0, false, null, "conditions", null, "logo.png", 1,
            18, "info", true, "conditions", "description", "title", null,
            "STANDARD", config
        );
        return new ProductResponseDtoV3(data);
    }
    
    private PolicyResponseDtoV3 createMockPolicyResponse() {
        DataPolicyResponseV3 data = new DataPolicyResponseV3(
            1L, "POL123456", "2024-01-01", "2025-01-01", null, null, 1L,
            "EXT001", null, null, null, null, null, null, null, "2024-01-01",
            "2024-01-01", 1L, true, null, null, null, null, null, null,
            "ACTIVE", null, false, false, null, null, "Policy Name",
            null, null, true, true, "SUB001"
        );
        return new PolicyResponseDtoV3(data);
    }
}
