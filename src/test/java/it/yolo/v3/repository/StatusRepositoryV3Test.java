package it.yolo.v3.repository;

import io.quarkus.test.junit.QuarkusTest;
import it.yolo.v3.dto.response.EmissionStatusDetailV3;
import it.yolo.v3.dto.response.EmissionStatusV3;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test per StatusRepositoryV3
 */
@QuarkusTest
class StatusRepositoryV3Test {
    
    @Inject
    StatusRepositoryV3 statusRepository;
    
    private static final String TEST_ORDER_CODE = "TEST_ORDER_001";
    private static final String TEST_POLICY_NUMBER = "POL123456";
    
    @BeforeEach
    void setUp() {
        statusRepository.clear();
    }
    
    @Test
    void testInit() {
        // Act
        statusRepository.init(TEST_ORDER_CODE);
        
        // Assert
        assertTrue(statusRepository.exists(TEST_ORDER_CODE));
        EmissionStatusDetailV3 status = statusRepository.getStatus(TEST_ORDER_CODE);
        assertEquals(TEST_ORDER_CODE, status.orderCode());
        assertNull(status.policyNumber());
        assertEquals(EmissionStatusV3.PENDING, status.status());
        assertNotNull(status.updatedAt());
    }
    
    @Test
    void testUpdateWithPolicyNumber() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        
        // Act
        statusRepository.updateWithPolicyNumber(TEST_ORDER_CODE, TEST_POLICY_NUMBER);
        
        // Assert
        EmissionStatusDetailV3 status = statusRepository.getStatus(TEST_ORDER_CODE);
        assertEquals(TEST_POLICY_NUMBER, status.policyNumber());
        assertEquals(EmissionStatusV3.EMITTED, status.status());
    }
    
    @Test
    void testUpdateState() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        
        // Act
        statusRepository.updateState(TEST_ORDER_CODE, EmissionStatusV3.CERTIFICATE_IN_PROGRESS);
        
        // Assert
        EmissionStatusDetailV3 status = statusRepository.getStatus(TEST_ORDER_CODE);
        assertEquals(EmissionStatusV3.CERTIFICATE_IN_PROGRESS, status.status());
        assertEquals("Generazione certificato in corso", status.details());
    }
    
    @Test
    void testUpdateStateWithCustomDetails() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        String customDetails = "Custom status message";
        
        // Act
        statusRepository.updateState(TEST_ORDER_CODE, EmissionStatusV3.COMPLETE, customDetails);
        
        // Assert
        EmissionStatusDetailV3 status = statusRepository.getStatus(TEST_ORDER_CODE);
        assertEquals(EmissionStatusV3.COMPLETE, status.status());
        assertEquals(customDetails, status.details());
    }
    
    @Test
    void testUpdateStateToFailed() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        String errorMessage = "Test error message";
        
        // Act
        statusRepository.updateStateToFailed(TEST_ORDER_CODE, errorMessage);
        
        // Assert
        EmissionStatusDetailV3 status = statusRepository.getStatus(TEST_ORDER_CODE);
        assertEquals(EmissionStatusV3.FAILED, status.status());
        assertTrue(status.details().contains(errorMessage));
    }
    
    @Test
    void testGetStatusNotFound() {
        // Act
        EmissionStatusDetailV3 status = statusRepository.getStatus("NON_EXISTENT_ORDER");
        
        // Assert
        assertEquals("NON_EXISTENT_ORDER", status.orderCode());
        assertNull(status.policyNumber());
        assertEquals(EmissionStatusV3.NOT_FOUND, status.status());
        assertEquals("Order not found", status.details());
    }
    
    @Test
    void testExists() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        
        // Act & Assert
        assertTrue(statusRepository.exists(TEST_ORDER_CODE));
        assertFalse(statusRepository.exists("NON_EXISTENT_ORDER"));
    }
    
    @Test
    void testRemove() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        assertTrue(statusRepository.exists(TEST_ORDER_CODE));
        
        // Act
        statusRepository.remove(TEST_ORDER_CODE);
        
        // Assert
        assertFalse(statusRepository.exists(TEST_ORDER_CODE));
    }
    
    @Test
    void testSize() {
        // Arrange
        assertEquals(0, statusRepository.size());
        
        // Act
        statusRepository.init(TEST_ORDER_CODE);
        statusRepository.init("ANOTHER_ORDER");
        
        // Assert
        assertEquals(2, statusRepository.size());
    }
    
    @Test
    void testClear() {
        // Arrange
        statusRepository.init(TEST_ORDER_CODE);
        statusRepository.init("ANOTHER_ORDER");
        assertEquals(2, statusRepository.size());
        
        // Act
        statusRepository.clear();
        
        // Assert
        assertEquals(0, statusRepository.size());
        assertFalse(statusRepository.exists(TEST_ORDER_CODE));
    }
    
    @Test
    void testConcurrentAccess() {
        // Test thread-safety con accessi concorrenti
        String orderCode1 = "ORDER_1";
        String orderCode2 = "ORDER_2";
        
        // Simula accessi concorrenti
        Thread thread1 = new Thread(() -> {
            statusRepository.init(orderCode1);
            statusRepository.updateState(orderCode1, EmissionStatusV3.CERTIFICATE_IN_PROGRESS);
        });
        
        Thread thread2 = new Thread(() -> {
            statusRepository.init(orderCode2);
            statusRepository.updateState(orderCode2, EmissionStatusV3.POST_PROCESSING);
        });
        
        // Act
        thread1.start();
        thread2.start();
        
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            fail("Thread interrupted");
        }
        
        // Assert
        assertTrue(statusRepository.exists(orderCode1));
        assertTrue(statusRepository.exists(orderCode2));
        assertEquals(EmissionStatusV3.CERTIFICATE_IN_PROGRESS, statusRepository.getStatus(orderCode1).status());
        assertEquals(EmissionStatusV3.POST_PROCESSING, statusRepository.getStatus(orderCode2).status());
    }
}
