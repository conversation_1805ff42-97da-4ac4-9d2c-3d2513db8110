package it.yolo.v3.service;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import io.smallrye.mutiny.Uni;
import it.yolo.v3.dto.request.EmissionRequestV3;
import it.yolo.v3.dto.response.*;
import it.yolo.v3.strategy.EmissionStrategyFactoryV3;
import it.yolo.v3.strategy.EmissionStrategyV3;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.inject.Inject;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * Test per PolicyServiceV3
 */
@QuarkusTest
class PolicyServiceV3Test {
    
    @Inject
    PolicyServiceV3 policyService;
    
    @InjectMock
    EmissionStrategyFactoryV3 strategyFactory;
    
    @InjectMock
    EmissionStrategyV3 mockStrategy;
    
    @BeforeEach
    void setUp() {
        Mockito.reset(strategyFactory, mockStrategy);
    }
    
    @Test
    void testCanEmitProductWithValidConfiguration() {
        // Arrange
        ProductResponseDtoV3 product = createValidProduct();
        Mockito.when(strategyFactory.isStrategyAvailable(anyString())).thenReturn(true);
        
        // Act
        boolean canEmit = policyService.canEmitProduct(product);
        
        // Assert
        assertTrue(canEmit);
    }
    
    @Test
    void testCanEmitProductWithInvalidConfiguration() {
        // Arrange
        ProductResponseDtoV3 product = createInvalidProduct();
        
        // Act
        boolean canEmit = policyService.canEmitProduct(product);
        
        // Assert
        assertFalse(canEmit);
    }
    
    @Test
    void testGetEmissionTypeInternal() {
        // Arrange
        ProductResponseDtoV3 product = createValidProduct();
        
        // Act
        String emissionType = policyService.getEmissionType(product);
        
        // Assert
        assertEquals("internal", emissionType);
    }
    
    @Test
    void testGetEmissionTypeDefault() {
        // Arrange
        ProductResponseDtoV3 product = createProductWithoutConfiguration();
        
        // Act
        String emissionType = policyService.getEmissionType(product);
        
        // Assert
        assertEquals("internal", emissionType); // default
    }
    
    @Test
    void testValidateEmissionDataSuccess() {
        // Arrange
        String orderCode = "TEST_ORDER";
        OrderResponseDtoV3 order = createValidOrder(orderCode);
        CustomerResponseDtoV3 customer = createValidCustomer();
        ProductResponseDtoV3 product = createValidProduct();
        
        Mockito.when(strategyFactory.isStrategyAvailable(anyString())).thenReturn(true);
        
        // Act & Assert
        assertDoesNotThrow(() -> {
            policyService.validateEmissionData(orderCode, order, customer, product)
                .await().indefinitely();
        });
    }
    
    @Test
    void testValidateEmissionDataWithNullOrder() {
        // Arrange
        String orderCode = "TEST_ORDER";
        CustomerResponseDtoV3 customer = createValidCustomer();
        ProductResponseDtoV3 product = createValidProduct();
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            policyService.validateEmissionData(orderCode, null, customer, product)
                .await().indefinitely();
        });
    }
    
    @Test
    void testValidateEmissionDataWithOrderCodeMismatch() {
        // Arrange
        String orderCode = "TEST_ORDER";
        OrderResponseDtoV3 order = createValidOrder("DIFFERENT_ORDER");
        CustomerResponseDtoV3 customer = createValidCustomer();
        ProductResponseDtoV3 product = createValidProduct();
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            policyService.validateEmissionData(orderCode, order, customer, product)
                .await().indefinitely();
        });
    }
    
    private ProductResponseDtoV3 createValidProduct() {
        ProductConfigurationV3 config = new ProductConfigurationV3(
            "internal", "internal", "INT", true
        );
        DataProductResponseV3 data = new DataProductResponseV3(
            1, "PROD001", "Test Product", "2024-01-01", false, null, null, null,
            null, 100.0, "Test Product Description", null, 65, false, true,
            false, 10000.0, false, null, "conditions", null, "logo.png", 1,
            18, "info", true, "conditions", "description", "title", null,
            "STANDARD", config
        );
        return new ProductResponseDtoV3(data);
    }
    
    private ProductResponseDtoV3 createInvalidProduct() {
        return new ProductResponseDtoV3(null);
    }
    
    private ProductResponseDtoV3 createProductWithoutConfiguration() {
        DataProductResponseV3 data = new DataProductResponseV3(
            1, "PROD001", "Test Product", "2024-01-01", false, null, null, null,
            null, 100.0, "Test Product Description", null, 65, false, true,
            false, 10000.0, false, null, "conditions", null, "logo.png", 1,
            18, "info", true, "conditions", "description", "title", null,
            "STANDARD", null
        );
        return new ProductResponseDtoV3(data);
    }
    
    private OrderResponseDtoV3 createValidOrder(String orderCode) {
        DataOrderResponseDtoV3 data = new DataOrderResponseDtoV3(
            1, orderCode, "PROD001", null, null, 1, null, 1, 1, 1,
            null, 100.0, "system", "system", null, LocalDateTime.now(),
            LocalDateTime.now().plusYears(1), null, "2024-01-01", "2024-01-01",
            null, null, null, null, null, "INSURANCE"
        );
        return new OrderResponseDtoV3(data, null, "0", "1.0", null, null);
    }
    
    private CustomerResponseDtoV3 createValidCustomer() {
        DataCustomerResponseDtoV3 data = new DataCustomerResponseDtoV3(
            1, "CUST001", null, null, "Mario", "Rossi", "1990-01-01",
            "Roma", "Italy", "Lazio", "Laurea", 1, 1, "NDG001", "Active",
            1, 1, 1, 1, "RM", "Roma", "****************", "M", "Via Roma 1",
            "1", "Roma", "PERSON", "Italy", "00100", "RM", "<EMAIL>",
            null, "1234567890", null, "it", "PERSON", "2024-01-01", "2024-01-01",
            "RM", null, "Acme Corp"
        );
        return new CustomerResponseDtoV3(data, null);
    }
}
