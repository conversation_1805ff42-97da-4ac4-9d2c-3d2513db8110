package it.yolo.v3;

import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.Matchers.greaterThan;
import static org.awaitility.Awaitility.await;
import java.time.Duration;

/**
 * Test di integrazione completi per l'API V3 di emissione.
 *
 * Copre tutti gli scenari principali:
 * - Emissione con successo
 * - Gestione errori
 * - Stati asincroni
 * - Integrazione end-to-end
 */
@QuarkusTest
@DisplayName("API V3 Emission Integration Tests")
public class EmissionV3IntegrationTest {

    private static final String VALID_TOKEN = "Bearer test-token-valid";
    private static final String INVALID_TOKEN = "Bearer test-token-invalid";

    @Nested
    @DisplayName("Emission Endpoint Tests")
    class EmissionEndpointTests {

        @Test
        @DisplayName("Should emit policy successfully with valid data")
        public void testSuccessfulEmission() {
            String orderCode = "TEST_ORDER_SUCCESS_" + System.currentTimeMillis();

            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(201)
                .header("Location", containsString("/api/v3/emissions/" + orderCode + "/status"))
                .body("orderCode", is(orderCode))
                .body("policyNumber", notNullValue())
                .body("policyNumber", startsWith("INT")); // Internal emission prefix
        }

        @Test
        @DisplayName("Should return 401 when authorization token is missing")
        public void testEmissionWithoutToken() {
            String orderCode = "TEST_ORDER_NO_TOKEN_" + System.currentTimeMillis();

            given()
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(401);
        }

        @Test
        @DisplayName("Should return 400 when order code is invalid")
        public void testEmissionWithInvalidOrderCode() {
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/")
                .then()
                .statusCode(404); // Path not found
        }

        @Test
        @DisplayName("Should handle duplicate emission requests")
        public void testDuplicateEmission() {
            String orderCode = "TEST_ORDER_DUPLICATE_" + System.currentTimeMillis();

            // Prima emissione
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(201);

            // Seconda emissione dello stesso ordine
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(409); // Conflict - già emessa
        }
    }

    @Nested
    @DisplayName("Status Endpoint Tests")
    class StatusEndpointTests {

        @Test
        @DisplayName("Should return emission status for existing order")
        public void testStatusRetrieval() {
            String orderCode = "TEST_ORDER_STATUS_" + System.currentTimeMillis();

            // Prima emetti una polizza
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(201);

            // Poi verifica lo stato
            given()
                .when()
                .get("/api/v3/emissions/" + orderCode + "/status")
                .then()
                .statusCode(200)
                .body("orderCode", is(orderCode))
                .body("policyNumber", notNullValue())
                .body("state", anyOf(is("EMITTED"), is("CERTIFICATE_IN_PROGRESS"),
                                   is("COMMUNICATION_IN_PROGRESS"), is("COMPLETE")))
                .body("updatedAt", notNullValue());
        }

        @Test
        @DisplayName("Should return 404 for non-existent order status")
        public void testStatusNotFound() {
            String nonExistentOrder = "NON_EXISTENT_ORDER_" + System.currentTimeMillis();

            given()
                .when()
                .get("/api/v3/emissions/" + nonExistentOrder + "/status")
                .then()
                .statusCode(404);
        }

        @Test
        @DisplayName("Should track status progression through background tasks")
        public void testStatusProgression() {
            String orderCode = "TEST_ORDER_PROGRESSION_" + System.currentTimeMillis();

            // Emetti la polizza
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(201);

            // Verifica che lo stato progredisca attraverso le fasi
            await()
                .atMost(Duration.ofSeconds(10))
                .pollInterval(Duration.ofMillis(500))
                .until(() -> {
                    String status = given()
                        .when()
                        .get("/api/v3/emissions/" + orderCode + "/status")
                        .then()
                        .statusCode(200)
                        .extract()
                        .path("state");

                    return "COMPLETE".equals(status) || "FAILED".equals(status);
                });
        }
    }

    @Nested
    @DisplayName("Performance Tests")
    class PerformanceTests {

        @Test
        @DisplayName("Should respond within 500ms P95 requirement")
        public void testResponseTime() {
            String orderCode = "TEST_ORDER_PERF_" + System.currentTimeMillis();

            long startTime = System.currentTimeMillis();

            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(201)
                .time(lessThan(500L)); // Verifica che risponda in meno di 500ms

            long responseTime = System.currentTimeMillis() - startTime;
            System.out.println("Response time: " + responseTime + "ms");
        }

        @Test
        @DisplayName("Should handle concurrent emissions")
        public void testConcurrentEmissions() {
            // Test di carico con emissioni concorrenti
            for (int i = 0; i < 5; i++) {
                String orderCode = "TEST_ORDER_CONCURRENT_" + i + "_" + System.currentTimeMillis();

                given()
                    .header("Authorization", VALID_TOKEN)
                    .contentType(ContentType.JSON)
                    .body("{}")
                    .when()
                    .post("/api/v3/emissions/" + orderCode)
                    .then()
                    .statusCode(201);
            }
        }
    }

    @Nested
    @DisplayName("Health Check Tests")
    class HealthCheckTests {

        @Test
        @DisplayName("Should report healthy status")
        public void testHealthCheck() {
            given()
                .when()
                .get("/q/health/ready")
                .then()
                .statusCode(200)
                .body("status", is("UP"))
                .body("checks.find { it.name == 'emission-v3' }.status", is("UP"));
        }

        @Test
        @DisplayName("Should report liveness")
        public void testLivenessCheck() {
            given()
                .when()
                .get("/q/health/live")
                .then()
                .statusCode(200)
                .body("status", is("UP"));
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle service unavailable scenarios")
        public void testServiceUnavailable() {
            // Test con token che simula servizio non disponibile
            String orderCode = "TEST_ORDER_UNAVAILABLE_" + System.currentTimeMillis();

            given()
                .header("Authorization", "Bearer service-unavailable-token")
                .contentType(ContentType.JSON)
                .body("{}")
                .when()
                .post("/api/v3/emissions/" + orderCode)
                .then()
                .statusCode(anyOf(is(500), is(503)));
        }

        @Test
        @DisplayName("Should validate request data")
        public void testRequestValidation() {
            given()
                .header("Authorization", VALID_TOKEN)
                .contentType(ContentType.JSON)
                .body("invalid json")
                .when()
                .post("/api/v3/emissions/TEST_ORDER_INVALID")
                .then()
                .statusCode(400);
        }
    }
}
