# Configurazione per i test V3

# Configurazioni generali emissione V3
emission.v3.enabled=true
emission.v3.max-concurrent-emissions=10
emission.v3.timeout-seconds=5

# Configurazioni emissione interna
emission.internal.policy-prefix=TEST_INT
emission.internal.enabled=true

# Configurazioni emissione esterna
emission.external.policy-prefix=TEST_EXT
emission.external.enabled=true
emission.external.timeout-ms=1000
emission.external.retry-attempts=1

# Configurazioni task in background
emission.background.certificate-enabled=true
emission.background.email-enabled=true
emission.background.document-upload-enabled=true
emission.background.max-retry-attempts=1
emission.background.retry-delay-seconds=1

# Configurazioni certificati
emission.certificate.format=PDF
emission.certificate.template=test
emission.certificate.language=it

# Configurazioni email
email.service.enabled=true
email.from.address=<EMAIL>
email.from.name=YOLO Test
email.templates.emission-confirmation=test_emission_confirmation

# Configurazioni logging
emission.logging.detailed=true
emission.logging.performance=true

# Configurazioni monitoraggio
emission.monitoring.metrics-enabled=true
emission.monitoring.health-check-enabled=true

# Configurazioni client REST (mock per i test)
quarkus.rest-client.iad-order.url=http://localhost:8080/mock-order
quarkus.rest-client.iad-customer.url=http://localhost:8080/mock-customer
quarkus.rest-client.iad-product.url=http://localhost:8080/mock-product
quarkus.rest-client.iad-policy.url=http://localhost:8080/mock-policy
quarkus.rest-client.iad-document.url=http://localhost:8080/mock-document
quarkus.rest-client.iad-document-manager.url=http://localhost:8080/mock-document-manager
quarkus.rest-client.provider-gateway.url=http://localhost:8080/mock-pg

# Configurazioni database per test (se necessario)
quarkus.datasource.db-kind=h2
quarkus.datasource.jdbc.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1
quarkus.hibernate-orm.database.generation=drop-and-create

# Configurazioni logging per test
quarkus.log.level=INFO
quarkus.log.category."it.yolo.v3".level=DEBUG

# Disabilita alcune funzionalità per i test
quarkus.micrometer.enabled=false
quarkus.health.enabled=true
