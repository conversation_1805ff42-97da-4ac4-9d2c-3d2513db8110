# iad-emission-manager

## Description

This service isn't meant for mapping any entity.
Its business logic is common to all the tenants, and it's only purpose it's making orchestration.

## Exposed Path

### /v1

- POST `/v1/emission-manager`

**description**: this endpoint creates an order from orchestration;
it takes an OrderManagerRequest object, and the following steps are performed by the service:
1) create order by version, v1 or v2, depending on the version specified in the request object
2) the OrderResponseDto returned by the preceding step is now passed to the node adapter (iad-adp)
3) if the line items attributes of the request's order object are not null, and the insurance info of the first line item of the emission manager response is not null, the line items attributes json node of the request's order is added to the insurance info of the first line item of the emission manager response.
4) if the line items attributes of the request's order object are not null, and the house_attributes contained in these line items attributes are not null, for every line item of the emission manager response is created a new insured entities object, and this object is added to the line item of the emission manager response.
5) the emission manager response object is finally returned by the endpoint in the response. 

**header parameters**: Authorization token

**request**:
```
{
  "state": "address",
  "order": {
    //Order fields...
  },
  "version": "v1",
  "payment_token": "xyz123",
  "discount": "10%",
  "type": "online",
  "payment_method_description": "Credit Card",
  "bearerToken": "abc456",
  "orderCode": "ORD789",
  "paymentTransactionId": 123,
  "paymentToken": 456,
  "payment_type": "Credit Card",
  "addons": {} oggetto json node addons,
  "bill_customer_id": 789,
  "fieldToRecover": {} oggetto Object fieldToRecover,
  "channel": "web",
  "customerId": 987
}
```

- POST `/v1/emission-manager/{code}`

**description**: This endpoint allows you to perform a new emission, providing an Authorization token, an emision manager request object (in the request body) and an order code.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"orderCode": "ORDER789",
		"discount": "15%",
		"paymentType": "CREDIT_CARD",
		"paymentTransactionId": 456,
		"subscriptionId": "SUB123",
		"paymentToken": "TOKEN789"
	}
}
```

- POST `/v1/emission-manager/number-failed-payment/{subscriptionId}`

**description**: This endpoint allows you to update the value of the number of failed payments associated with a policy identified by the parameter {subscriptionId}. after that, the customer and the order are retrieved and an email is sent by the communication manager.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"id": 1,
		"policyCode": "POL123",
		"orderIdCode": "ORDER456",
		"subscriptionId": "SUB789",
		"product": {
			//product fields...
		},
		"packet": {
			//packet fields...
		},
		"customer": {
			//customer fields...
		},
		"startDate": "2023-01-01",
		"endDate": "2023-12-31",
		"stateId": 101,
		"payment": {
			//payment fields...
		},
		"certificateLink": "/path/to/certificate",
		"certificateFileName": "certificate.pdf",
		"certificateContentType": "application/pdf",
		"type": "TypeA",
		"insurancePremium": 1500.0,
		"quantity": 2,
		"name": "Policy Holder",
		"orderItemId": 123,
		"number_failed_payment": 2,
		"next_billing_date": "2023-02-15T14:30:00"
	}
}
```

- POST `/v1/emission-manager/sendCertificate/{policyCode}`

**description**: This endpoint allows you to send the certificate of a policy identified by the policyCode parameter. The user must provide an authorization token to authenticate and send the requested data. an emission request is sent to the providers gateway.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"id": 1,
		"policyCode": "POL123",
		"orderIdCode": "ORDER456",
		"subscriptionId": "SUB789",
		"product": {
			//product fields...
		},
		"packet": {
			//packet fields...
		},
		"customer": {
			//customer fields...
		},
		"startDate": "2023-01-01",
		"endDate": "2023-12-31",
		"stateId": 101,
		"payment": {
			//payment fields...
		},
		"certificateLink": "/path/to/certificate",
		"certificateFileName": "certificate.pdf",
		"certificateContentType": "application/pdf",
		"type": "TypeA",
		"insurancePremium": 1500.0,
		"quantity": 2,
		"name": "Policy Holder",
		"orderItemId": 123,
		"number_failed_payment": 2,
		"next_billing_date": "2023-02-15T14:30:00"
	}
}
```

- POST `/v1/emission-manager/sendReceipt/{policyCode}`

**description**: This endpoint allows you to send the receipt of a policy identified by the policyCode parameter. The user must provide an authorization token to authenticate and send the requested data. an emission request is sent to the providers gateway.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"id": 1,
		"policyCode": "POL123",
		"orderIdCode": "ORDER456",
		"subscriptionId": "SUB789",
		"product": {
			//product fields...
		},
		"packet": {
			//packet fields...
		},
		"customer": {
			//customer fields...
		},
		"startDate": "2023-01-01",
		"endDate": "2023-12-31",
		"stateId": 101,
		"payment": {
			//payment fields...
		},
		"certificateLink": "/path/to/certificate",
		"certificateFileName": "certificate.pdf",
		"certificateContentType": "application/pdf",
		"type": "TypeA",
		"insurancePremium": 1500.0,
		"quantity": 2,
		"name": "Policy Holder",
		"orderItemId": 123,
		"number_failed_payment": 2,
		"next_billing_date": "2023-02-15T14:30:00"
	}
}
```

- POST `/v1/emission-manager/success-recurring/{subscriptionId}`

**description**: This endpoint is designed to handle the reset of the number_failed_payment value to 0 upon success of a recurring payment associated with a subscription. The number of failed payments is set to 0 in the context of policy management, and the next billing date, if not null, is moved to the following month.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"id": 1,
		"policyCode": "POL123",
		"orderIdCode": "ORDER456",
		"subscriptionId": "SUB789",
		"product": {
			//product fields...
		},
		"packet": {
			//packet fields...
		},
		"customer": {
			//customer fields...
		},
		"startDate": "2023-01-01",
		"endDate": "2023-12-31",
		"stateId": 101,
		"payment": {
			//payment fields...
		},
		"certificateLink": "/path/to/certificate",
		"certificateFileName": "certificate.pdf",
		"certificateContentType": "application/pdf",
		"type": "TypeA",
		"insurancePremium": 1500.0,
		"quantity": 2,
		"name": "Policy Holder",
		"orderItemId": 123,
		"number_failed_payment": 2,
		"next_billing_date": "2023-02-15T14:30:00"
	}
}
```

- POST `/v1/emission-manager/update-warranties`

**description**: This endpoint allows you to update the warranties of an order through orchestration. the order client is called for the GET /v3/code/{order_code} and the POST /v3/order/duplicated-order; finally, the POST /v2/quote of the iad-pricing is performed and the response is returned.

**header parameters**: Authorization token

**request**:
```
{
  "orderCode": "ORDER123",
  "packetId": 456,
  "instance": {} oggetto json node instance
}
```

- GET `/v1/emission-manager/{orderCode}`

**description**: This endpoint allows you to retrieve information of a specific order associated with a user through the provided JWT token. The user is identified by the customer code (NDG) included in the JWT token; the customer is retrieved by ndg, the order is retrieved by order code and customer id, and the order response dto is transformed by the adapter in node, and returned in the response.

**header parameters**: Authorization token

- GET `/v1/emission-manager/code/{orderCode}/{version}`

**header parameters**: Authorization token

**description**: This endpoint allows you to retrieve information of a specific order based on its provided code and version. The user is identified through the JWT token provided in the "Authorization" header parameter. the adapter is called before returning the response.

- GET `/v1/policy`

**description**: This endpoint allows you to retrieve all policies by customer id; the customer is retrieved by ndg, which is extracted from the json web token. the adapter is called before returning the response.

**header parameters**: Authorization token

- PUT `/v1/emission-manager/{code}`

**description**: This endpoint allows you to update an order from an orchestration process. a different order workflow will be performed, depending on the state specified in the request JSON (it can be one of the following: "address", "confirm", "insurance_info", "payment", "survey").

**header parameters**: Authorization token

**request**:
```
{
  "state": "insurance_info",
  "order": {
    //Order fields...
  },
  "version": "v1",
  "payment_token": "TOKEN123",
  "discount": "10%",
  "type": "ONLINE",
  "payment_method_description": "Credit Card",
  "bearerToken": "BEARERTOKEN789",
  "orderCode": "ORDER456",
  "paymentTransactionId": 789,
  "paymentToken": 456,
  "payment_type": "CREDIT_CARD",
  "addons": {},
  "bill_customer_id": 567,
  "fieldToRecover": null,
  "channel": "WEB",
  "customerId": 101
}
```

- PUT `/v1/emission-manager/complete/{code}`

**description**: This endpoint allows an order status to be updated to "complete" by an orchestration process. The user must provide an authorization token in the header to authenticate and send the request data.

**header parameters**: Authorization token

**request**:
```
{
  "state": "confirm",
  "order": {
    // ... altre proprietà della classe Order
  },
  "version": "1.0",
  "payment_token": "TOKEN123",
  "discount": "10%",
  "type": "ONLINE",
  "payment_method_description": "Credit Card",
  "bearerToken": "BEARERTOKEN789",
  "orderCode": "ORDER456",
  "paymentTransactionId": 789,
  "paymentToken": 456,
  "payment_type": "CREDIT_CARD",
  "addons": {},
  "bill_customer_id": 567,
  "fieldToRecover": null,
  "channel": "WEB",
  "customerId": 101
}
```

- PUT `/v1/failed/{orderCode}`

**description**: This endpoint allows you to report from an orchestration process that a particular order has failed. This is useful in the context of order management, allowing you to properly record and manage situations where an order cannot be processed successfully.

**header parameters**:
1) Authorization token
2) Version (it can be "v1" or "v2")

## External Communication

This ms communicates with:

- Adp ms (the adapter written in Node). the adp client interfaces in the package it.yolo.client are currently declared DEPRECATED, but still present in the microservice. the adapter in Node is used for:
  1) Customer
  2) Order
  3) Payment (for Braintree and No Pay)
  4) Providers Gateway
  5) Policy
  6) Survey
- Communication Manager ms;
- Customer ms;
- Document ms;
- Token ms;
- Order ms;
- Survey ms;
- Product ms;
- Payment Braintree ms;
- No Payment ms;
- Providers Gateway ms;
- Policy ms;
- Pricing ms;
- YIN:
  1) YIN API (https://tim-intermediari-staging.yolo-insurance.com)
  2) ALIGN ORDER (https://intermediari-sys.yolo-insurance.com/api/orders/yep/${yin.api.key})

## Entity

No entity is handled by this ms.

## Swagger URL

By clicking on this link, you can get to the swagger documentation to be able to use the microservice endpoints:

http://tenant-api.yoloassicurazioni.it/iad-emission-manager/openapi/swagger