#!/bin/bash

# Script per testare l'API V3 di emissione
# Uso: ./test-api-v3.sh [base-url] [token]

set -e

BASE_URL=${1:-"http://localhost:8080"}
TOKEN=${2:-"test-token"}
ORDER_CODE="TEST_$(date +%s)"

echo "🚀 Testing API V3 Emission"
echo "Base URL: $BASE_URL"
echo "Order Code: $ORDER_CODE"
echo "Token: $TOKEN"
echo ""

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare risultati
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

# Test 1: Health Check
echo -e "${BLUE}1. Testing Health Check...${NC}"
curl -s -f "$BASE_URL/q/health/ready" > /dev/null
print_result $? "Health check passed"

# Test 2: Emissione Polizza
echo -e "${BLUE}2. Testing Policy Emission...${NC}"
EMISSION_RESPONSE=$(curl -s -w "%{http_code}" \
    -X POST "$BASE_URL/api/v3/emissions/$ORDER_CODE" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{}')

HTTP_CODE="${EMISSION_RESPONSE: -3}"
RESPONSE_BODY="${EMISSION_RESPONSE%???}"

if [ "$HTTP_CODE" = "201" ]; then
    print_result 0 "Policy emission successful (HTTP $HTTP_CODE)"
    echo "Response: $RESPONSE_BODY"
    
    # Estrai policy number dalla risposta
    POLICY_NUMBER=$(echo "$RESPONSE_BODY" | grep -o '"policyNumber":"[^"]*"' | cut -d'"' -f4)
    echo "Policy Number: $POLICY_NUMBER"
else
    print_result 1 "Policy emission failed (HTTP $HTTP_CODE)"
fi

echo ""

# Test 3: Verifica Stato Iniziale
echo -e "${BLUE}3. Testing Initial Status...${NC}"
sleep 1
STATUS_RESPONSE=$(curl -s -w "%{http_code}" \
    -X GET "$BASE_URL/api/v3/emissions/$ORDER_CODE/status")

HTTP_CODE="${STATUS_RESPONSE: -3}"
RESPONSE_BODY="${STATUS_RESPONSE%???}"

if [ "$HTTP_CODE" = "200" ]; then
    print_result 0 "Status retrieval successful (HTTP $HTTP_CODE)"
    echo "Status: $RESPONSE_BODY"
    
    # Estrai stato dalla risposta
    STATE=$(echo "$RESPONSE_BODY" | grep -o '"state":"[^"]*"' | cut -d'"' -f4)
    echo "Current State: $STATE"
else
    print_result 1 "Status retrieval failed (HTTP $HTTP_CODE)"
fi

echo ""

# Test 4: Monitoraggio Progressione Stati
echo -e "${BLUE}4. Monitoring Status Progression...${NC}"
echo "Waiting for background tasks to complete..."

MAX_ATTEMPTS=20
ATTEMPT=0
FINAL_STATE=""

while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    sleep 1
    ATTEMPT=$((ATTEMPT + 1))
    
    STATUS_RESPONSE=$(curl -s "$BASE_URL/api/v3/emissions/$ORDER_CODE/status")
    STATE=$(echo "$STATUS_RESPONSE" | grep -o '"state":"[^"]*"' | cut -d'"' -f4)
    
    echo -e "${YELLOW}Attempt $ATTEMPT: State = $STATE${NC}"
    
    if [ "$STATE" = "COMPLETE" ] || [ "$STATE" = "FAILED" ]; then
        FINAL_STATE="$STATE"
        break
    fi
done

if [ "$FINAL_STATE" = "COMPLETE" ]; then
    print_result 0 "Background tasks completed successfully"
elif [ "$FINAL_STATE" = "FAILED" ]; then
    print_result 1 "Background tasks failed"
else
    echo -e "${YELLOW}⚠️  Background tasks still in progress after ${MAX_ATTEMPTS}s${NC}"
fi

echo ""

# Test 5: Test Errori
echo -e "${BLUE}5. Testing Error Scenarios...${NC}"

# Test senza token
echo "Testing missing authorization..."
curl -s -w "%{http_code}" \
    -X POST "$BASE_URL/api/v3/emissions/TEST_NO_AUTH" \
    -H "Content-Type: application/json" \
    -d '{}' | grep -q "401"
print_result $? "Missing authorization handled correctly"

# Test ordine inesistente per status
echo "Testing non-existent order status..."
curl -s -w "%{http_code}" \
    -X GET "$BASE_URL/api/v3/emissions/NON_EXISTENT_ORDER/status" | grep -q "404"
print_result $? "Non-existent order status handled correctly"

echo ""

# Test 6: Performance Test
echo -e "${BLUE}6. Testing Performance...${NC}"
echo "Measuring response time for emission endpoint..."

PERFORMANCE_ORDER="PERF_TEST_$(date +%s)"
START_TIME=$(date +%s%3N)

curl -s -w "%{http_code}" \
    -X POST "$BASE_URL/api/v3/emissions/$PERFORMANCE_ORDER" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{}' > /dev/null

END_TIME=$(date +%s%3N)
RESPONSE_TIME=$((END_TIME - START_TIME))

echo "Response time: ${RESPONSE_TIME}ms"

if [ $RESPONSE_TIME -lt 500 ]; then
    print_result 0 "Performance requirement met (< 500ms)"
else
    echo -e "${YELLOW}⚠️  Response time ${RESPONSE_TIME}ms exceeds 500ms target${NC}"
fi

echo ""

# Riepilogo
echo -e "${GREEN}🎉 API V3 Testing Complete!${NC}"
echo ""
echo "Summary:"
echo "- Order Code: $ORDER_CODE"
echo "- Policy Number: $POLICY_NUMBER"
echo "- Final State: $FINAL_STATE"
echo "- Response Time: ${RESPONSE_TIME}ms"
echo ""
echo "Next steps:"
echo "1. Check application logs for detailed execution flow"
echo "2. Monitor metrics in Grafana dashboard"
echo "3. Verify background task completion in status endpoint"
echo ""
echo "For more information, see: docs/API_V3_EMISSION_GUIDE.md"
