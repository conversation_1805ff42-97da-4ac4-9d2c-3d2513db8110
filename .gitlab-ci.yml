variables:
  BUILD_TEST_DOCKER_IMAGE: maven:3.8.6
  PREBUILD_COMMAND: mvn clean
  PRETEST_COMMAND: mvn clean
  BUILD_COMMAND: mvn package
  TEST_COMMAND: mvn verify
  DOCKERFILE_LOCATION: ./Dockerfile.jvm
include:
  - project: nyp/pipelines/reusable-pipelines
    ref: faster
    file: tag_hook.gitlab-ci.yml
  #- project: nyp/pipelines/reusable-pipelines
  #  ref: main
  #  file: commit_hook.gitlab-ci.yml
